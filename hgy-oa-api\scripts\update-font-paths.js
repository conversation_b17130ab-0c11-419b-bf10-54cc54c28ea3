import { query } from '../src/config/database.js';

const updateFontPaths = async () => {
  try {
    console.log('🚀 开始更新字体文件路径...\n');

    // 获取所有字体记录
    const fonts = await query('SELECT id, name, code, download_url FROM fonts WHERE is_system = 0');
    
    console.log(`📋 找到 ${fonts.length} 个本地字体需要更新路径`);

    let successCount = 0;
    let errorCount = 0;

    for (const font of fonts) {
      try {
        // 从当前路径提取文件名
        let fileName = '';
        if (font.download_url) {
          // 提取文件名（处理不同路径格式）
          fileName = font.download_url.split('/').pop();
        } else {
          // 根据字体名称推导文件名
          const nameMapping = {
            'fangzhengshusong': 'fangzhengshusong.ttf',
            'fangzhengfangsong': 'fangzhengfangsong.ttf',
            'fangzhengheiti': 'fangzhengheiti.ttf',
            'aa_houdihei': 'Aa厚底黑.ttf',
            'alimama_dongfangdakai': 'Alimama_DongFangDaKai_Regular.ttf',
            'dingtalk_jinbuti': 'DingTalk JinBuTi.ttf',
            'douyin_sans_bold': 'DouyinSansBold.ttf',
            'sourcehan_sans_bold': 'SourceHanSansCN-Bold.ttf',
            'sourcehan_sans_extralight': 'SourceHanSansCN-ExtraLight.ttf',
            'sourcehan_sans_heavy': 'SourceHanSansCN-Heavy.ttf',
            'sourcehan_sans_light': 'SourceHanSansCN-Light.ttf',
            'sourcehan_sans_medium': 'SourceHanSansCN-Medium.ttf',
            'sourcehan_sans_normal': 'SourceHanSansCN-Normal.ttf',
            'sourcehan_sans_regular': 'SourceHanSansCN-Regular.ttf',
            'sourcehan_serif_bold': 'SourceHanSerifCN-Bold.ttf',
            'sourcehan_serif_extralight': 'SourceHanSerifCN-ExtraLight.ttf',
            'sourcehan_serif_heavy': 'SourceHanSerifCN-Heavy.ttf',
            'sourcehan_serif_light': 'SourceHanSerifCN-Light.ttf',
            'sourcehan_serif_medium': 'SourceHanSerifCN-Medium.ttf',
            'sourcehan_serif_regular': 'SourceHanSerifCN-Regular.ttf',
            'sourcehan_serif_semibold': 'SourceHanSerifCN-SemiBold.ttf',
            'swis721_black': 'Swis721 Blk BT Black.ttf',
            'swisski': 'swisski.ttf',
            'youshe_biaotiyuan': '优设标题圆.ttf',
            'youshe_biaotihei': '优设标题黑.ttf',
            'alibaba_puhuiti_bold': '阿里巴巴普惠体-Bold.ttf',
            'alibaba_puhuiti_heavy': '阿里巴巴普惠体-Heavy.ttf',
            'alibaba_puhuiti_light': '阿里巴巴普惠体-Light.ttf',
            'alibaba_puhuiti_medium': '阿里巴巴普惠体-Medium.ttf',
            'alibaba_puhuiti_regular': '阿里巴巴普惠体-Regular.ttf'
          };
          
          fileName = nameMapping[font.code] || `${font.code}.ttf`;
        }

        // 新的下载路径（相对于public目录）
        const newDownloadUrl = `/fonts/${fileName}`;

        // 更新数据库记录
        await query(
          'UPDATE fonts SET download_url = ? WHERE id = ?',
          [newDownloadUrl, font.id]
        );

        console.log(`✅ 更新成功: ${font.name} -> ${newDownloadUrl}`);
        successCount++;
      } catch (error) {
        console.error(`❌ 更新失败: ${font.name} - ${error.message}`);
        errorCount++;
      }
    }

    console.log(`\n📊 更新完成统计:`);
    console.log(`   成功: ${successCount} 个字体`);
    console.log(`   失败: ${errorCount} 个字体`);

    // 显示更新后的字体列表
    console.log('\n📋 更新后的字体路径:');
    const updatedFonts = await query(`
      SELECT name, code, download_url 
      FROM fonts 
      WHERE is_system = 0 
      ORDER BY name ASC
    `);

    updatedFonts.forEach(font => {
      console.log(`   ${font.name} (${font.code}) -> ${font.download_url}`);
    });

    console.log('\n🎉 字体路径更新完成！');

  } catch (error) {
    console.error('❌ 更新过程中发生错误:', error);
  }
};

// 执行更新
updateFontPaths().then(() => {
  console.log('程序执行完成');
  process.exit(0);
}).catch(error => {
  console.error('程序执行失败:', error);
  process.exit(1);
});
