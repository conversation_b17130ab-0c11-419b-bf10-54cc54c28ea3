<template>
  <div class="login-container">
    <div class="login-box">
      <div class="login-header">
        <div class="logo">
          <img src="/logo.png" alt="红果设计" class="logo-img" />
          <h1>红果设计</h1>
        </div>
        <p class="subtitle">专业的在线设计平台</p>
      </div>

      <a-form
        :model="loginForm"
        :rules="loginRules"
        ref="loginFormRef"
        layout="vertical"
      >
        <a-form-item label="用户名" field="username">
          <a-input
            v-model="loginForm.username"
            placeholder="请输入用户名"
            size="large"
            allow-clear
          >
            <template #prefix>
              <icon-user />
            </template>
          </a-input>
        </a-form-item>

        <a-form-item label="密码" field="password">
          <a-input-password
            v-model="loginForm.password"
            placeholder="请输入密码"
            size="large"
            allow-clear
            @press-enter="handleLogin"
          >
            <template #prefix>
              <icon-lock />
            </template>
          </a-input-password>
        </a-form-item>

        <a-form-item>
          <a-button
            type="primary"
            size="large"
            long
            :loading="loading"
            @click="handleLogin"
          >
            登录
          </a-button>
        </a-form-item>
      </a-form>

      <div class="login-footer">
        <p>还没有账号？请联系管理员开通</p>
      </div>
    </div>

    <div class="login-bg">
      <div class="static-shapes">
        <div class="shape shape-1"></div>
        <div class="shape shape-2"></div>
        <div class="shape shape-3"></div>
        <div class="shape shape-4"></div>
        <div class="shape shape-5"></div>
        <div class="shape shape-6"></div>
        <div class="shape shape-7"></div>
        <div class="shape shape-8"></div>
        <div class="shape shape-9"></div>
        <div class="shape shape-10"></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { Message } from '@arco-design/web-vue'
import { IconUser, IconLock } from '@arco-design/web-vue/es/icon'

const router = useRouter()
const loading = ref(false)

// 表单数据
const loginForm = reactive({
  username: '',
  password: ''
})

// 表单验证规则
const loginRules = {
  username: [
    { required: true, message: '请输入用户名' }
  ],
  password: [
    { required: true, message: '请输入密码' }
  ]
}

const loginFormRef = ref()

// 登录处理
const handleLogin = async () => {
  console.log('登录按钮被点击')
  console.log('表单数据:', loginForm)

  // 简单验证
  if (!loginForm.username) {
    Message.error('请输入用户名')
    return
  }

  if (!loginForm.password) {
    Message.error('请输入密码')
    return
  }

  loading.value = true
  console.log('开始登录流程')

  try {
    // 调用登录API
    const { getEndpointUrl } = await import('../../config/api')
    const response = await fetch(getEndpointUrl('LOGIN'), {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        username: loginForm.username,
        password: loginForm.password
      })
    })

    const result = await response.json()

    if (result.success) {
      // 保存用户信息到localStorage
      localStorage.setItem('userInfo', JSON.stringify(result.data))
      localStorage.setItem('isLoggedIn', 'true')

      Message.success('登录成功')

      // 跳转到首页
      router.push('/index')
    } else {
      Message.error(result.msg || '登录失败')
    }
  } catch (error) {
    console.error('登录失败:', error)
    Message.error('登录失败，请检查网络连接')
  } finally {
    loading.value = false
  }
}




</script>

<style scoped>
.login-container {
  position: relative;
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.login-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  z-index: 1;
}



.static-shapes {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 1;
}

.shape {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.08);
}

/* 随机大小和位置的静态球体 */
.shape-1 {
  width: 120px;
  height: 120px;
  top: 8%;
  left: 12%;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.08), rgba(120, 119, 198, 0.15));
}

.shape-2 {
  width: 80px;
  height: 80px;
  top: 65%;
  right: 18%;
  background: linear-gradient(45deg, rgba(255, 119, 198, 0.08), rgba(255, 255, 255, 0.08));
}

.shape-3 {
  width: 45px;
  height: 45px;
  bottom: 25%;
  left: 25%;
  background: linear-gradient(45deg, rgba(120, 219, 255, 0.08), rgba(255, 255, 255, 0.08));
}

.shape-4 {
  width: 95px;
  height: 95px;
  top: 35%;
  right: 8%;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.08), rgba(120, 219, 255, 0.15));
}

.shape-5 {
  width: 35px;
  height: 35px;
  bottom: 45%;
  right: 35%;
  background: linear-gradient(45deg, rgba(255, 119, 198, 0.15), rgba(255, 255, 255, 0.08));
}

.shape-6 {
  width: 65px;
  height: 65px;
  top: 15%;
  right: 45%;
  background: linear-gradient(45deg, rgba(120, 119, 198, 0.08), rgba(255, 255, 255, 0.08));
}

.shape-7 {
  width: 25px;
  height: 25px;
  top: 75%;
  left: 8%;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.08), rgba(255, 119, 198, 0.08));
}

.shape-8 {
  width: 110px;
  height: 110px;
  bottom: 8%;
  right: 55%;
  background: linear-gradient(45deg, rgba(120, 219, 255, 0.08), rgba(255, 255, 255, 0.08));
}

.shape-9 {
  width: 55px;
  height: 55px;
  top: 50%;
  left: 5%;
  background: linear-gradient(45deg, rgba(255, 119, 198, 0.08), rgba(120, 119, 198, 0.08));
}

.shape-10 {
  width: 30px;
  height: 30px;
  bottom: 60%;
  left: 45%;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.08), rgba(120, 219, 255, 0.08));
}



.login-box {
  position: relative;
  z-index: 2;
  width: 420px;
  padding: 48px;
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  box-shadow:
    0 25px 50px rgba(0, 0, 0, 0.15),
    0 0 0 1px rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
}

.login-box:hover {
  transform: translateY(-2px);
  box-shadow:
    0 30px 60px rgba(0, 0, 0, 0.2),
    0 0 0 1px rgba(255, 255, 255, 0.3);
}

.login-header {
  text-align: center;
  margin-bottom: 32px;
}

.logo {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  margin-bottom: 8px;
}

.logo-img {
  width: 40px;
  height: 40px;
  border-radius: 8px;
}

.logo h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #1d2129;
}

.subtitle {
  margin: 0;
  color: #86909c;
  font-size: 14px;
}

.login-footer {
  text-align: center;
  margin-top: 24px;
}

.login-footer p {
  margin: 0;
  color: #86909c;
  font-size: 14px;
}

:deep(.arco-form-item-label) {
  font-weight: 500;
  color: #1d2129;
}

:deep(.arco-input-wrapper) {
  border-radius: 8px;
}

:deep(.arco-btn-primary) {
  border-radius: 8px;
  font-weight: 500;
}
</style>
