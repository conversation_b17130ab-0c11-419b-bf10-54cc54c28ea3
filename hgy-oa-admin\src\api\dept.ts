import { request } from '@/utils/request'

// 部门相关接口类型定义
export interface Department {
  id: number
  name: string
  code: string
  description?: string
  manager_id?: number
  parent_id?: number
  sort_order: number
  status: 0 | 1
  is_active: 0 | 1
  created_at: string
  updated_at: string
  manager_name?: string
  manager_nickname?: string
  parent_name?: string
  user_count?: number
}

export interface CreateDepartmentParams {
  name: string
  code: string
  description?: string
  manager_id?: number
  parent_id?: number
  sort_order?: number
  status?: 0 | 1
}

export interface UpdateDepartmentParams {
  name: string
  code: string
  description?: string
  manager_id?: number
  parent_id?: number
  sort_order?: number
  status?: 0 | 1
}

export interface DepartmentListParams {
  pageNum?: number
  pageSize?: number
  keyword?: string
  status?: string | number
}

export interface DepartmentListResponse {
  records: Department[]
  total: number
  pageNum: number
  pageSize: number
  totalPages: number
}

// 部门管理API
export const getDepartmentList = (params: DepartmentListParams) => {
  return request.get<DepartmentListResponse>('/api/department/list', { params })
}

export const getAllDepartments = () => {
  return request.get<Department[]>('/api/department/all')
}

export const getDepartmentDetail = (id: number) => {
  return request.get<Department>(`/api/department/${id}`)
}

export const createDepartment = (data: CreateDepartmentParams) => {
  return request.post<{ id: number }>('/api/department', data)
}

export const updateDepartment = (id: number, data: UpdateDepartmentParams) => {
  return request.put('/api/department/' + id, data)
}

export const deleteDepartment = (id: number) => {
  return request.delete(`/api/department/${id}`)
}

export const batchDeleteDepartments = (ids: number[]) => {
  return request.post('/api/department/batch-delete', { ids })
}
