import express from 'express';
import { query } from '../config/database.js';

const router = express.Router();

// 获取图片分类列表（树形结构）
router.get('/images/categories', async (req, res) => {
  try {
    // 获取所有分类
    const categories = await query(`
      SELECT id, name, parent_id, icon, sort_order, description, is_active, created_at
      FROM display_image_categories
      ORDER BY parent_id ASC, sort_order ASC, id ASC
    `);

    // 构建树形结构
    const categoryMap = new Map();
    const rootCategories = [];

    // 先创建所有分类的映射
    categories.forEach(category => {
      categoryMap.set(category.id, { ...category, children: [] });
    });

    // 构建父子关系
    categories.forEach(category => {
      if (category.parent_id === null) {
        rootCategories.push(categoryMap.get(category.id));
      } else {
        const parent = categoryMap.get(category.parent_id);
        if (parent) {
          parent.children.push(categoryMap.get(category.id));
        }
      }
    });

    res.success(rootCategories, '获取分类列表成功');
  } catch (error) {
    console.error('获取分类列表失败:', error);
    res.error('获取分类列表失败', 500);
  }
});

// 获取图片列表
router.get('/images/list', async (req, res) => {
  try {
    const {
      pageNum = 1,
      pageSize = 20,
      keyword = '',
      categoryId = ''
    } = req.query;

    // 基础查询
    let sql = `
      SELECT DISTINCT
        gi.id, gi.title, gi.filename, gi.original_name, gi.file_path, gi.thumbnail_path,
        gi.file_size, gi.width, gi.height, gi.format, gi.description,
        gi.download_count, gi.view_count, gi.is_public, gi.is_featured,
        gi.created_at, gi.updated_at, gi.upload_user_id,
        u.nickname as upload_user_nickname, u.username as upload_user
      FROM display_images gi
      LEFT JOIN system_users u ON gi.upload_user_id = u.id
      WHERE gi.is_public = 1 AND gi.deleted_at IS NULL
    `;

    const params = [];

    // 关键词搜索
    if (keyword) {
      sql += ` AND gi.title LIKE ?`;
      params.push(`%${keyword}%`);
    }

    // 分类筛选
    if (categoryId) {
      // 这里需要根据实际的分类关联表来调整
      sql += ` AND gi.primary_category_id = ?`;
      params.push(categoryId);
    }

    // 分页
    const limit = parseInt(pageSize);
    const offset = (parseInt(pageNum) - 1) * limit;
    sql += ` ORDER BY gi.created_at DESC LIMIT ${limit} OFFSET ${offset}`;

    const images = await query(sql, params);

    // 获取总数
    let countSql = `
      SELECT COUNT(*) as total
      FROM display_images gi
      WHERE gi.is_public = 1 AND gi.deleted_at IS NULL
    `;
    const countParams = [];

    if (keyword) {
      countSql += ` AND gi.title LIKE ?`;
      countParams.push(`%${keyword}%`);
    }

    if (categoryId) {
      countSql += ` AND gi.primary_category_id = ?`;
      countParams.push(categoryId);
    }

    const totalResult = await query(countSql, countParams);
    const total = totalResult[0].total;

    res.success({
      records: images,
      total,
      pageNum: parseInt(pageNum),
      pageSize: parseInt(pageSize)
    }, '获取图片列表成功');

  } catch (error) {
    console.error('获取图片列表失败:', error);
    res.error('获取图片列表失败', 500);
  }
});

// 获取套件分类列表（树形结构）
router.get('/suites', async (req, res) => {
  try {
    // 获取所有套件分类
    const categories = await query(`
      SELECT 
        id, name, description, parent_id, sort_order, is_visible
      FROM display_suits
      WHERE deleted_at IS NULL AND is_visible = 1
      ORDER BY sort_order ASC, created_at ASC
    `);

    // 构建树形结构
    const categoryMap = new Map();
    const rootCategories = [];

    // 先创建所有分类的映射
    categories.forEach(category => {
      categoryMap.set(category.id, { ...category, children: [] });
    });

    // 构建父子关系
    categories.forEach(category => {
      if (category.parent_id === null) {
        rootCategories.push(categoryMap.get(category.id));
      } else {
        const parent = categoryMap.get(category.parent_id);
        if (parent) {
          parent.children.push(categoryMap.get(category.id));
        }
      }
    });

    res.success(rootCategories, '获取套件分类树成功');
  } catch (error) {
    console.error('获取套件分类树失败:', error);
    res.error('获取套件分类树失败', 500);
  }
});

// 获取文案列表
router.get('/documents', async (req, res) => {
  try {
    const {
      pageNum = 1,
      pageSize = 10,
      keyword = ''
    } = req.query;

    let sql = `
      SELECT
        id,
        title,
        filename,
        file_type,
        file_size,
        file_path,
        qiniu_key,
        remark,
        description,
        is_public,
        upload_user,
        upload_user as upload_user_nickname,
        created_at,
        updated_at
      FROM display_documents
      WHERE deleted_at IS NULL
    `;

    const params = [];

    // 关键词搜索
    if (keyword) {
      sql += ` AND title LIKE ?`;
      params.push(`%${keyword}%`);
    }

    // 分页
    const limit = parseInt(pageSize);
    const offset = (parseInt(pageNum) - 1) * limit;
    sql += ` ORDER BY created_at DESC LIMIT ${limit} OFFSET ${offset}`;

    const documents = await query(sql, params);

    // 获取总数
    let countSql = `
      SELECT COUNT(*) as total
      FROM display_documents
      WHERE deleted_at IS NULL
    `;
    const countParams = [];

    if (keyword) {
      countSql += ` AND title LIKE ?`;
      countParams.push(`%${keyword}%`);
    }

    const totalResult = await query(countSql, countParams);
    const total = totalResult[0].total;

    res.success({
      records: documents,
      total,
      pageNum: parseInt(pageNum),
      pageSize: parseInt(pageSize)
    }, '获取文案列表成功');

  } catch (error) {
    console.error('获取文案列表失败:', error);
    res.error('获取文案列表失败', 500);
  }
});

export default router;
