import qiniu from 'qiniu';
import path from 'path';
import crypto from 'crypto';
import { v4 as uuidv4 } from 'uuid';

// 七牛云配置
const qiniuConfig = {
  accessKey: 'XY7NulclFTE8ff15ORUTq-zRFhrfnSOxwnrGF6e7',
  secretKey: '3Pqaeoh1JmmSdhfpONLE3LcH6HJsl3Y2vl8lT-0D',
  bucket: '3d-app',
  domain: 'https://3d-app-qiniu.dlmu.cc',
  zone: qiniu.zone.Zone_z0, // 华东区域
  pathPrefix: 'image-design/' // 路径前缀
};

// 创建上传凭证
export function getUploadToken(key = null) {
  const mac = new qiniu.auth.digest.Mac(qiniuConfig.accessKey, qiniuConfig.secretKey);
  
  const options = {
    scope: key ? `${qiniuConfig.bucket}:${key}` : qiniuConfig.bucket,
    expires: 7200, // 2小时有效期
    returnBody: JSON.stringify({
      key: '$(key)',
      hash: '$(etag)',
      fsize: '$(fsize)',
      bucket: '$(bucket)',
      mimeType: '$(mimeType)',
      width: '$(imageInfo.width)',
      height: '$(imageInfo.height)'
    })
  };
  
  const putPolicy = new qiniu.rs.PutPolicy(options);
  return putPolicy.uploadToken(mac);
}

// 生成文件key
export function generateFileKey(originalName, category = 'images') {
  const ext = path.extname(originalName);
  const timestamp = Date.now();
  const uuid = uuidv4().replace(/-/g, '');
  // 确保路径格式正确，不以斜杠开头
  const key = `${qiniuConfig.pathPrefix}${category}/${timestamp}_${uuid}${ext}`;
  return key;
}

// 获取文件完整URL（公开访问）
export function getFileUrl(key) {
  // 确保key不以斜杠开头，避免双斜杠
  const cleanKey = key.startsWith('/') ? key.substring(1) : key;
  const url = `${qiniuConfig.domain}/${cleanKey}`;
  return url;
}

// 手动实现七牛云私有URL签名
function generatePrivateUrlSignature(url, deadline, secretKey) {

  // 构建签名字符串
  const signStr = `${url}?e=${deadline}`;

  // 使用HMAC-SHA1生成签名
  const hmac = crypto.createHmac('sha1', secretKey);
  hmac.update(signStr);
  const signature = hmac.digest('base64');

  // URL安全的base64编码
  const urlSafeSignature = signature
    .replace(/\+/g, '-')
    .replace(/\//g, '_');

  return urlSafeSignature;
}

// 获取私有文件的下载URL（带签名）
export function getPrivateFileUrl(key, expires = 3600) {
  try {
    // 确保key不以斜杠开头
    const cleanKey = key.startsWith('/') ? key.substring(1) : key;

    // 生成私有空间的下载链接
    const publicUrl = `${qiniuConfig.domain}/${cleanKey}`;

    // 计算过期时间戳（当前时间 + 有效期秒数）
    const deadline = Math.floor(Date.now() / 1000) + expires;

    // 手动生成签名
    const signature = generatePrivateUrlSignature(publicUrl, deadline, qiniuConfig.secretKey);

    // 构建最终的私有URL
    const privateUrl = `${publicUrl}?e=${deadline}&token=${qiniuConfig.accessKey}:${signature}`;

    return privateUrl;
  } catch (error) {
    throw error;
  }
}

// 上传文件到七牛云
export function uploadToQiniu(fileBuffer, key, originalName) {
  return new Promise((resolve, reject) => {
    const mac = new qiniu.auth.digest.Mac(qiniuConfig.accessKey, qiniuConfig.secretKey);
    const config = new qiniu.conf.Config();
    config.zone = qiniuConfig.zone;
    config.useHttpsDomain = false;
    config.useCdnDomain = false;

    const formUploader = new qiniu.form_up.FormUploader(config);
    const putExtra = new qiniu.form_up.PutExtra();

    const uploadToken = getUploadToken(key);

    formUploader.put(uploadToken, key, fileBuffer, putExtra, (respErr, respBody, respInfo) => {
      if (respErr) {
        reject(respErr);
        return;
      }

      if (respInfo.statusCode === 200) {
        const result = {
          key: respBody.key,
          hash: respBody.hash,
          url: getFileUrl(respBody.key),
          size: respBody.fsize,
          mimeType: respBody.mimeType,
          width: respBody.width || null,
          height: respBody.height || null
        };
        resolve(result);
      } else {
        const error = new Error(`上传失败: ${respInfo.statusCode}`);
        reject(error);
      }
    });
  });
}

// 删除七牛云文件
export function deleteFromQiniu(key) {
  return new Promise((resolve, reject) => {
    const mac = new qiniu.auth.digest.Mac(qiniuConfig.accessKey, qiniuConfig.secretKey);
    const config = new qiniu.conf.Config();
    config.zone = qiniuConfig.zone;
    
    const bucketManager = new qiniu.rs.BucketManager(mac, config);
    
    bucketManager.delete(qiniuConfig.bucket, key, (err, respBody, respInfo) => {
      if (err) {
        reject(err);
        return;
      }
      
      if (respInfo.statusCode === 200) {
        resolve(true);
      } else {
        reject(new Error(`删除失败: ${respInfo.statusCode}`));
      }
    });
  });
}

export default {
  getUploadToken,
  generateFileKey,
  getFileUrl,
  uploadToQiniu,
  deleteFromQiniu,
  config: qiniuConfig
};
