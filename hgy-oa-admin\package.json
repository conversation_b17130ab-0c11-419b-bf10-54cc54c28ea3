{"name": "gzm-design", "private": true, "version": "0.0.0", "type": "module", "engines": {"node": ">=22.12.0", "pnpm": ">=8.0.0"}, "scripts": {"dev": "vite --mode development", "dev:local": "vite --mode development", "dev:test": "vite --mode test", "build": "vite build", "build:test": "vite build --mode test", "preview": "vite preview"}, "dependencies": {"@leafer-in/arrow": "^1.3.1", "@leafer-in/editor": "^1.3.1", "@leafer-in/export": "^1.3.1", "@leafer-in/find": "^1.3.1", "@leafer-in/flow": "^1.3.1", "@leafer-in/html": "^1.3.1", "@leafer-in/scroll": "^1.3.1", "@leafer-in/state": "^1.3.1", "@leafer-in/text-editor": "^1.3.1", "@leafer-in/view": "^1.3.1", "@leafer-in/viewport": "^1.3.1", "@leafer-ui/core": "^1.3.1", "@leafer-ui/interface": "^1.3.1", "@tinymce/tinymce-vue": "^5.1.1", "@unocss/reset": "^0.57.7", "@vueuse/core": "^10.11.1", "ag-psd": "^20.2.3", "axios": "^1.7.9", "convert-units": "3.0.0-beta.6", "fontfaceobserver": "^2.3.0", "jsbarcode": "^3.11.6", "leafer-ui": "^1.3.1", "leafer-x-ruler": "^1.0.14", "lodash": "^4.17.21", "mousetrap": "^1.6.5", "number-precision": "^1.6.0", "pinia": "^2.3.0", "pinyin-pro": "^3.26.0", "qrcode": "^1.5.4", "tinycolor2": "^1.6.0", "tinymce": "^6.8.5", "uuid": "^9.0.1", "vue": "^3.5.13", "vue-router": "^4.5.0"}, "devDependencies": {"@arco-design/web-vue": "^2.56.3", "@types/fontfaceobserver": "^2.1.3", "@types/lodash": "^4.17.14", "@types/mockjs": "^1.0.10", "@types/mousetrap": "^1.6.15", "@types/node": "^20.17.12", "@types/qrcode": "^1.5.5", "@types/tinycolor2": "^1.4.6", "@types/uuid": "^9.0.8", "@vitejs/plugin-vue": "^4.6.2", "less": "^4.2.1", "mockjs": "^1.1.0", "typescript": "^5.7.2", "unocss": "^0.53.6", "unplugin-auto-import": "^0.16.7", "unplugin-vue-components": "^0.25.2", "vite": "^4.5.5", "vite-plugin-svg-icons": "^2.0.1", "vue-tsc": "^1.8.27"}}