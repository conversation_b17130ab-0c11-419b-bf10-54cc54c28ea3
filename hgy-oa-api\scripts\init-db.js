import { query, testConnection } from '../src/config/database.js';

// 创建数据库表的SQL语句
const createTables = async () => {
  console.log('开始创建数据库表...');

  const tables = [
    // 1. 模板表
    `CREATE TABLE IF NOT EXISTS templates (
      id int(11) NOT NULL AUTO_INCREMENT,
      cover varchar(500) DEFAULT NULL COMMENT '封面图片URL',
      title varchar(255) NOT NULL COMMENT '模板标题',
      json longtext NOT NULL COMMENT '模板JSON数据',
      state tinyint(1) DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
      created_at timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
      updated_at timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
      PRIMARY KEY (id),
      KEY idx_state (state),
      KEY idx_created_at (created_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设计模板表'`,

    // 2. 文字素材表
    `CREATE TABLE IF NOT EXISTS text_materials (
      id int(11) NOT NULL AUTO_INCREMENT,
      title varchar(255) NOT NULL COMMENT '素材标题',
      content text NOT NULL COMMENT '文字内容',
      preview_url varchar(500) DEFAULT NULL COMMENT '预览图URL',
      category_id int(11) DEFAULT NULL COMMENT '分类ID',
      tags json DEFAULT NULL COMMENT '标签',
      status tinyint(1) DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
      created_at timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
      updated_at timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
      PRIMARY KEY (id),
      KEY idx_category_id (category_id),
      KEY idx_status (status),
      KEY idx_created_at (created_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文字素材表'`,

    // 3. 图片素材表
    `CREATE TABLE IF NOT EXISTS image_materials (
      id int(11) NOT NULL AUTO_INCREMENT,
      title varchar(255) NOT NULL COMMENT '素材标题',
      content varchar(500) NOT NULL COMMENT '图片URL',
      preview_url varchar(500) DEFAULT NULL COMMENT '预览图URL',
      category_id int(11) DEFAULT NULL COMMENT '分类ID',
      tags json DEFAULT NULL COMMENT '标签',
      width int(11) DEFAULT NULL COMMENT '图片宽度',
      height int(11) DEFAULT NULL COMMENT '图片高度',
      file_size int(11) DEFAULT NULL COMMENT '文件大小(字节)',
      status tinyint(1) DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
      created_at timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
      updated_at timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
      PRIMARY KEY (id),
      KEY idx_category_id (category_id),
      KEY idx_status (status),
      KEY idx_created_at (created_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='图片素材表'`,

    // 4. 字体表
    `CREATE TABLE IF NOT EXISTS fonts (
      id int(11) NOT NULL AUTO_INCREMENT,
      name varchar(255) NOT NULL COMMENT '字体名称',
      code varchar(255) NOT NULL COMMENT '字体代码',
      preview_url varchar(500) DEFAULT NULL COMMENT '预览图URL',
      download_url varchar(500) DEFAULT NULL COMMENT '下载URL',
      file_size int(11) DEFAULT NULL COMMENT '文件大小(字节)',
      font_family varchar(255) NOT NULL COMMENT '字体族名',
      font_weight varchar(50) DEFAULT 'normal' COMMENT '字体粗细',
      font_style varchar(50) DEFAULT 'normal' COMMENT '字体样式',
      category_id int(11) DEFAULT NULL COMMENT '分类ID',
      tags json DEFAULT NULL COMMENT '标签',
      is_system tinyint(1) DEFAULT 0 COMMENT '是否系统字体：1-是，0-否',
      status tinyint(1) DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
      created_at timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
      updated_at timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
      PRIMARY KEY (id),
      UNIQUE KEY uk_code (code),
      KEY idx_category_id (category_id),
      KEY idx_is_system (is_system),
      KEY idx_status (status),
      KEY idx_created_at (created_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='字体表'`,

    // 5. 图形分类表
    `CREATE TABLE IF NOT EXISTS graph_categories (
      id int(11) NOT NULL AUTO_INCREMENT,
      name varchar(255) NOT NULL COMMENT '分类名称',
      icon varchar(500) DEFAULT NULL COMMENT '分类图标URL',
      sort_order int(11) DEFAULT 0 COMMENT '排序',
      status tinyint(1) DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
      created_at timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
      updated_at timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
      PRIMARY KEY (id),
      KEY idx_sort_order (sort_order),
      KEY idx_status (status)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='图形分类表'`,

    // 6. 图形表
    `CREATE TABLE IF NOT EXISTS graphs (
      id int(11) NOT NULL AUTO_INCREMENT,
      name varchar(255) NOT NULL COMMENT '图形名称',
      preview_url varchar(500) DEFAULT NULL COMMENT '预览图URL',
      svg_content longtext NOT NULL COMMENT 'SVG内容',
      category_id int(11) DEFAULT NULL COMMENT '分类ID',
      tags json DEFAULT NULL COMMENT '标签',
      width int(11) DEFAULT NULL COMMENT '图形宽度',
      height int(11) DEFAULT NULL COMMENT '图形高度',
      status tinyint(1) DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
      created_at timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
      updated_at timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
      PRIMARY KEY (id),
      KEY idx_category_id (category_id),
      KEY idx_status (status),
      KEY idx_created_at (created_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='图形表'`,

    // 7. 元素分类表
    `CREATE TABLE IF NOT EXISTS element_categories (
      id int(11) NOT NULL AUTO_INCREMENT,
      name varchar(255) NOT NULL COMMENT '分类名称',
      icon varchar(500) DEFAULT NULL COMMENT '分类图标URL',
      sort_order int(11) DEFAULT 0 COMMENT '排序',
      status tinyint(1) DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
      created_at timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
      updated_at timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
      PRIMARY KEY (id),
      KEY idx_sort_order (sort_order),
      KEY idx_status (status)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='元素分类表'`,

    // 8. 元素表
    `CREATE TABLE IF NOT EXISTS elements (
      id int(11) NOT NULL AUTO_INCREMENT,
      name varchar(255) NOT NULL COMMENT '元素名称',
      preview_url varchar(500) DEFAULT NULL COMMENT '预览图URL',
      content_url varchar(500) NOT NULL COMMENT '元素内容URL',
      element_type varchar(50) NOT NULL COMMENT '元素类型：image,svg,icon等',
      category_id int(11) DEFAULT NULL COMMENT '分类ID',
      tags json DEFAULT NULL COMMENT '标签',
      width int(11) DEFAULT NULL COMMENT '元素宽度',
      height int(11) DEFAULT NULL COMMENT '元素高度',
      file_size int(11) DEFAULT NULL COMMENT '文件大小(字节)',
      status tinyint(1) DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
      created_at timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
      updated_at timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
      PRIMARY KEY (id),
      KEY idx_category_id (category_id),
      KEY idx_element_type (element_type),
      KEY idx_status (status),
      KEY idx_created_at (created_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='元素表'`,

    // 9. 背景分类表
    `CREATE TABLE IF NOT EXISTS background_categories (
      id int(11) NOT NULL AUTO_INCREMENT,
      name varchar(255) NOT NULL COMMENT '分类名称',
      icon varchar(500) DEFAULT NULL COMMENT '分类图标URL',
      sort_order int(11) DEFAULT 0 COMMENT '排序',
      status tinyint(1) DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
      created_at timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
      updated_at timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
      PRIMARY KEY (id),
      KEY idx_sort_order (sort_order),
      KEY idx_status (status)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='背景分类表'`,

    // 10. 背景图片表
    `CREATE TABLE IF NOT EXISTS background_images (
      id int(11) NOT NULL AUTO_INCREMENT,
      name varchar(255) NOT NULL COMMENT '图片名称',
      preview_url varchar(500) DEFAULT NULL COMMENT '预览图URL',
      original_url varchar(500) NOT NULL COMMENT '原图URL',
      thumbnail_url varchar(500) DEFAULT NULL COMMENT '缩略图URL',
      category_id int(11) DEFAULT NULL COMMENT '分类ID',
      tags json DEFAULT NULL COMMENT '标签',
      width int(11) DEFAULT NULL COMMENT '图片宽度',
      height int(11) DEFAULT NULL COMMENT '图片高度',
      file_size int(11) DEFAULT NULL COMMENT '文件大小(字节)',
      color_palette json DEFAULT NULL COMMENT '颜色调色板',
      status tinyint(1) DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
      created_at timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
      updated_at timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
      PRIMARY KEY (id),
      KEY idx_category_id (category_id),
      KEY idx_status (status),
      KEY idx_created_at (created_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='背景图片表'`
  ];

  let successCount = 0;
  let errorCount = 0;

  for (let i = 0; i < tables.length; i++) {
    try {
      await query(tables[i]);
      console.log(`✅ 表 ${i + 1} 创建成功`);
      successCount++;
    } catch (error) {
      console.error(`❌ 表 ${i + 1} 创建失败:`, error.message);
      errorCount++;
    }
  }

  console.log(`\n📊 表创建结果: 成功 ${successCount} 个, 失败 ${errorCount} 个`);
  return errorCount === 0;
};

// 插入默认数据
const insertDefaultData = async () => {
  console.log('\n开始插入默认数据...');

  try {
    // 插入默认字体
    const defaultFonts = [
      ['Arial', 'arial', 'Arial, sans-serif', 1],
      ['Times New Roman', 'times-new-roman', 'Times New Roman, serif', 1],
      ['微软雅黑', 'microsoft-yahei', 'Microsoft Yahei, sans-serif', 1],
      ['宋体', 'simsun', 'SimSun, serif', 1],
      ['黑体', 'simhei', 'SimHei, sans-serif', 1]
    ];

    for (const font of defaultFonts) {
      const sql = `
        INSERT IGNORE INTO fonts (name, code, font_family, is_system, status, created_at, updated_at)
        VALUES (?, ?, ?, ?, 1, NOW(), NOW())
      `;
      await query(sql, font);
    }

    // 插入默认分类
    const categories = [
      ['graph_categories', '基础图形'],
      ['graph_categories', '装饰图形'],
      ['element_categories', '图标'],
      ['element_categories', '装饰元素'],
      ['background_categories', '纯色背景'],
      ['background_categories', '渐变背景']
    ];

    for (const [table, name] of categories) {
      const sql = `
        INSERT IGNORE INTO ${table} (name, sort_order, status, created_at, updated_at)
        VALUES (?, 0, 1, NOW(), NOW())
      `;
      await query(sql, [name]);
    }

    console.log('✅ 默认数据插入成功');
  } catch (error) {
    console.error('❌ 插入默认数据失败:', error.message);
  }
};

// 主函数
const initDatabase = async () => {
  console.log('🚀 开始初始化数据库...\n');

  // 测试数据库连接
  const connected = await testConnection();
  if (!connected) {
    console.error('❌ 数据库连接失败，请检查配置');
    process.exit(1);
  }

  // 创建表
  const tablesCreated = await createTables();
  if (!tablesCreated) {
    console.error('❌ 部分表创建失败');
  }

  // 插入默认数据
  await insertDefaultData();

  console.log('\n🎉 数据库初始化完成！');
};

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  console.log('脚本开始执行...');
  initDatabase().catch(error => {
    console.error('初始化过程中发生错误:', error);
    process.exit(1);
  });
}

export default initDatabase;
