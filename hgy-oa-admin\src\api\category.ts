import { request } from '@/utils/request'

// 分类相关接口类型定义
export interface Category {
  id: number
  name: string
  parent_id?: number
  icon?: string
  sort_order: number
  description?: string
  is_active: number
  created_at: string
  children?: Category[]
}

export interface CreateCategoryParams {
  name: string
  parent_id?: number
  icon?: string
  sort_order?: number
  description?: string
  is_active?: number
}

export interface UpdateCategoryParams {
  name: string
  parent_id?: number
  icon?: string
  sort_order?: number
  description?: string
  is_active?: number
}

// 获取分类列表（树形结构）
export const getCategoryTree = () => {
  return request.get('/api/gallery/categories')
}

// 获取一级分类列表
export const getPrimaryCategories = () => {
  return request.get('/api/gallery/categories/primary')
}

// 获取二级分类列表
export const getSecondaryCategories = (parentId: number) => {
  return request.get(`/api/gallery/categories/secondary/${parentId}`)
}

// 创建分类
export const createCategory = (data: CreateCategoryParams) => {
  return request.post('/api/gallery/categories', data)
}

// 更新分类
export const updateCategory = (id: number, data: UpdateCategoryParams) => {
  return request.put(`/api/gallery/categories/${id}`, data)
}

// 删除分类
export const deleteCategory = (id: number) => {
  return request.delete(`/api/gallery/categories/${id}`)
}
