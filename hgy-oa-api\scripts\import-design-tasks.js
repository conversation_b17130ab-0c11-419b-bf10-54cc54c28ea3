import { query } from '../src/config/database.js';
import XLSX from 'xlsx';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 简单的拼音转换映射（常用汉字）
const pinyinMap = {
  '杨': 'yang', '同': 'tong', '杰': 'jie',
  '邓': 'deng', '静': 'jing', '雅': 'ya',
  '刘': 'liu', '洪': 'hong', '廷': 'ting',
  '炜': 'wei', '祺': 'qi',
  '程': 'cheng', '梅': 'mei', '薇': 'wei',
  '袁': 'yuan', '思': 'si', '维': 'wei',
  '樊': 'fan', '志': 'zhi', '亮': 'liang',
  '胡': 'hu', '柯': 'ke', '鑫': 'xin',
  '游': 'you', '佳': 'jia', '豪': 'hao',
  '高': 'gao', '鹏': 'peng', '智': 'zhi',
  '烈': 'lie', '娟': 'juan',
  '吴': 'wu', '晨': 'chen',
  '张': 'zhang', '少': 'shao', '成': 'cheng',
  '梦': 'meng', '萱': 'xuan',
  '咏': 'yong', '琪': 'qi',
  '黄': 'huang', '安': 'an',
  '何': 'he', '希': 'xi',
  '徐': 'xu', '乐': 'le', '莹': 'ying',
  '洁': 'jie', '斌': 'bin',
  '孙': 'sun', '陈': 'chen', '樟': 'zhang',
  '文': 'wen',
  '余': 'yu', '湘': 'xiang', '晴': 'qing',
  '蔡': 'cai', '恒': 'heng', '林': 'lin',
  '王': 'wang', '叶': 'ye', '红': 'hong',
  '西': 'xi', '柚': 'you', '子': 'zi',
  '书': 'shu', '玉': 'yu',
  '小': 'xiao', '强': 'qiang',
  '赵': 'zhao', '楚': 'chu',
  '谢': 'xie', '远': 'yuan',
  '雅': 'ya', '婕': 'jie',
  '滕': 'teng', '淑': 'shu', '慧': 'hui',
  '未': 'wei', '江': 'jiang', '华': 'hua',
  '笑': 'xiao',
  '方': 'fang', '源': 'yuan',
  '魏': 'wei', '汉': 'han', '霞': 'xia',
  '雪': 'xue', '纯': 'chun',
  '温': 'wen', '世': 'shi', '仙': 'xian',
  '宁': 'ning',
  '阳': 'yang', '浩': 'hao',
  '启': 'qi', '扬': 'yang',
  '彭': 'peng', '录': 'lu',
  '海': 'hai', '燕': 'yan',
  '裘': 'qiu', '旦': 'dan',
  '俊': 'jun',
  '汪': 'wang',
  '娴': 'xian',
  '李': 'li', '建': 'jian', '峰': 'feng',
  '韩': 'han', '美': 'mei',
  '屈': 'qu', '帅': 'shuai',
  '辉': 'hui',
  '朱': 'zhu', '涵': 'han',
  '兰': 'lan',
  '婷': 'ting',
  '曾': 'zeng', '迎': 'ying', '春': 'chun',
  '润': 'run',
  '邵': 'shao',
  '寒': 'han',
  '琳': 'lin',
  '贺': 'he', '蓝': 'lan',
  '颜': 'yan', '旸': 'yang',
  '勇': 'yong',
  '万': 'wan', '锋': 'feng',
  '泽': 'ze',
  '阎': 'yan', '莉': 'li',
  '欣': 'xin',
  '吟': 'yin',
  '邱': 'qiu',
  '芦': 'lu', '欢': 'huan',
  '玮': 'wei',
  '龚': 'gong', '龙': 'long',
  '赞': 'zan',
  '杰': 'jie',
  '晏': 'yan', '金': 'jin', '伟': 'wei',
  '熊': 'xiong', '嘉': 'jia', '颖': 'ying',
  '果': 'guo', '师': 'shi', '兄': 'xiong',
  '田': 'tian', '殷': 'yin',
  '周': 'zhou', '姿': 'zi',
  '媛': 'yuan',
  '永': 'yong', '基': 'ji',
  '丽': 'li', '增': 'zeng', '秀': 'xiu',
  '贞': 'zhen',
  '桂': 'gui', '语': 'yu',
  '宝': 'bao', '哥': 'ge',
  '裕': 'yu', '毫': 'hao',
  '晗': 'han',
  '怡': 'yi',
  '芝': 'zhi',
  '丰': 'feng',
  '雨': 'yu',
  '浩': 'hao',
  '范': 'fan', '瑞': 'rui',
  '段': 'duan', '夏': 'xia',
  '轩': 'xuan',
  '艳': 'yan',
  '邹': 'zou', '珍': 'zhen',
  '芳': 'fang',
  '叮': 'ding', '当': 'dang',
  '贺': 'he',
  '青': 'qing',
  '肖': 'xiao',
  '晓': 'xiao', '雯': 'wen',
  '涛': 'tao',
  '劲': 'jin',
  '利': 'li',
  '城': 'cheng',
  '荆': 'jing', '璐': 'lu',
  '霈': 'pei', '蔚': 'wei',
  '彬': 'bin',
  '衫': 'shan',
  '平': 'ping',
  '易': 'yi', '宣': 'xuan',
  '宇': 'yu',
  '娜': 'na',
  '晴': 'qing',
  '历': 'li', '鹏': 'peng',
  '夏': 'xia',
  '相': 'xiang', '里': 'li',
  '辜': 'gu',
  '磊': 'lei',
  '广': 'guang',
  '萍': 'ping',
  '亚': 'ya', '男': 'nan',
  '硕': 'shuo',
  '峰': 'feng',
  '宽': 'kuan',
  '旖': 'yi',
  '冰': 'bing', '倩': 'qian',
  '晶': 'jing', '焱': 'yan',
  '康': 'kang',
  '姝': 'shu',
  '蒲': 'pu',
  '义': 'yi',
  '贾': 'jia', '贤': 'xian',
  '厉': 'li',
  '饶': 'rao', '星': 'xing',
  '月': 'yue', '蓉': 'rong',
  '柠': 'ning', '檬': 'meng', '学': 'xue', '姐': 'jie',
  '郑': 'zheng', '依': 'yi',
  '税': 'shui', '衡': 'heng',
  '瑶': 'yao',
  '继': 'ji',
  '虎': 'hu',
  '影': 'ying',
  '任': 'ren', '唤': 'huan',
  '戴': 'dai',
  '秀': 'xiu',
  '喻': 'yu',
  '曼': 'man',
  '蒲': 'pu', '飞': 'fei',
  '晔': 'ye',
  '付': 'fu',
  '杜': 'du', '玲': 'ling',
  '乐': 'le',
  '洪': 'hong',
  '小': 'xiao', '芍': 'shao',
  '彤': 'tong',
  '东': 'dong',
  '振': 'zhen',
  '旭': 'xu',
  '茅': 'mao', '悦': 'yue',
  '川': 'chuan',
  '曹': 'cao',
  '芷': 'zhi', '帆': 'fan',
  '武': 'wu',
  '家': 'jia'
};

// 将中文名转换为拼音用户名
function chineseToUsername(chineseName) {
  if (!chineseName) return '';
  let username = '';
  for (let char of chineseName) {
    if (pinyinMap[char]) {
      username += pinyinMap[char];
    } else {
      // 如果没有映射，使用字符的Unicode编码
      username += char.charCodeAt(0).toString(16);
    }
  }
  return username.toLowerCase();
}

// 格式化日期
function formatDate(excelDate) {
  if (!excelDate) return null;

  // 如果是Excel日期序列号
  if (typeof excelDate === 'number') {
    const date = XLSX.SSF.parse_date_code(excelDate);
    return `${date.y}-${String(date.m).padStart(2, '0')}-${String(date.d).padStart(2, '0')} ${String(date.H || 0).padStart(2, '0')}:${String(date.M || 0).padStart(2, '0')}:${String(date.S || 0).padStart(2, '0')}`;
  }

  // 如果是字符串日期
  if (typeof excelDate === 'string') {
    const date = new Date(excelDate);
    if (!isNaN(date.getTime())) {
      return date.toISOString().slice(0, 19).replace('T', ' ');
    }
  }

  return null;
}

// 转换评级字母为数字
function convertRating(rating) {
  if (!rating) return 0;

  const ratingMap = {
    'A': 5,
    'B': 4,
    'C': 3,
    'D': 2,
    'E': 1,
    'F': 0
  };

  return ratingMap[rating.toString().toUpperCase()] || 0;
}

async function importDesignTasks() {
  try {
    console.log('开始导入设计任务数据...');

    // 读取Excel文件
    const excelPath = path.join(__dirname, '../../hgy-oa-admin/docs/总任务数据.xlsx');
    const workbook = XLSX.readFile(excelPath);
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];
    const data = XLSX.utils.sheet_to_json(worksheet);

    console.log(`读取到 ${data.length} 条数据`);

    // 获取用户映射
    const users = await query('SELECT id, username, nickname FROM system_users');
    const userMap = {};
    users.forEach(user => {
      userMap[user.nickname] = user.id;
      userMap[user.username] = user.id;
    });

    // 获取部门映射
    const departments = await query('SELECT id, name FROM system_depts');
    const deptMap = {};
    departments.forEach(dept => {
      deptMap[dept.name] = dept.id;
    });

    console.log('用户映射:', Object.keys(userMap).length, '个用户');
    console.log('部门映射:', Object.keys(deptMap).length, '个部门');

    // 清空现有数据
    console.log('清空现有设计任务数据...');
    await query('DELETE FROM design_tasks');
    console.log('✅ 现有数据已清空');

    let successCount = 0;
    let errorCount = 0;

    for (let index = 0; index < data.length; index++) {
      const row = data[index];
      try {
        // 映射Excel列到数据库字段
        const taskName = row['任务名称'] || '';
        const projectOverview = row['项目概述'] || '';
        const taskType = row['类型'] || '设计';
        const creator = row['创建人'] || '';
        const createTime = formatDate(row['创建时间']);
        const rating = convertRating(row['评级']);
        const assignee = row['处理人'] || '';
        const assigner = row['派发人'] || '';
        const assignDept = row['派发部门'] || '';
        const assignTime = formatDate(row['派发时间']);
        let currentStatus = row['当前状态'] || '设计中';
        const completeTime = formatDate(row['完成时间']);
        const storageLocation = row['存储位置'] || '';
        const deliveryTime = formatDate(row['交付时间']);

        // 处理状态映射
        if (currentStatus === '已处理' || currentStatus === '设计完') {
          currentStatus = '验收完'; // 已完成的默认已经验收
        } else if (currentStatus === '处理中') {
          currentStatus = '设计中';
        } else if (currentStatus === '待修改') {
          currentStatus = '设计中';
        } else if (currentStatus === '设计中') {
          currentStatus = '设计中';
        } else {
          currentStatus = '设计中'; // 默认状态
        }

        if (!taskName) {
          console.log('跳过空任务名称的行');
          continue;
        }

        // 查找用户ID
        const creatorUserId = userMap[creator] || userMap[chineseToUsername(creator)] || null;
        const assigneeUserId = userMap[assignee] || userMap[chineseToUsername(assignee)] || null;
        const assignerUserId = userMap[assigner] || userMap[chineseToUsername(assigner)] || null;

        // 查找部门ID
        const departmentId = deptMap[assignDept] || null;

        // 插入设计任务（按序号顺序导入，使用created_at来控制顺序）
        const importTime = new Date(Date.now() + index * 1000); // 每条记录间隔1秒，确保顺序

        await query(`
          INSERT INTO design_tasks (
            task_name, project_overview, task_type, creator, creator_user_id,
            assign_time, rating, assignee, assigner, department,
            current_status, complete_time, storage_location, delivery_time,
            status, created_at, updated_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `, [
          taskName, projectOverview, taskType, creator, creatorUserId,
          assignTime, rating, assignee, assigner, assignDept,
          currentStatus, completeTime, storageLocation, deliveryTime || null,
          currentStatus === '验收完' ? '已完成' : (currentStatus === '设计完' ? '处理中' : '待处理'),
          importTime.toISOString().slice(0, 19).replace('T', ' '),
          importTime.toISOString().slice(0, 19).replace('T', ' ')
        ]);

        console.log(`✅ 成功导入任务: ${taskName} - 创建人: ${creator} (ID: ${creatorUserId}) - 部门: ${assignDept} (ID: ${departmentId})`);
        successCount++;

      } catch (error) {
        console.error(`❌ 导入任务失败:`, error.message);
        console.error('行数据:', row);
        errorCount++;
      }
    }

    console.log(`\n导入完成！成功: ${successCount}, 失败: ${errorCount}`);
    process.exit(0);

  } catch (error) {
    console.error('导入设计任务数据失败:', error);
    process.exit(1);
  }
}

importDesignTasks();
