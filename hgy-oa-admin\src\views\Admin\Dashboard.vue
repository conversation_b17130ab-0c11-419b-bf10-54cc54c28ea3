<template>
  <div class="dashboard">
    <h1>数据统计</h1>
    
    <!-- 统计卡片 -->
    <a-row :gutter="24" style="margin-bottom: 24px;">
      <a-col :span="6">
        <a-card>
          <a-statistic
            title="模板总数"
            :value="statistics.templates"
            :value-style="{ color: '#1890ff' }"
          >
            <template #prefix>
              <icon-file />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
      
      <a-col :span="6">
        <a-card>
          <a-statistic
            title="素材总数"
            :value="statistics.materials"
            :value-style="{ color: '#52c41a' }"
          >
            <template #prefix>
              <icon-image />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
      
      <a-col :span="6">
        <a-card>
          <a-statistic
            title="字体总数"
            :value="statistics.fonts"
            :value-style="{ color: '#faad14' }"
          >
            <template #prefix>
              <icon-font-colors />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
      
      <a-col :span="6">
        <a-card>
          <a-statistic
            title="用户总数"
            :value="statistics.users"
            :value-style="{ color: '#f5222d' }"
          >
            <template #prefix>
              <icon-user />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
    </a-row>

    <!-- 图表区域 -->
    <a-row :gutter="24">
      <a-col :span="12">
        <a-card title="素材分类统计" style="margin-bottom: 24px;">
          <div ref="materialChart" style="height: 300px;"></div>
        </a-card>
      </a-col>
      
      <a-col :span="12">
        <a-card title="最近7天访问量" style="margin-bottom: 24px;">
          <div ref="visitChart" style="height: 300px;"></div>
        </a-card>
      </a-col>
    </a-row>

    <!-- 最近活动 -->
    <a-card title="最近活动">
      <a-list :data="recentActivities" :pagination="false">
        <template #item="{ item }">
          <a-list-item>
            <a-list-item-meta
              :title="item.title"
              :description="item.description"
            >
              <template #avatar>
                <a-avatar :style="{ backgroundColor: item.color }">
                  <component :is="item.icon" />
                </a-avatar>
              </template>
            </a-list-item-meta>
            <template #actions>
              <span style="color: #999;">{{ item.time }}</span>
            </template>
          </a-list-item>
        </template>
      </a-list>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { IconFile, IconImage, IconFontColors, IconUser, IconPlus, IconEdit, IconDelete } from '@arco-design/web-vue/es/icon'
import { getUserStats } from '@/api/user'
import { request } from '@/utils/request'

// 统计数据
const statistics = ref({
  templates: 0,
  materials: 0,
  fonts: 0,
  users: 0
})

// 最近活动数据
const recentActivities = ref([
  {
    title: '新增模板',
    description: '用户上传了新的设计模板',
    time: '2小时前',
    icon: IconPlus,
    color: '#52c41a'
  },
  {
    title: '编辑素材',
    description: '管理员修改了图片素材分类',
    time: '4小时前',
    icon: IconEdit,
    color: '#1890ff'
  },
  {
    title: '删除用户',
    description: '删除了违规用户账号',
    time: '6小时前',
    icon: IconDelete,
    color: '#f5222d'
  },
  {
    title: '新增字体',
    description: '系统添加了5个新字体',
    time: '1天前',
    icon: IconPlus,
    color: '#faad14'
  }
])

const materialChart = ref()
const visitChart = ref()

// 获取统计数据
const fetchStatistics = async () => {
  try {
    // 获取模板数量
    const templateData = await request.get('/api/template/templateList?pageNum=1&pageSize=1')
    statistics.value.templates = templateData.data?.total || 0

    // 获取素材数量（使用统一的素材接口）
    const materialData = await request.get('/api/material/list?page=1&pageSize=1')
    statistics.value.materials = materialData.data?.total || 0

    // 获取字体数量
    const fontData = await request.get('/api/font/list?pageNum=1&pageSize=1')
    statistics.value.fonts = fontData.data?.total || 0

    // 获取用户统计
    const userStatsRes = await getUserStats()
    statistics.value.users = userStatsRes.data?.total || 0

  } catch (error) {
    console.error('获取统计数据失败:', error)
    Message.error('获取统计数据失败')
  }
}

// 初始化图表
const initCharts = () => {
  // 这里可以集成 ECharts 或其他图表库
  // 暂时显示占位内容
  if (materialChart.value) {
    materialChart.value.innerHTML = `
      <div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #999;">
        <div style="text-align: center;">
          <p>素材分类统计图表</p>
          <p style="font-size: 12px;">文字素材: ${statistics.value.materials > 0 ? Math.floor(statistics.value.materials * 0.3) : 0}</p>
          <p style="font-size: 12px;">图片素材: ${statistics.value.materials > 0 ? Math.floor(statistics.value.materials * 0.5) : 0}</p>
          <p style="font-size: 12px;">背景图片: ${statistics.value.materials > 0 ? Math.floor(statistics.value.materials * 0.2) : 0}</p>
        </div>
      </div>
    `
  }
  
  if (visitChart.value) {
    visitChart.value.innerHTML = `
      <div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #999;">
        <div style="text-align: center;">
          <p>访问量趋势图表</p>
          <p style="font-size: 12px;">今日访问: 1,234</p>
          <p style="font-size: 12px;">昨日访问: 1,156</p>
          <p style="font-size: 12px;">本周访问: 8,567</p>
        </div>
      </div>
    `
  }
}

onMounted(async () => {
  await fetchStatistics()
  initCharts()
})
</script>

<style scoped>
.dashboard h1 {
  margin-bottom: 24px;
  color: #262626;
}

:deep(.arco-statistic-title) {
  font-size: 14px;
  color: #8c8c8c;
}

:deep(.arco-statistic-content) {
  font-size: 24px;
  font-weight: 600;
}

:deep(.arco-card-header) {
  border-bottom: 1px solid #f0f0f0;
}
</style>
