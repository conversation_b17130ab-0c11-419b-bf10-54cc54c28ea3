<template>
  <div class="dashboard">
    <h1>数据统计</h1>
    
    <!-- 时间筛选 -->
    <a-row style="margin-bottom: 16px;">
      <a-col :span="24">
        <a-space>
          <span>统计时间范围：</span>
          <a-select v-model="timeRangeType" @change="handleTimeRangeTypeChange" style="width: 120px;">
            <a-option value="1">近1天</a-option>
            <a-option value="7">近1周</a-option>
            <a-option value="30">近1月</a-option>
            <a-option value="90">近3月</a-option>
            <a-option value="custom">自定义</a-option>
          </a-select>

          <!-- 自定义时间范围选择器 -->
          <a-range-picker
            v-if="timeRangeType === 'custom'"
            v-model="customDateRange"
            @change="handleCustomDateChange"
            style="width: 280px;"
            format="YYYY-MM-DD"
            :placeholder="['开始日期', '结束日期']"
            :allow-clear="true"
          />

          <span style="color: #999; font-size: 12px;">（按完成时间统计）</span>
        </a-space>
      </a-col>
    </a-row>

    <!-- 统计卡片 -->
    <a-row :gutter="24" style="margin-bottom: 24px;">
      <a-col :span="6">
        <a-card :loading="loading">
          <a-statistic
            title="设计任务总数"
            :value="statistics.totalTasks"
            :value-style="{ color: '#1890ff' }"
          >
            <template #prefix>
              <icon-file />
            </template>
          </a-statistic>
        </a-card>
      </a-col>

      <a-col :span="6">
        <a-card :loading="loading">
          <a-statistic
            title="已完成任务"
            :value="statistics.completedTasks"
            :value-style="{ color: '#52c41a' }"
          >
            <template #prefix>
              <icon-check-circle />
            </template>
          </a-statistic>
        </a-card>
      </a-col>

      <a-col :span="6">
        <a-card :loading="loading">
          <a-statistic
            title="进行中任务"
            :value="statistics.inProgressTasks"
            :value-style="{ color: '#faad14' }"
          >
            <template #prefix>
              <icon-clock-circle />
            </template>
          </a-statistic>
        </a-card>
      </a-col>

      <a-col :span="6">
        <a-card :loading="loading">
          <a-statistic
            title="已验收任务"
            :value="statistics.approvedTasks"
            :value-style="{ color: '#f5222d' }"
          >
            <template #prefix>
              <icon-check />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
    </a-row>

    <!-- 图表区域 -->
    <a-row :gutter="24">
      <a-col :span="12">
        <a-card style="margin-bottom: 24px;">
          <template #title>
            <a-space>
              <span>按派发部门统计分数</span>
              <a-tag color="blue">{{ getTimeRangeText() }}</a-tag>
            </a-space>
          </template>
          <div ref="deptChart" style="height: 300px;">
            <a-table
              :data="statistics.deptStats"
              :pagination="false"
              size="small"
              :scroll="{ y: 250 }"
              :loading="loading"
            >
              <template #columns>
                <a-table-column title="部门" data-index="department" width="120" />
                <a-table-column title="任务数" data-index="task_count" width="80" />
                <a-table-column title="平均分" data-index="avg_rating" width="80" />
                <a-table-column title="总分" data-index="total_score" width="80" />
              </template>
            </a-table>
          </div>
        </a-card>
      </a-col>

      <a-col :span="12">
        <a-card style="margin-bottom: 24px;">
          <template #title>
            <a-space>
              <span>按处理人统计分数</span>
              <a-tag color="green">{{ getTimeRangeText() }}</a-tag>
            </a-space>
          </template>
          <div ref="assigneeChart" style="height: 300px;">
            <a-table
              :data="statistics.assigneeStats"
              :pagination="false"
              size="small"
              :scroll="{ y: 250 }"
              :loading="loading"
            >
              <template #columns>
                <a-table-column title="处理人" data-index="assignee" width="120" />
                <a-table-column title="任务数" data-index="task_count" width="80" />
                <a-table-column title="平均分" data-index="avg_rating" width="80" />
                <a-table-column title="总分" data-index="total_score" width="80" />
              </template>
            </a-table>
          </div>
        </a-card>
      </a-col>
    </a-row>

    <!-- 最近活动 -->
    <a-card title="最近活动">
      <a-list :data="recentActivities" :pagination="false">
        <template #item="{ item }">
          <a-list-item>
            <a-list-item-meta
              :title="item.title"
              :description="item.description"
            >
              <template #avatar>
                <a-avatar :style="{ backgroundColor: item.color }">
                  <component :is="item.icon" />
                </a-avatar>
              </template>
            </a-list-item-meta>
            <template #actions>
              <span style="color: #999;">{{ item.time }}</span>
            </template>
          </a-list-item>
        </template>
      </a-list>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { IconFile, IconCheckCircle, IconClockCircle, IconCheck, IconPlus, IconEdit, IconDelete } from '@arco-design/web-vue/es/icon'
import { Message } from '@arco-design/web-vue'
import { request } from '@/utils/request'

// 类型定义
interface StatisticsData {
  totalTasks: number
  completedTasks: number
  inProgressTasks: number
  approvedTasks: number
  deptStats: Array<{
    department: string
    task_count: number
    avg_rating: number
    total_score: number
  }>
  assigneeStats: Array<{
    assignee: string
    task_count: number
    avg_rating: number
    total_score: number
  }>
  statusStats: Array<{
    current_status: string
    count: number
  }>
}

// 时间范围类型
const timeRangeType = ref('7')
// 自定义时间范围
const customDateRange = ref([])
// 加载状态
const loading = ref(false)

// 统计数据
const statistics = ref<StatisticsData>({
  totalTasks: 0,
  completedTasks: 0,
  inProgressTasks: 0,
  approvedTasks: 0,
  deptStats: [],
  assigneeStats: [],
  statusStats: []
})

// 最近活动数据
const recentActivities = ref([
  {
    title: '新增模板',
    description: '用户上传了新的设计模板',
    time: '2小时前',
    icon: IconPlus,
    color: '#52c41a'
  },
  {
    title: '编辑素材',
    description: '管理员修改了图片素材分类',
    time: '4小时前',
    icon: IconEdit,
    color: '#1890ff'
  },
  {
    title: '删除用户',
    description: '删除了违规用户账号',
    time: '6小时前',
    icon: IconDelete,
    color: '#f5222d'
  },
  {
    title: '新增字体',
    description: '系统添加了5个新字体',
    time: '1天前',
    icon: IconPlus,
    color: '#faad14'
  }
])

const deptChart = ref()
const assigneeChart = ref()

// 格式化日期为字符串
const formatDateToString = (date: any) => {
  if (!date) return ''
  if (typeof date === 'string') return date
  if (date instanceof Date) {
    return date.toISOString().slice(0, 10)
  }
  return ''
}

// 获取时间范围文本
const getTimeRangeText = () => {
  if (timeRangeType.value === 'custom' && customDateRange.value.length === 2) {
    const startDate = formatDateToString(customDateRange.value[0])
    const endDate = formatDateToString(customDateRange.value[1])
    return `${startDate} 至 ${endDate}`
  }

  const rangeMap = {
    '1': '近1天',
    '7': '近1周',
    '30': '近1月',
    '90': '近3月'
  }
  return rangeMap[timeRangeType.value] || '近1周'
}

// 时间范围类型变化处理
const handleTimeRangeTypeChange = () => {
  if (timeRangeType.value !== 'custom') {
    customDateRange.value = []
    fetchStatistics()
  }
}

// 自定义时间范围变化处理
const handleCustomDateChange = () => {
  if (customDateRange.value && customDateRange.value.length === 2) {
    fetchStatistics()
  }
}

// 获取设计任务统计数据
const fetchStatistics = async () => {
  try {
    loading.value = true
    let apiUrl = '/api/design/stats'

    // 构建查询参数
    const params = new URLSearchParams()

    if (timeRangeType.value === 'custom' && customDateRange.value.length === 2) {
      // 自定义时间范围
      const startDate = formatDateToString(customDateRange.value[0])
      const endDate = formatDateToString(customDateRange.value[1])
      params.append('startDate', startDate)
      params.append('endDate', endDate)
    } else {
      // 预设时间范围
      params.append('timeRange', timeRangeType.value)
    }

    const response = await request.get(`${apiUrl}?${params.toString()}`)
    console.log('API响应:', response) // 调试日志

    if (response?.success) {
      const data = response.data
      statistics.value = {
        totalTasks: data.totalTasks || 0,
        completedTasks: data.completedTasks || 0,
        inProgressTasks: data.inProgressTasks || 0,
        approvedTasks: data.approvedTasks || 0,
        deptStats: data.deptStats || [],
        assigneeStats: data.assigneeStats || [],
        statusStats: data.statusStats || []
      }
    } else {
      console.error('API返回失败:', response)
      Message.error('获取设计任务统计失败')
    }
  } catch (error) {
    console.error('获取设计任务统计失败:', error)
    Message.error('获取设计任务统计失败')
  } finally {
    loading.value = false
  }
}

onMounted(async () => {
  await fetchStatistics()
})
</script>

<style scoped>
.dashboard h1 {
  margin-bottom: 24px;
  color: #262626;
}

:deep(.arco-statistic-title) {
  font-size: 14px;
  color: #8c8c8c;
}

:deep(.arco-statistic-content) {
  font-size: 24px;
  font-weight: 600;
}

:deep(.arco-card-header) {
  border-bottom: 1px solid #f0f0f0;
}
</style>
