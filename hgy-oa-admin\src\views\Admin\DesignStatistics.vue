<template>
  <div class="design-statistics">
    <div class="page-header">
      <h2>工作统计</h2>
      <p>设计任务工作量统计与分析</p>
    </div>

    <!-- 统计卡片 -->
    <a-row :gutter="16" style="margin-bottom: 24px;">
      <a-col :span="6">
        <a-card>
          <a-statistic
            title="总任务数"
            :value="statistics.totalTasks"
            :value-style="{ color: '#1890ff' }"
          >
            <template #prefix>
              <icon-file />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card>
          <a-statistic
            title="总工作量"
            :value="statistics.totalScore"
            suffix="分"
            :value-style="{ color: '#52c41a' }"
          >
            <template #prefix>
              <icon-bar-chart />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card>
          <a-statistic
            title="已完成工作量"
            :value="statistics.completedScore"
            suffix="分"
            :value-style="{ color: '#faad14' }"
          >
            <template #prefix>
              <icon-check-circle />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card>
          <a-statistic
            title="平均评级"
            :value="statistics.averageRating"
            :value-style="{ color: '#722ed1' }"
          >
            <template #prefix>
              <icon-clock-circle />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
    </a-row>

    <!-- 筛选条件 -->
    <a-card style="margin-bottom: 24px;">
      <a-row :gutter="16">
        <a-col :span="4">
          <a-select v-model="filters.statisticsType" placeholder="统计维度">
            <a-option value="person">按人员统计</a-option>
            <a-option value="department">按部门统计</a-option>
          </a-select>
        </a-col>
        <a-col :span="4">
          <a-select v-model="filters.department" placeholder="选择部门" allow-clear>
            <a-option value="">全部部门</a-option>
            <a-option value="技术部">技术部</a-option>
            <a-option value="南方运营部">南方运营部</a-option>
            <a-option value="北方运营部">北方运营部</a-option>
            <a-option value="人事部">人事部</a-option>
          </a-select>
        </a-col>
        <a-col :span="4">
          <a-select v-model="filters.taskType" placeholder="任务类型" allow-clear>
            <a-option value="">全部类型</a-option>
            <a-option value="设计">设计</a-option>
            <a-option value="视频">视频</a-option>
          </a-select>
        </a-col>
        <a-col :span="6">
          <a-range-picker v-model="filters.dateRange" style="width: 100%" />
        </a-col>
        <a-col :span="6">
          <a-space>
            <a-button type="primary" @click="handleSearch">
              <template #icon>
                <icon-search />
              </template>
              查询
            </a-button>
            <a-button @click="handleReset">
              <template #icon>
                <icon-refresh />
              </template>
              重置
            </a-button>
          </a-space>
        </a-col>
      </a-row>
    </a-card>

    <!-- 工作量统计表格 -->
    <a-card :title="filters.statisticsType === 'person' ? '人员工作量统计' : '部门工作量统计'">
      <a-table
        :columns="statisticsColumns"
        :data="statisticsTableData"
        :pagination="false"
        :loading="loading"
        size="small"
      >
        <template #score="{ record }">
          <a-tag color="blue">{{ record.totalScore }}分</a-tag>
        </template>
        <template #completedScore="{ record }">
          <a-tag color="green">{{ record.completedScore }}分</a-tag>
        </template>
        <template #averageRating="{ record }">
          <a-tag color="gold">{{ record.averageRating }}</a-tag>
        </template>
        <template #progress="{ record }">
          <a-progress
            :percent="Math.round((record.completedScore / record.totalScore) * 100)"
            size="small"
            :show-text="true"
          />
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { Message } from '@arco-design/web-vue'
import {
  IconFile,
  IconCheckCircle,
  IconClockCircle,
  IconBarChart,
  IconSearch,
  IconRefresh
} from '@arco-design/web-vue/es/icon'

// 响应式数据
const loading = ref(false)
const statisticsTableData = ref([])

// 统计数据
const statistics = reactive({
  totalTasks: 0,
  totalScore: 0,
  completedScore: 0,
  averageRating: 'F'
})

// 筛选条件
const filters = reactive({
  statisticsType: 'person', // person | department
  department: '',
  taskType: '',
  dateRange: []
})

// 统计表格列配置（动态）
const statisticsColumns = computed(() => {
  if (filters.statisticsType === 'person') {
    return [
      { title: '姓名', dataIndex: 'name', width: 120 },
      { title: '部门', dataIndex: 'department', width: 120 },
      { title: '任务数量', dataIndex: 'taskCount', width: 100, align: 'center' },
      { title: '总工作量', slotName: 'score', width: 120, align: 'center' },
      { title: '已完成工作量', slotName: 'completedScore', width: 140, align: 'center' },
      { title: '平均评级', slotName: 'averageRating', width: 100, align: 'center' },
      { title: '完成进度', slotName: 'progress', width: 150, align: 'center' }
    ]
  } else {
    return [
      { title: '部门', dataIndex: 'department', width: 150 },
      { title: '人员数量', dataIndex: 'personCount', width: 100, align: 'center' },
      { title: '任务数量', dataIndex: 'taskCount', width: 100, align: 'center' },
      { title: '总工作量', slotName: 'score', width: 120, align: 'center' },
      { title: '已完成工作量', slotName: 'completedScore', width: 140, align: 'center' },
      { title: '平均评级', slotName: 'averageRating', width: 100, align: 'center' },
      { title: '完成进度', slotName: 'progress', width: 150, align: 'center' }
    ]
  }
})

// 获取状态颜色
const getStatusColor = (status: string): string => {
  const colorMap: Record<string, string> = {
    '待处理': 'gray',
    '处理中': 'blue',
    '已完成': 'green',
    '已取消': 'red'
  }
  return colorMap[status] || 'gray'
}

// 获取统计数据
const fetchStatistics = async () => {
  try {
    // 这里应该调用实际的API
    // const response = await getDesignStatistics(filters)

    // 模拟数据
    statistics.totalTasks = 156
    statistics.totalScore = 1250.5
    statistics.completedScore = 890.2
    statistics.averageRating = 'B'
  } catch (error) {
    console.error('获取统计数据失败:', error)
    Message.error('获取统计数据失败')
  }
}

// 获取统计表格数据
const fetchStatisticsTableData = async () => {
  loading.value = true
  try {
    // 这里应该调用实际的API来获取工作量统计
    // const response = await getWorkloadStatistics(filters)

    // 模拟数据
    if (filters.statisticsType === 'person') {
      // 按人员统计
      statisticsTableData.value = [
        {
          name: '张三',
          department: '技术部',
          taskCount: 15,
          totalScore: 180.5,
          completedScore: 150.2,
          averageRating: 'A'
        },
        {
          name: '李四',
          department: '技术部',
          taskCount: 12,
          totalScore: 144.0,
          completedScore: 120.6,
          averageRating: 'B'
        },
        {
          name: '王五',
          department: '南方运营部',
          taskCount: 18,
          totalScore: 216.3,
          completedScore: 180.1,
          averageRating: 'A'
        }
      ]
    } else {
      // 按部门统计
      statisticsTableData.value = [
        {
          department: '技术部',
          personCount: 8,
          taskCount: 45,
          totalScore: 540.8,
          completedScore: 450.3,
          averageRating: 'B'
        },
        {
          department: '南方运营部',
          personCount: 6,
          taskCount: 38,
          totalScore: 456.2,
          completedScore: 380.1,
          averageRating: 'A'
        },
        {
          department: '北方运营部',
          personCount: 5,
          taskCount: 32,
          totalScore: 384.0,
          completedScore: 320.5,
          averageRating: 'B'
        }
      ]
    }
  } catch (error) {
    console.error('获取统计数据失败:', error)
    Message.error('获取统计数据失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  fetchStatistics()
  fetchStatisticsTableData()
}

// 重置
const handleReset = () => {
  filters.statisticsType = 'person'
  filters.department = ''
  filters.taskType = ''
  filters.dateRange = []
  fetchStatistics()
  fetchStatisticsTableData()
}

// 初始化
onMounted(() => {
  fetchStatistics()
  fetchStatisticsTableData()
})
</script>

<style scoped>
.design-statistics {
  padding: 0;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #1d2129;
}

.page-header p {
  margin: 0;
  color: #86909c;
  font-size: 14px;
}
</style>
