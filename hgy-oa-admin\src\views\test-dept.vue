<template>
  <div style="padding: 20px;">
    <h2>部门API测试页面</h2>
    
    <div style="margin: 20px 0;">
      <a-button @click="testGetList" type="primary">测试获取部门列表</a-button>
      <a-button @click="testCreateDept" style="margin-left: 10px;">测试创建部门</a-button>
    </div>
    
    <div v-if="loading">加载中...</div>
    
    <div v-if="result" style="margin-top: 20px;">
      <h3>测试结果:</h3>
      <pre>{{ JSON.stringify(result, null, 2) }}</pre>
    </div>
    
    <div v-if="error" style="margin-top: 20px; color: red;">
      <h3>错误信息:</h3>
      <pre>{{ error }}</pre>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Message } from '@arco-design/web-vue'

// 直接使用fetch测试API
const loading = ref(false)
const result = ref(null)
const error = ref('')

const testGetList = async () => {
  loading.value = true
  error.value = ''
  result.value = null
  
  try {
    const { getEndpointUrl } = await import('../config/api')
    const response = await fetch(`${getEndpointUrl('DEPARTMENT_LIST')}?pageNum=1&pageSize=5`)
    const data = await response.json()
    result.value = data
    
    if (data.success) {
      Message.success('获取部门列表成功')
    } else {
      Message.error('获取部门列表失败')
    }
  } catch (err) {
    error.value = err.message
    Message.error('请求失败')
  } finally {
    loading.value = false
  }
}

const testCreateDept = async () => {
  loading.value = true
  error.value = ''
  result.value = null
  
  try {
    const { getApiBaseUrl } = await import('../config/api')
    const response = await fetch(`${getApiBaseUrl()}/api/department`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        name: '前端测试部门' + Date.now(),
        code: 'FRONTEND_TEST_' + Date.now(),
        description: '前端测试创建的部门',
        sort_order: 1,
        status: 1
      })
    })
    
    const data = await response.json()
    result.value = data
    
    if (data.success) {
      Message.success('创建部门成功')
    } else {
      Message.error('创建部门失败')
    }
  } catch (err) {
    error.value = err.message
    Message.error('请求失败')
  } finally {
    loading.value = false
  }
}
</script>
