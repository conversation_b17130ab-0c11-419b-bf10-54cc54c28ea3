import express from 'express';
import multer from 'multer';
import { query, paginate } from '../config/database.js';
import { uploadToQiniu, generateFileKey, getPrivateFileUrl } from '../config/qiniu.js';
import { authenticateUser, optionalAuth } from '../middleware/auth.js';

const router = express.Router();

// 配置multer用于处理文件上传
const storage = multer.memoryStorage();
const upload = multer({
  storage,
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB限制
  }
});

// 获取模板列表
router.get('/templateList', async (req, res) => {
  try {
    const { pageNum = 1, pageSize = 10 } = req.query;
    
    const sql = `
      SELECT
        id,
        COALESCE(cover, thumbnail_url) as cover,
        name as title,
        COALESCE(state, 1) as state,
        COALESCE(json, canvas_data) as json,
        created_at,
        updated_at
      FROM editor_templates
      WHERE COALESCE(state, 1) = 1
      ORDER BY created_at DESC
    `;
    
    const result = await paginate(sql, [], pageNum, pageSize);
    
    // 解析JSON字段
    result.records = result.records.map(item => ({
      ...item,
      json: typeof item.json === 'string' ? JSON.parse(item.json) : item.json
    }));
    
    res.success(result);
  } catch (error) {
    console.error('获取模板列表失败:', error);
    res.error('获取模板列表失败');
  }
});

// 根据ID获取模板详情
router.get('/template/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    const sql = `
      SELECT
        id,
        COALESCE(cover, thumbnail_url) as cover,
        name as title,
        COALESCE(state, 1) as state,
        COALESCE(json, canvas_data) as json,
        created_at,
        updated_at
      FROM editor_templates
      WHERE id = ? AND COALESCE(state, 1) = 1
    `;
    
    const result = await query(sql, [id]);
    
    if (result.length === 0) {
      return res.error('模板不存在', 404);
    }
    
    const template = result[0];
    template.json = typeof template.json === 'string' ? JSON.parse(template.json) : template.json;
    
    res.success(template);
  } catch (error) {
    console.error('获取模板详情失败:', error);
    res.error('获取模板详情失败');
  }
});

// 创建新模板
router.post('/template', async (req, res) => {
  try {
    const { cover, title, json } = req.body;
    
    if (!title || !json) {
      return res.error('标题和JSON数据不能为空', 400);
    }
    
    const sql = `
      INSERT INTO editor_templates (cover, name, json, state, created_at, updated_at)
      VALUES (?, ?, ?, 1, NOW(), NOW())
    `;
    
    const jsonStr = typeof json === 'object' ? JSON.stringify(json) : json;
    const result = await query(sql, [cover, title, jsonStr]);
    
    res.success({ id: result.insertId }, '模板创建成功');
  } catch (error) {
    console.error('创建模板失败:', error);
    res.error('创建模板失败');
  }
});

// 更新模板
router.put('/template/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { cover, title, json } = req.body;
    
    const sql = `
      UPDATE editor_templates
      SET cover = ?, name = ?, json = ?, updated_at = NOW()
      WHERE id = ? AND COALESCE(state, 1) = 1
    `;
    
    const jsonStr = typeof json === 'object' ? JSON.stringify(json) : json;
    const result = await query(sql, [cover, title, jsonStr, id]);
    
    if (result.affectedRows === 0) {
      return res.error('模板不存在或更新失败', 404);
    }
    
    res.success(null, '模板更新成功');
  } catch (error) {
    console.error('更新模板失败:', error);
    res.error('更新模板失败');
  }
});

// 删除模板（软删除）
router.delete('/template/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    const sql = `
      UPDATE editor_templates 
      SET state = 0, updated_at = NOW()
      WHERE id = ?
    `;
    
    const result = await query(sql, [id]);
    
    if (result.affectedRows === 0) {
      return res.error('模板不存在', 404);
    }
    
    res.success(null, '模板删除成功');
  } catch (error) {
    console.error('删除模板失败:', error);
    res.error('删除模板失败');
  }
});

// 保存模板
router.post('/save', optionalAuth, upload.array('images'), async (req, res) => {
  try {
    const {
      name,
      description,
      category,
      canvas_data,
      width,
      height,
      is_public = 1,
      tags
    } = req.body;

    console.log('保存模板请求:', { name, description, category, width, height });
    console.log('上传的图片数量:', req.files?.length || 0);

    // 验证必填字段
    if (!name || !canvas_data) {
      return res.error('模板名称和画布数据不能为空', 400);
    }

    // 获取当前用户ID，先检查数据库中是否存在用户
    let created_by = req.user?.id || 1;

    // 验证用户是否存在
    const userCheck = await query('SELECT id FROM system_users WHERE id = ?', [created_by]);
    if (userCheck.length === 0) {
      // 如果用户不存在，查找第一个可用用户
      const firstUser = await query('SELECT id FROM system_users LIMIT 1');
      if (firstUser.length > 0) {
        created_by = firstUser[0].id;
      } else {
        return res.error('系统中没有可用用户，请先创建用户', 400);
      }
    }

    // 解析canvas_data
    let parsedCanvasData;
    try {
      parsedCanvasData = typeof canvas_data === 'string' ? JSON.parse(canvas_data) : canvas_data;
    } catch (error) {
      return res.error('画布数据格式错误', 400);
    }

    // 处理blob图片上传
    const imageMap = new Map(); // 存储blob URL到七牛云URL的映射

    if (req.files && req.files.length > 0) {
      console.log('开始处理blob图片上传...');

      for (let i = 0; i < req.files.length; i++) {
        const file = req.files[i];
        const blobUrl = req.body[`blobUrl_${i}`]; // 前端传递的blob URL

        if (!blobUrl) continue;

        try {
          // 生成七牛云文件key
          const fileKey = generateFileKey(file.originalname || `template_image_${Date.now()}.png`, 'editor_templates');
          console.log('生成的文件key:', fileKey);

          // 上传到七牛云
          const qiniuResult = await uploadToQiniu(file.buffer, fileKey, file.originalname);
          console.log('七牛云上传结果:', qiniuResult);

          // 生成私有URL
          let fileUrl;
          try {
            fileUrl = getPrivateFileUrl(qiniuResult.key, 86400 * 365); // 1年有效期
            console.log('生成的私有URL:', fileUrl);
          } catch (error) {
            console.error('生成私有URL失败，使用公开URL:', error);
            fileUrl = qiniuResult.url;
          }

          // 记录映射关系
          imageMap.set(blobUrl, fileUrl);
          console.log(`映射: ${blobUrl} -> ${fileUrl}`);

        } catch (error) {
          console.error('上传图片失败:', error);
          return res.error(`图片上传失败: ${error.message}`, 500);
        }
      }
    }

    // 替换canvas_data中的blob URL
    let updatedCanvasData = JSON.stringify(parsedCanvasData);
    for (const [blobUrl, qiniuUrl] of imageMap) {
      updatedCanvasData = updatedCanvasData.replace(new RegExp(blobUrl.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), qiniuUrl);
    }
    parsedCanvasData = JSON.parse(updatedCanvasData);

    // 生成缩略图（使用第一张图片或默认图片）
    let thumbnail_url = null;
    if (imageMap.size > 0) {
      thumbnail_url = Array.from(imageMap.values())[0];
    }

    // 处理cover图片
    let cover_url = null;
    if (req.body.cover) {
      try {
        // cover是base64格式的图片数据
        const coverBase64 = req.body.cover;
        const matches = coverBase64.match(/^data:image\/([a-zA-Z]+);base64,(.+)$/);

        if (matches) {
          const imageType = matches[1];
          const imageData = matches[2];
          const coverBuffer = Buffer.from(imageData, 'base64');

          // 生成cover图片的文件key
          const coverKey = generateFileKey(`cover_${Date.now()}.${imageType}`, 'templates/covers');
          console.log('生成的cover文件key:', coverKey);

          // 上传cover到七牛云
          const coverQiniuResult = await uploadToQiniu(coverBuffer, coverKey, `cover.${imageType}`);
          console.log('cover七牛云上传结果:', coverQiniuResult);

          // 生成私有URL
          try {
            cover_url = getPrivateFileUrl(coverQiniuResult.key, 86400 * 365); // 1年有效期
            console.log('生成的cover私有URL:', cover_url);
          } catch (error) {
            console.error('生成cover私有URL失败，使用公开URL:', error);
            cover_url = coverQiniuResult.url;
          }
        }
      } catch (error) {
        console.error('处理cover图片失败:', error);
        // cover图片处理失败不影响模板保存
      }
    }

    // 插入模板数据
    const insertSql = `
      INSERT INTO editor_templates (
        name, description, category, canvas_data, width, height,
        is_public, created_by, thumbnail_url, cover, tags, json, created_at, updated_at
      )
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
    `;

    const result = await query(insertSql, [
      name,
      description || '',
      category || 'custom',
      JSON.stringify(parsedCanvasData),
      parseInt(width) || 800,
      parseInt(height) || 600,
      parseInt(is_public),
      created_by,
      thumbnail_url,
      cover_url,
      tags ? JSON.stringify(tags) : null,
      JSON.stringify(parsedCanvasData) // json字段也保存一份
    ]);

    console.log('模板保存成功，ID:', result.insertId);

    res.success({
      id: result.insertId,
      message: '模板保存成功',
      imageCount: imageMap.size
    });

  } catch (error) {
    console.error('保存模板失败:', error);
    res.error('保存模板失败: ' + (error.message || '未知错误'));
  }
});

// 获取模板管理列表
router.get('/admin/list', async (req, res) => {
  try {
    const {
      page = 1,
      pageSize = 20,
      keyword,
      category_id,
      is_featured,
      is_public,
      created_by
    } = req.query;

    const offset = (parseInt(page) - 1) * parseInt(pageSize);

    let whereClauses = ['t.state = 1'];
    let params = [];

    if (keyword && keyword.trim()) {
      whereClauses.push('(t.name LIKE ? OR t.description LIKE ?)');
      params.push(`%${keyword}%`, `%${keyword}%`);
    }

    if (category_id && category_id !== '') {
      whereClauses.push('t.category_id = ?');
      params.push(parseInt(category_id));
    }

    if (is_featured !== undefined && is_featured !== '') {
      whereClauses.push('t.is_featured = ?');
      params.push(parseInt(is_featured));
    }

    if (is_public !== undefined && is_public !== '') {
      whereClauses.push('t.is_public = ?');
      params.push(parseInt(is_public));
    }

    if (created_by && created_by !== '') {
      whereClauses.push('t.created_by = ?');
      params.push(parseInt(created_by));
    }

    const whereClause = whereClauses.join(' AND ');

    const listSql = `
      SELECT
        t.id, t.name, t.description, t.category, t.category_id, t.thumbnail_url,
        t.width, t.height, t.is_public, t.is_featured, t.created_by, t.usage_count,
        t.cover, t.created_at, t.updated_at,
        u.nickname as creator_name,
        tc.name as category_name,
        parent_tc.name as parent_category_name
      FROM editor_templates t
      LEFT JOIN system_users u ON t.created_by = u.id
      LEFT JOIN editor_template_categories tc ON t.category_id = tc.id
      LEFT JOIN editor_template_categories parent_tc ON tc.parent_id = parent_tc.id
      WHERE ${whereClause}
      ORDER BY t.created_at DESC
      LIMIT ? OFFSET ?
    `;

    const templates = await query(listSql, [...params, parseInt(pageSize), offset]);

    // 获取总数
    const countSql = `SELECT COUNT(*) as total FROM editor_templates t WHERE ${whereClause}`;
    const [countResult] = await query(countSql, params);

    // 格式化数据
    const formattedTemplates = templates.map(template => ({
      ...template,
      category_display: template.parent_category_name
        ? `${template.parent_category_name} / ${template.category_name}`
        : template.category_name || template.category || '-',
      created_at: template.created_at ? new Date(template.created_at).toISOString().split('T')[0] : '',
      updated_at: template.updated_at ? new Date(template.updated_at).toISOString().split('T')[0] : ''
    }));

    res.success({
      list: formattedTemplates,
      total: countResult.total,
      page: parseInt(page),
      pageSize: parseInt(pageSize)
    });

  } catch (error) {
    console.error('获取模板管理列表失败:', error);
    res.error('获取模板管理列表失败');
  }
});

// 更新模板信息（管理员功能）
router.put('/admin/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { name, category_id, is_featured, is_public, description } = req.body;

    if (!name) {
      return res.error('模板名称不能为空', 400);
    }

    // 检查模板是否存在
    const template = await query('SELECT id FROM editor_templates WHERE id = ? AND state = 1', [id]);
    if (template.length === 0) {
      return res.error('模板不存在', 404);
    }

    // 如果有分类ID，验证分类是否存在
    if (category_id) {
      const category = await query(
        'SELECT id FROM editor_template_categories WHERE id = ? AND status = 1',
        [category_id]
      );
      if (category.length === 0) {
        return res.error('分类不存在', 400);
      }
    }

    const updateSql = `
      UPDATE editor_templates
      SET name = ?, category_id = ?, is_featured = ?, is_public = ?, description = ?, updated_at = NOW()
      WHERE id = ?
    `;

    await query(updateSql, [
      name,
      category_id || null,
      parseInt(is_featured) || 0,
      parseInt(is_public) || 1,
      description || '',
      id
    ]);

    res.success(null, '模板更新成功');

  } catch (error) {
    console.error('更新模板失败:', error);
    res.error('更新模板失败');
  }
});

// 删除模板（管理员功能）
router.delete('/admin/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // 检查模板是否存在
    const template = await query('SELECT id FROM editor_templates WHERE id = ? AND state = 1', [id]);
    if (template.length === 0) {
      return res.error('模板不存在', 404);
    }

    // 软删除
    await query('UPDATE editor_templates SET state = 0, updated_at = NOW() WHERE id = ?', [id]);

    res.success(null, '模板删除成功');

  } catch (error) {
    console.error('删除模板失败:', error);
    res.error('删除模板失败');
  }
});

// ==================== 用户模板相关API ====================

// 获取用户模板列表
router.get('/user/list', optionalAuth, async (req, res) => {
  try {
    const { pageNum = 1, pageSize = 20 } = req.query;
    const userId = req.user?.id; // 从认证中间件获取用户ID

    console.log('获取用户模板列表，用户ID:', userId);

    // 如果没有用户ID，返回空列表（未登录状态）
    if (!userId) {
      return res.success({
        list: [],
        total: 0,
        page: parseInt(pageNum),
        pageSize: parseInt(pageSize)
      });
    }

    const sql = `
      SELECT
        id,
        name,
        COALESCE(cover, thumbnail_url) as thumbnail_url,
        COALESCE(json, canvas_data) as canvas_data,
        description,
        created_at,
        updated_at
      FROM editor_templates
      WHERE created_by = ? AND COALESCE(state, 1) = 1
      ORDER BY created_at DESC
    `;

    const result = await paginate(sql, [userId], pageNum, pageSize);

    // 处理返回数据
    const list = result.records.map(item => ({
      id: item.id,
      name: item.name,
      thumbnail_url: item.thumbnail_url,
      canvas_data: item.canvas_data,
      description: item.description,
      created_at: item.created_at,
      updated_at: item.updated_at
    }));

    res.success({
      list,
      total: result.total,
      page: parseInt(pageNum),
      pageSize: parseInt(pageSize)
    });

  } catch (error) {
    console.error('获取用户模板列表失败:', error);
    res.error('获取用户模板列表失败');
  }
});

// 删除用户模板
router.delete('/user/:id', optionalAuth, async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user?.id;

    if (!userId) {
      return res.error('用户未登录', 401);
    }

    // 检查模板是否属于当前用户
    const checkSql = 'SELECT id, created_by FROM editor_templates WHERE id = ?';
    const templates = await query(checkSql, [id]);

    if (templates.length === 0) {
      return res.error('模板不存在', 404);
    }

    if (templates[0].created_by !== userId) {
      return res.error('无权限删除此模板', 403);
    }

    // 软删除模板
    const deleteSql = 'UPDATE editor_templates SET state = 0, updated_at = NOW() WHERE id = ?';
    await query(deleteSql, [id]);

    res.success(null, '模板删除成功');

  } catch (error) {
    console.error('删除用户模板失败:', error);
    res.error('删除用户模板失败');
  }
});

// 更新用户模板
router.put('/user/:id', optionalAuth, async (req, res) => {
  try {
    const { id } = req.params;
    const { name, description } = req.body;
    const userId = req.user?.id;

    if (!userId) {
      return res.error('用户未登录', 401);
    }

    // 检查模板是否属于当前用户
    const checkSql = 'SELECT id, created_by FROM editor_templates WHERE id = ?';
    const templates = await query(checkSql, [id]);

    if (templates.length === 0) {
      return res.error('模板不存在', 404);
    }

    if (templates[0].created_by !== userId) {
      return res.error('无权限修改此模板', 403);
    }

    // 更新模板信息
    const updateSql = `
      UPDATE editor_templates
      SET name = ?, description = ?, updated_at = NOW()
      WHERE id = ?
    `;
    await query(updateSql, [name, description, id]);

    res.success(null, '模板更新成功');

  } catch (error) {
    console.error('更新用户模板失败:', error);
    res.error('更新用户模板失败');
  }
});

export default router;
