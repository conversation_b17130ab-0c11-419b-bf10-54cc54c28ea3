import { query } from '../src/config/database.js';

const checkTemplates = async () => {
  try {
    console.log('🔍 检查模板数据...\n');

    // 查询所有模板
    const allTemplates = await query(`
      SELECT 
        id, name, description, created_by, created_at, updated_at,
        CASE WHEN state IS NULL THEN 'NULL' ELSE state END as state_value
      FROM editor_templates 
      ORDER BY created_at DESC 
      LIMIT 10
    `);

    console.log(`📋 数据库中的模板 (最近10条):`);
    if (allTemplates.length === 0) {
      console.log('   暂无模板数据');
    } else {
      allTemplates.forEach(template => {
        console.log(`   ID: ${template.id}`);
        console.log(`   名称: ${template.name}`);
        console.log(`   创建者: ${template.created_by}`);
        console.log(`   状态: ${template.state_value}`);
        console.log(`   创建时间: ${template.created_at}`);
        console.log(`   ---`);
      });
    }

    // 查询用户信息
    console.log('\n👥 系统用户列表:');
    const users = await query('SELECT id, username, nickname FROM system_users LIMIT 5');
    if (users.length === 0) {
      console.log('   暂无用户数据');
    } else {
      users.forEach(user => {
        console.log(`   ID: ${user.id}, 用户名: ${user.username}, 昵称: ${user.nickname}`);
      });
    }

    // 检查最新保存的模板
    console.log('\n🆕 最新保存的模板:');
    const latestTemplate = await query(`
      SELECT 
        id, name, description, created_by, created_at,
        CASE WHEN state IS NULL THEN 'NULL' ELSE state END as state_value,
        CASE WHEN canvas_data IS NULL THEN 'NULL' ELSE 'EXISTS' END as canvas_data_status,
        CASE WHEN json IS NULL THEN 'NULL' ELSE 'EXISTS' END as json_status
      FROM editor_templates 
      ORDER BY created_at DESC 
      LIMIT 1
    `);

    if (latestTemplate.length > 0) {
      const template = latestTemplate[0];
      console.log(`   ID: ${template.id}`);
      console.log(`   名称: ${template.name}`);
      console.log(`   描述: ${template.description}`);
      console.log(`   创建者ID: ${template.created_by}`);
      console.log(`   状态: ${template.state_value}`);
      console.log(`   画布数据: ${template.canvas_data_status}`);
      console.log(`   JSON数据: ${template.json_status}`);
      console.log(`   创建时间: ${template.created_at}`);
    } else {
      console.log('   暂无模板');
    }

    console.log('\n✅ 检查完成！');

  } catch (error) {
    console.error('❌ 检查过程中发生错误:', error);
  }
};

// 执行检查
checkTemplates().then(() => {
  console.log('程序执行完成');
  process.exit(0);
}).catch(error => {
  console.error('程序执行失败:', error);
  process.exit(1);
});
