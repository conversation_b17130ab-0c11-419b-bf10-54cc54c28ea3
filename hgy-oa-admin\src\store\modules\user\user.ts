import { defineStore } from 'pinia'
import { ref } from 'vue'

export interface UserInfo {
  id: number
  username: string
  nickname: string
  role: string
  email?: string
  phone?: string
  avatar?: string
  department?: string
}

export const useUserStore = defineStore('user', () => {
  const userInfo = ref<UserInfo | null>(null)
  const token = ref<string>('')

  // 设置用户信息
  const setUserInfo = (info: UserInfo) => {
    userInfo.value = info
  }

  // 设置token
  const setToken = (newToken: string) => {
    token.value = newToken
    localStorage.setItem('token', newToken)
  }

  // 清除用户信息
  const clearUserInfo = () => {
    userInfo.value = null
    token.value = ''
    localStorage.removeItem('token')
  }

  // 初始化用户信息（从localStorage或API获取）
  const initUserInfo = async () => {
    const savedToken = localStorage.getItem('token')
    if (savedToken) {
      token.value = savedToken
      // 这里可以根据token获取用户信息
      // 暂时设置一个默认的管理员用户
      userInfo.value = {
        id: 1,
        username: 'admin',
        nickname: '管理员',
        role: 'admin',
        department: '技术部'
      }
    }
  }

  // 检查是否为管理员
  const isAdmin = () => {
    return userInfo.value?.role === 'admin'
  }

  return {
    userInfo,
    token,
    setUserInfo,
    setToken,
    clearUserInfo,
    initUserInfo,
    isAdmin
  }
})
