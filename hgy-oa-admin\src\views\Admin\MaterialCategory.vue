<template>
  <div class="material-category">
    <div class="page-header">
      <h1>素材分类管理</h1>
      <a-button type="primary" @click="showAddModal">
        新增分类
      </a-button>
    </div>

    <!-- 分类表格 -->
    <a-card>
      <a-table
        :columns="columns"
        :data="tableData"
        :pagination="false"
        :loading="loading"
        row-key="id"
        size="medium"
      >

        <template #status="{ record }">
          <a-switch
            :model-value="record.status === 1"
            @change="(value) => handleStatusChange(record, value)"
            size="small"
          />
        </template>

        <template #created_at="{ record }">
          <div style="font-size: 12px; color: #86909c;">
            {{ formatDateTime(record.created_at) }}
          </div>
        </template>

        <template #actions="{ record }">
          <a-space size="mini">
            <a-button type="text" size="mini" @click="editCategory(record)">
              编辑
            </a-button>
            <a-button type="text" size="mini" status="danger" @click="deleteCategory(record)">
              删除
            </a-button>
          </a-space>
        </template>
      </a-table>
    </a-card>

    <!-- 新增/编辑分类弹窗 -->
    <a-modal
      v-model:visible="modalVisible"
      :title="modalTitle"
      :confirm-loading="loading"
      @ok="handleSubmit"
      @cancel="handleCancel"
    >
      <a-form :model="formData" layout="vertical">
        <a-form-item label="分类名称" required>
          <a-input v-model="formData.name" placeholder="请输入分类名称" />
        </a-form-item>

        <a-form-item label="图标">
          <a-input v-model="formData.icon" placeholder="请输入图标名称（如：icon-text）" />
        </a-form-item>

        <a-form-item label="排序">
          <a-input-number v-model="formData.sort" :min="0" placeholder="数字越小越靠前" />
        </a-form-item>

        <a-form-item label="描述">
          <a-textarea v-model="formData.description" placeholder="请输入分类描述" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { Message, Modal } from '@arco-design/web-vue'
// 暂时移除API导入，使用直接的fetch调用

// 响应式数据
const expandedKeys = ref<string[]>([])
const modalVisible = ref(false)
const isEdit = ref(false)
const currentEditId = ref<number | null>(null)
const loading = ref(false)

// 表单数据
const formData = ref({
  name: '',
  icon: '',
  sort: 0,
  description: ''
})

// 分类数据
const categoryTree = ref<any[]>([])

// 表格列配置
const columns = [
  { title: '分类名称', dataIndex: 'name', width: 150 },
  { title: '图标', dataIndex: 'icon', width: 100 },
  { title: '排序', dataIndex: 'sort_order', width: 80, align: 'center' },
  { title: '素材数量', dataIndex: 'count', width: 100, align: 'center' },
  { title: '描述', dataIndex: 'description', width: 200, ellipsis: true, tooltip: true },
  { title: '状态', slotName: 'status', width: 80, align: 'center' },
  { title: '创建时间', slotName: 'created_at', width: 120 },
  { title: '操作', slotName: 'actions', width: 120, align: 'center', fixed: 'right' }
]

// 计算属性
const modalTitle = computed(() => isEdit.value ? '编辑分类' : '新增分类')

// 将分类数据转换为表格数据
const tableData = computed(() => {
  return categoryTree.value.map(category => ({
    id: parseInt(category.key),
    name: category.title,
    icon: category.icon,
    sort_order: category.sort_order,
    description: category.description,
    count: category.count || 0,
    status: 1,
    created_at: category.created_at || new Date().toISOString()
  }))
})



// 方法
const showAddModal = () => {
  isEdit.value = false
  currentEditId.value = null
  formData.value = {
    name: '',
    icon: '',
    sort: 0,
    description: ''
  }
  modalVisible.value = true
}

// 格式化时间
const formatDateTime = (dateString: string) => {
  if (!dateString) return '-'
  const date = new Date(dateString)
  if (isNaN(date.getTime())) return '-'

  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')

  return `${year}-${month}-${day}`
}

// 状态切换
const handleStatusChange = async (record: any, value: boolean) => {
  try {
    // 这里可以调用API更新状态
    console.log('切换状态:', record.name, value)
    Message.success(`${record.name} 状态已${value ? '启用' : '禁用'}`)
  } catch (error) {
    console.error('状态切换失败:', error)
    Message.error('状态切换失败')
  }
}

const editCategory = (record: any) => {
  isEdit.value = true
  currentEditId.value = record.id

  formData.value = {
    name: record.name,
    icon: record.icon || '',
    sort: record.sort_order || 0,
    description: record.description || ''
  }
  modalVisible.value = true
}



const deleteCategory = async (record: any) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除分类"${record.name}"吗？删除后该分类下的所有素材将移至未分类。`,
    onOk: async () => {
      try {
        loading.value = true

        // 调用删除API
        const { getApiBaseUrl } = await import('../../config/api')
        const response = await fetch(`${getApiBaseUrl()}/api/material/categories/${record.id}`, {
          method: 'DELETE'
        })

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }

        const result = await response.json()

        if (result.success) {
          Message.success('删除成功')
          await fetchCategoryTree()
        } else {
          throw new Error(result.msg || '删除失败')
        }
      } catch (error) {
        console.error('删除分类失败:', error)
        Message.error('删除失败')
      } finally {
        loading.value = false
      }
    }
  })
}

const handleSubmit = async () => {
  if (!formData.value.name.trim()) {
    Message.error('请输入分类名称')
    return
  }

  try {
    loading.value = true

    if (isEdit.value && currentEditId.value) {
      // 编辑分类
      const updateData = {
        name: formData.value.name,
        icon: formData.value.icon,
        sort_order: formData.value.sort,
        description: formData.value.description
      }
      // 调用更新API
      const { getApiBaseUrl } = await import('../../config/api')
      const response = await fetch(`${getApiBaseUrl()}/api/material/categories/${currentEditId.value}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updateData)
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const result = await response.json()

      if (result.success) {
        Message.success('编辑成功')
      } else {
        throw new Error(result.msg || '编辑失败')
      }
    } else {
      // 新增分类
      const createData = {
        name: formData.value.name,
        icon: formData.value.icon,
        sort_order: formData.value.sort,
        description: formData.value.description
      }

      // 调用创建API
      const { getApiBaseUrl } = await import('../../config/api')
      const response = await fetch(`${getApiBaseUrl()}/api/material/categories`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(createData)
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const result = await response.json()

      if (result.success) {
        Message.success('新增成功')
      } else {
        throw new Error(result.msg || '新增失败')
      }
    }

    modalVisible.value = false
    await fetchCategoryTree()
  } catch (error) {
    console.error('提交失败:', error)
    Message.error(isEdit.value ? '编辑失败' : '新增失败')
  } finally {
    loading.value = false
  }
}

const handleCancel = () => {
  modalVisible.value = false
}

// 获取分类数据
const fetchCategoryTree = async () => {
  try {
    loading.value = true
    // 使用统一的API配置
    const { getEndpointUrl } = await import('../../config/api')
    const response = await fetch(getEndpointUrl('MATERIAL_CATEGORIES'))

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const result = await response.json()

    if (result.success) {
      // 转换数据格式以兼容现有组件
      categoryTree.value = result.data.map((item: any) => ({
        key: item.id.toString(),
        title: item.name,
        icon: item.icon,
        sort_order: item.sort_order,
        description: item.description,
        count: item.count,
        created_at: item.created_at
      }))
      console.log('分类数据加载成功:', categoryTree.value)
    } else {
      console.error('API返回错误:', result.msg)
      categoryTree.value = []
    }



    // 自动展开所有一级分类
    expandedKeys.value = categoryTree.value.map(item => item.key)
  } catch (error) {
    console.error('获取分类数据失败:', error)
    Message.error('获取分类数据失败')
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  fetchCategoryTree()
})
</script>

<style scoped>
.material-category {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0;
  color: #262626;
}

/* 表格样式优化 */
:deep(.arco-table-th) {
  background-color: #fafafa;
  font-weight: 500;
}

:deep(.arco-table-td) {
  border-bottom: 1px solid #f0f0f0;
}

:deep(.arco-table-tr:hover .arco-table-td) {
  background-color: #f9f9f9;
}
</style>
