import { request } from '@/utils/request'

// 获取项目列表
export const getProjectList = (params: any) => {
  return request.get('/api/project/list', { params })
}

// 获取项目详情
export const getProjectDetail = (id: number) => {
  return request.get(`/api/project/${id}`)
}

// 创建项目
export const createProject = (data: any) => {
  return request.post('/api/project', data)
}

// 更新项目
export const updateProject = (id: number, data: any) => {
  return request.put(`/api/project/${id}`, data)
}

// 删除项目
export const deleteProject = (id: number) => {
  return request.delete(`/api/project/${id}`)
}

// 获取项目分类和阶段枚举
export const getProjectEnums = () => {
  return request.get('/api/project/enums/all')
}

// 项目相关类型定义
export interface Project {
  id: number
  name: string
  category: string
  start_date: string
  current_stage: string
  product_doc_url?: string
  ui_design_url?: string
  participants: Participant[]
  progress_description?: string
  launch_date?: string
  user_id: number
  creator_name?: string
  created_at: string
  updated_at: string
}

export interface Participant {
  name: string
  role: string
}

export interface CreateProjectData {
  name: string
  category: string
  start_date: string
  current_stage?: string
  product_doc_url?: string
  ui_design_url?: string
  participants?: Participant[]
  progress_description?: string
  launch_date?: string
}

export interface UpdateProjectData {
  name?: string
  category?: string
  start_date?: string
  current_stage?: string
  product_doc_url?: string
  ui_design_url?: string
  participants?: Participant[]
  progress_description?: string
  launch_date?: string
}

export interface ProjectEnums {
  categories: string[]
  stages: string[]
}
