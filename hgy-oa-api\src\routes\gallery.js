import express from 'express';
import multer from 'multer';
import { query } from '../config/database.js';
import { uploadToQiniu, generateFileKey, deleteFromQiniu, getFileUrl, getPrivateFileUrl } from '../config/qiniu.js';
import { optionalAuth } from '../middleware/auth.js';

const router = express.Router();

// 配置multer用于文件上传
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('只支持图片格式: JPG, PNG, GIF, WEBP'), false);
    }
  }
});

// 获取分类列表（树形结构）
router.get('/categories', async (req, res) => {
  try {
    // 获取所有分类
    const categories = await query(`
      SELECT id, name, parent_id, icon, sort_order, description, is_active, created_at
      FROM display_image_categories
      ORDER BY parent_id ASC, sort_order ASC, id ASC
    `);

    // 构建树形结构
    const categoryMap = new Map();
    const rootCategories = [];

    // 先创建所有分类的映射
    categories.forEach(category => {
      categoryMap.set(category.id, { ...category, children: [] });
    });

    // 构建父子关系
    categories.forEach(category => {
      if (category.parent_id === null) {
        rootCategories.push(categoryMap.get(category.id));
      } else {
        const parent = categoryMap.get(category.parent_id);
        if (parent) {
          parent.children.push(categoryMap.get(category.id));
        }
      }
    });

    res.success(rootCategories, '获取分类列表成功');
  } catch (error) {
    console.error('获取分类列表失败:', error);
    res.error('获取分类列表失败', 500);
  }
});

// 获取一级分类列表
router.get('/categories/primary', async (req, res) => {
  try {
    const categories = await query(`
      SELECT id, name, icon, sort_order, description
      FROM display_image_categories 
      WHERE parent_id IS NULL AND is_active = 1
      ORDER BY sort_order ASC, id ASC
    `);

    res.success(categories, '获取一级分类成功');
  } catch (error) {
    console.error('获取一级分类失败:', error);
    res.error('获取一级分类失败', 500);
  }
});

// 获取二级分类列表
router.get('/categories/secondary/:parentId', async (req, res) => {
  try {
    const { parentId } = req.params;

    const categories = await query(`
      SELECT id, name, parent_id, sort_order, description
      FROM display_image_categories
      WHERE parent_id = ? AND is_active = 1
      ORDER BY sort_order ASC, id ASC
    `, [parentId]);

    res.success(categories, '获取二级分类成功');
  } catch (error) {
    console.error('获取二级分类失败:', error);
    res.error('获取二级分类失败', 500);
  }
});

// 获取基础分类（排除套件）
router.get('/categories/basic', async (req, res) => {
  try {
    // 获取所有基础分类
    const categories = await query(`
      SELECT id, name, parent_id, icon, sort_order, description, is_active, created_at
      FROM display_image_categories
      WHERE category_type = 'basic' AND is_active = 1
      ORDER BY parent_id ASC, sort_order ASC, id ASC
    `);

    // 构建树形结构
    const categoryMap = new Map();
    const rootCategories = [];

    // 先创建所有分类的映射
    categories.forEach(category => {
      categoryMap.set(category.id, { ...category, children: [] });
    });

    // 构建父子关系
    categories.forEach(category => {
      if (category.parent_id === null) {
        rootCategories.push(categoryMap.get(category.id));
      } else {
        const parent = categoryMap.get(category.parent_id);
        if (parent) {
          parent.children.push(categoryMap.get(category.id));
        }
      }
    });

    res.success(rootCategories, '获取基础分类成功');
  } catch (error) {
    console.error('获取基础分类失败:', error);
    res.error('获取基础分类失败', 500);
  }
});

// 获取套件分类
router.get('/categories/suite', async (req, res) => {
  try {
    // 获取所有套件分类
    const categories = await query(`
      SELECT id, name, parent_id, icon, sort_order, description, is_active, created_at
      FROM display_image_categories
      WHERE category_type = 'suite' AND is_active = 1
      ORDER BY parent_id ASC, sort_order ASC, id ASC
    `);

    // 构建树形结构
    const categoryMap = new Map();
    const rootCategories = [];

    // 先创建所有分类的映射
    categories.forEach(category => {
      categoryMap.set(category.id, { ...category, children: [] });
    });

    // 构建父子关系
    categories.forEach(category => {
      if (category.parent_id === null) {
        rootCategories.push(categoryMap.get(category.id));
      } else {
        const parent = categoryMap.get(category.parent_id);
        if (parent) {
          parent.children.push(categoryMap.get(category.id));
        }
      }
    });

    res.success(rootCategories, '获取套件分类成功');
  } catch (error) {
    console.error('获取套件分类失败:', error);
    res.error('获取套件分类失败', 500);
  }
});

// 创建分类
router.post('/categories', async (req, res) => {
  try {
    const { name, parent_id, icon, sort_order = 0, description, is_active = 1 } = req.body;
    
    if (!name) {
      return res.error('分类名称不能为空', 400);
    }

    // 检查同级分类名称是否重复
    const existingCategories = await query(
      'SELECT id FROM display_image_categories WHERE name = ? AND parent_id = ?',
      [name, parent_id || null]
    );
    
    if (existingCategories.length > 0) {
      return res.error('同级分类名称已存在', 400);
    }

    const result = await query(`
      INSERT INTO display_image_categories (name, parent_id, icon, sort_order, description, is_active, created_at)
      VALUES (?, ?, ?, ?, ?, ?, NOW())
    `, [name, parent_id || null, icon || null, sort_order, description || null, is_active]);

    res.success({ id: result.insertId }, '创建分类成功');
  } catch (error) {
    console.error('创建分类失败:', error);
    res.error('创建分类失败', 500);
  }
});

// 获取单个分类详细信息
router.get('/categories/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const categories = await query(`
      SELECT id, name, parent_id, icon, sort_order, description, is_active, category_type, created_at
      FROM display_image_categories
      WHERE id = ?
    `, [id]);

    if (categories.length === 0) {
      return res.error('分类不存在', 404);
    }

    res.success(categories[0], '获取分类详情成功');
  } catch (error) {
    console.error('获取分类详情失败:', error);
    res.error('获取分类详情失败', 500);
  }
});

// 更新分类
router.put('/categories/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { name, parent_id, icon, sort_order, description, is_active } = req.body;
    
    if (!name) {
      return res.error('分类名称不能为空', 400);
    }

    // 检查分类是否存在
    const existingCategories = await query('SELECT id FROM display_image_categories WHERE id = ?', [id]);
    if (existingCategories.length === 0) {
      return res.error('分类不存在', 404);
    }

    // 检查同级分类名称是否重复
    const duplicateCategories = await query(
      'SELECT id FROM display_image_categories WHERE name = ? AND parent_id = ? AND id != ?',
      [name, parent_id || null, id]
    );
    
    if (duplicateCategories.length > 0) {
      return res.error('同级分类名称已存在', 400);
    }

    await query(`
      UPDATE display_image_categories SET
      name = ?, parent_id = ?, icon = ?, sort_order = ?, description = ?, is_active = ?
      WHERE id = ?
    `, [name, parent_id || null, icon || null, sort_order, description || null, is_active, id]);

    res.success(null, '更新分类成功');
  } catch (error) {
    console.error('更新分类失败:', error);
    res.error('更新分类失败', 500);
  }
});

// 删除分类
router.delete('/categories/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // 检查是否有子分类
    const childCategories = await query('SELECT id FROM display_image_categories WHERE parent_id = ?', [id]);
    if (childCategories.length > 0) {
      return res.error('该分类下还有子分类，无法删除', 400);
    }

    // 检查是否有图片使用该分类
    const imagesCount = await query(
      'SELECT COUNT(*) as count FROM display_images WHERE primary_category_id = ? OR secondary_category_id = ?',
      [id, id]
    );
    
    if (imagesCount[0].count > 0) {
      return res.error('该分类下还有图片，无法删除', 400);
    }

    await query('DELETE FROM display_image_categories WHERE id = ?', [id]);

    res.success(null, '删除分类成功');
  } catch (error) {
    console.error('删除分类失败:', error);
    res.error('删除分类失败', 500);
  }
});

// 获取图片列表（带多分类信息）
router.get('/images/multi-category', async (req, res) => {
  try {
    const {
      pageNum = 1,
      pageSize = 10,
      keyword = '',
      categoryId = '',
      isPublic = ''
    } = req.query;

    // 基础查询
    let sql = `
      SELECT DISTINCT
        gi.id, gi.title, gi.filename, gi.original_name, gi.file_path, gi.thumbnail_path,
        gi.file_size, gi.width, gi.height, gi.format, gi.description, gi.material_preparation,
        gi.download_count, gi.view_count, gi.is_public, gi.is_featured,
        gi.created_at, gi.updated_at, gi.upload_user_id,
        u.nickname as upload_user_nickname, u.username as upload_user
      FROM display_images gi
      LEFT JOIN system_users u ON gi.upload_user_id = u.id
    `;

    const params = [];
    let whereConditions = ['gi.deleted_at IS NULL'];

    // 如果有分类筛选，需要关联分类表
    if (categoryId) {
      sql += ` INNER JOIN gallery_image_categories gic ON gi.id = gic.image_id`;
      whereConditions.push('gic.category_id = ?');
      params.push(categoryId);
    }

    // 关键词搜索
    if (keyword) {
      whereConditions.push('gi.title LIKE ?');
      params.push(`%${keyword}%`);
    }

    // 公开状态筛选
    if (isPublic !== '') {
      whereConditions.push('gi.is_public = ?');
      params.push(isPublic);
    }

    // 添加WHERE条件
    if (whereConditions.length > 0) {
      sql += ` WHERE ${whereConditions.join(' AND ')}`;
    }

    // 分页
    const limit = parseInt(pageSize);
    const offset = (parseInt(pageNum) - 1) * limit;
    sql += ` ORDER BY gi.created_at DESC LIMIT ${limit} OFFSET ${offset}`;

    const images = await query(sql, params);

    // 为每个图片获取分类和文案信息
    for (let image of images) {
      // 获取分类
      const categories = await query(`
        SELECT c.id, c.name, c.parent_id
        FROM gallery_image_categories gic
        JOIN display_image_categories c ON gic.category_id = c.id
        WHERE gic.image_id = ?
        ORDER BY c.parent_id ASC, c.sort_order ASC
      `, [image.id]);

      // 获取关联文案
      const documents = await query(`
        SELECT d.id, d.title, d.filename, d.file_type, d.file_path, d.qiniu_key, d.remark
        FROM gallery_image_documents gid
        JOIN display_documents d ON gid.document_id = d.id
        WHERE gid.image_id = ? AND d.deleted_at IS NULL
      `, [image.id]);

      // 获取关联套件
      const suites = await query(`
        SELECT s.id, s.name, s.description
        FROM gallery_suite_images gsi
        JOIN gallery_suites s ON gsi.suite_id = s.id
        WHERE gsi.image_id = ? AND s.deleted_at IS NULL
        ORDER BY gsi.sort_order ASC
      `, [image.id]);

      image.categories = categories;
      image.documents = documents;
      image.suites = suites;
      image.suite_id = suites.length > 0 ? suites[0].id : null;
    }

    // 获取总数
    let countSql = `
      SELECT COUNT(DISTINCT gi.id) as total
      FROM display_images gi
    `;

    if (categoryId) {
      countSql += ` INNER JOIN gallery_image_categories gic ON gi.id = gic.image_id`;
    }

    if (whereConditions.length > 0) {
      countSql += ` WHERE ${whereConditions.join(' AND ')}`;
    }

    const totalResult = await query(countSql, params);
    const total = totalResult[0].total;

    res.success({
      records: images,
      total,
      pageNum: parseInt(pageNum),
      pageSize: parseInt(pageSize)
    }, '获取图片列表成功');

  } catch (error) {
    console.error('获取图片列表失败:', error);
    res.error('获取图片列表失败', 500);
  }
});

// 获取图片列表（原有API保持兼容）
router.get('/images', async (req, res) => {
  try {
    const { 
      pageNum = 1, 
      pageSize = 10, 
      keyword = '', 
      primaryCategory = '', 
      secondaryCategory = '',
      isPublic = ''
    } = req.query;

    let sql = `
      SELECT
        gi.id, gi.title, gi.filename, gi.original_name, gi.file_path, gi.thumbnail_path,
        gi.file_size, gi.width, gi.height, gi.format, gi.description,
        gi.download_count, gi.view_count, gi.is_public, gi.is_featured,
        gi.created_at, gi.updated_at,
        gi.primary_category_id, gi.secondary_category_id,
        pc.name as primary_category,
        sc.name as secondary_category,
        gi.upload_user_id
      FROM display_images gi
      LEFT JOIN display_image_categories pc ON gi.primary_category_id = pc.id
      LEFT JOIN display_image_categories sc ON gi.secondary_category_id = sc.id
      WHERE gi.deleted_at IS NULL
    `;
    
    const params = [];

    // 关键词搜索
    if (keyword) {
      sql += ` AND gi.title LIKE ?`;
      params.push(`%${keyword}%`);
    }

    // 一级分类筛选
    if (primaryCategory) {
      sql += ` AND gi.primary_category_id = ?`;
      params.push(primaryCategory);
    }

    // 二级分类筛选
    if (secondaryCategory) {
      sql += ` AND gi.secondary_category_id = ?`;
      params.push(secondaryCategory);
    }

    // 公开状态筛选
    if (isPublic !== '') {
      sql += ` AND gi.is_public = ?`;
      params.push(isPublic);
    }

    // 分页 - MySQL不支持LIMIT和OFFSET使用参数绑定，需要直接拼接
    const limit = parseInt(pageSize);
    const offset = (parseInt(pageNum) - 1) * limit;
    sql += ` ORDER BY gi.created_at DESC LIMIT ${limit} OFFSET ${offset}`;

    const images = await query(sql, params);

    // 获取总数
    let countSql = `
      SELECT COUNT(*) as total
      FROM display_images gi
      WHERE 1=1
    `;
    const countParams = [];

    if (keyword) {
      countSql += ` AND gi.title LIKE ?`;
      countParams.push(`%${keyword}%`);
    }

    if (primaryCategory) {
      countSql += ` AND gi.primary_category_id = ?`;
      countParams.push(primaryCategory);
    }

    if (secondaryCategory) {
      countSql += ` AND gi.secondary_category_id = ?`;
      countParams.push(secondaryCategory);
    }

    if (isPublic !== '') {
      countSql += ` AND gi.is_public = ?`;
      countParams.push(isPublic);
    }

    const totalResult = await query(countSql, countParams);
    const total = totalResult[0].total;

    // 为每个图片生成新的私有URL（如果有qiniu_key）
    const imagesWithPrivateUrls = images.map(image => {
      if (image.qiniu_key) {
        try {
          // 生成新的私有URL，有效期24小时
          const privateUrl = getPrivateFileUrl(image.qiniu_key, 86400);
          return {
            ...image,
            file_path: privateUrl,
            thumbnail_path: privateUrl
          };
        } catch (error) {
          console.error('生成私有URL失败，使用原URL:', error);
          // 如果生成失败，保持原URL
          return image;
        }
      }
      return image;
    });

    res.success({
      records: imagesWithPrivateUrls,
      total,
      pageNum: parseInt(pageNum),
      pageSize: parseInt(pageSize)
    }, '获取图片列表成功');

  } catch (error) {
    console.error('获取图片列表失败:', error);
    res.error('获取图片列表失败', 500);
  }
});

// 获取首页图片列表（公开的图片）
router.get('/images/public', async (req, res) => {
  try {
    const {
      pageNum = 1,
      pageSize = 20,
      keyword = '',
      categoryId = '',
      suite_category_id = '',
      suite_primary_category_id = '',
      has_suite = ''
    } = req.query;

    // 基础查询
    let sql = `
      SELECT DISTINCT
        gi.id, gi.title, gi.filename, gi.original_name, gi.file_path, gi.thumbnail_path,
        gi.file_size, gi.width, gi.height, gi.format, gi.description, gi.material_preparation,
        gi.download_count, gi.view_count, gi.is_public, gi.is_featured,
        gi.created_at, gi.updated_at, gi.upload_user_id,
        u.nickname as upload_user_nickname, u.username as upload_user
      FROM display_images gi
      LEFT JOIN system_users u ON gi.upload_user_id = u.id
    `;

    const params = [];
    let whereConditions = ['gi.is_public = 1', 'gi.deleted_at IS NULL'];

    // 如果有分类筛选，需要关联分类表
    if (categoryId) {
      // 先验证分类ID是否存在
      const categoryExists = await query(`
        SELECT id FROM display_image_categories WHERE id = ?
      `, [categoryId]);

      if (categoryExists.length === 0) {
        // 如果分类不存在，返回空结果而不是错误
        return res.success({
          records: [],
          total: 0,
          pageNum: parseInt(pageNum),
          pageSize: parseInt(pageSize)
        }, '获取图片列表成功');
      }

      // 检查该分类是否有关联的图片
      const hasImages = await query(`
        SELECT COUNT(*) as count
        FROM gallery_image_categories gic
        JOIN display_image_categories gc ON gic.category_id = gc.id
        JOIN display_images gi ON gic.image_id = gi.id
        WHERE (gc.id = ? OR gc.parent_id = ?) AND gi.is_public = 1 AND gi.deleted_at IS NULL
      `, [categoryId, categoryId]);

      if (hasImages[0].count === 0) {
        // 如果分类存在但没有关联的公开图片，返回空结果
        return res.success({
          records: [],
          total: 0,
          pageNum: parseInt(pageNum),
          pageSize: parseInt(pageSize)
        }, '获取图片列表成功');
      }

      sql += ` INNER JOIN gallery_image_categories gic ON gi.id = gic.image_id`;
      sql += ` INNER JOIN display_image_categories gc ON gic.category_id = gc.id`;

      // 支持父子分类筛选：如果选择父分类，显示所有子分类的图片；如果选择子分类，只显示该子分类的图片
      whereConditions.push('(gc.id = ? OR gc.parent_id = ?)');
      params.push(categoryId, categoryId);
    }

    // 关键词搜索
    if (keyword) {
      whereConditions.push('gi.title LIKE ?');
      params.push(`%${keyword}%`);
    }

    // 套件分类筛选
    if (suite_category_id) {
      // 通过套件分类筛选图片
      sql += ` INNER JOIN gallery_suite_images gsi ON gi.id = gsi.image_id`;
      sql += ` INNER JOIN gallery_suites gs ON gsi.suite_id = gs.id`;
      whereConditions.push('gs.category_id = ?');
      params.push(suite_category_id);
    } else if (suite_primary_category_id) {
      // 通过套件一级分类筛选图片
      sql += ` INNER JOIN gallery_suite_images gsi ON gi.id = gsi.image_id`;
      sql += ` INNER JOIN gallery_suites gs ON gsi.suite_id = gs.id`;
      sql += ` INNER JOIN display_suits gsc ON gs.category_id = gsc.id`;
      whereConditions.push('(gsc.id = ? OR gsc.parent_id = ?)');
      params.push(suite_primary_category_id, suite_primary_category_id);
    } else if (has_suite) {
      // 只显示有套件关联的图片
      sql += ` INNER JOIN gallery_suite_images gsi ON gi.id = gsi.image_id`;
      sql += ` INNER JOIN gallery_suites gs ON gsi.suite_id = gs.id`;
      whereConditions.push('gs.deleted_at IS NULL');
    }

    // 添加WHERE条件
    if (whereConditions.length > 0) {
      sql += ` WHERE ${whereConditions.join(' AND ')}`;
    }

    // 分页
    const limit = parseInt(pageSize);
    const offset = (parseInt(pageNum) - 1) * limit;
    sql += ` ORDER BY gi.created_at DESC LIMIT ${limit} OFFSET ${offset}`;

    const images = await query(sql, params);

    // 为每个图片获取分类和文案信息
    for (let image of images) {
      // 获取分类
      const categories = await query(`
        SELECT c.id, c.name, c.parent_id
        FROM gallery_image_categories gic
        JOIN display_image_categories c ON gic.category_id = c.id
        WHERE gic.image_id = ?
        ORDER BY c.parent_id ASC, c.sort_order ASC
      `, [image.id]);

      // 获取关联文案
      const documents = await query(`
        SELECT d.id, d.title, d.filename, d.file_type, d.file_path, d.qiniu_key, d.remark
        FROM gallery_image_documents gid
        JOIN display_documents d ON gid.document_id = d.id
        WHERE gid.image_id = ? AND d.deleted_at IS NULL
      `, [image.id]);

      // 获取关联套件
      const suites = await query(`
        SELECT s.id, s.name, s.description
        FROM gallery_suite_images gsi
        JOIN gallery_suites s ON gsi.suite_id = s.id
        WHERE gsi.image_id = ? AND s.deleted_at IS NULL
        ORDER BY gsi.sort_order ASC
      `, [image.id]);

      image.categories = categories;
      image.documents = documents;
      image.suites = suites;
      image.suite_id = suites.length > 0 ? suites[0].id : null;
    }

    // 获取总数
    let countSql = `
      SELECT COUNT(DISTINCT gi.id) as total
      FROM display_images gi
    `;

    if (categoryId) {
      countSql += ` INNER JOIN gallery_image_categories gic ON gi.id = gic.image_id`;
      countSql += ` INNER JOIN display_image_categories gc ON gic.category_id = gc.id`;
    } else if (suite_category_id) {
      // 通过套件分类筛选图片
      countSql += ` INNER JOIN gallery_suite_images gsi ON gi.id = gsi.image_id`;
      countSql += ` INNER JOIN gallery_suites gs ON gsi.suite_id = gs.id`;
    } else if (suite_primary_category_id) {
      // 通过套件一级分类筛选图片
      countSql += ` INNER JOIN gallery_suite_images gsi ON gi.id = gsi.image_id`;
      countSql += ` INNER JOIN gallery_suites gs ON gsi.suite_id = gs.id`;
      countSql += ` INNER JOIN display_suits gsc ON gs.category_id = gsc.id`;
    } else if (has_suite) {
      // 只显示有套件关联的图片
      countSql += ` INNER JOIN gallery_suite_images gsi ON gi.id = gsi.image_id`;
      countSql += ` INNER JOIN gallery_suites gs ON gsi.suite_id = gs.id`;
    }

    if (whereConditions.length > 0) {
      countSql += ` WHERE ${whereConditions.join(' AND ')}`;
    }

    const totalResult = await query(countSql, params);
    const total = totalResult[0].total;

    // 为每个图片生成新的私有URL（如果有qiniu_key）
    const imagesWithPrivateUrls = images.map(image => {
      if (image.qiniu_key) {
        try {
          // 生成新的私有URL，有效期24小时
          const privateUrl = getPrivateFileUrl(image.qiniu_key, 86400);
          return {
            ...image,
            file_path: privateUrl,
            thumbnail_path: privateUrl
          };
        } catch (error) {
          console.error('生成私有URL失败，使用原URL:', error);
          // 如果生成失败，保持原URL
          return image;
        }
      }
      return image;
    });

    res.success({
      records: imagesWithPrivateUrls,
      total,
      pageNum: parseInt(pageNum),
      pageSize: parseInt(pageSize)
    }, '获取公开图片列表成功');

  } catch (error) {
    console.error('获取公开图片列表失败:', error);
    res.error('获取公开图片列表失败', 500);
  }
});

// 上传图片到七牛云并保存到数据库
router.post('/images', optionalAuth, upload.single('file'), async (req, res) => {
  try {
    if (!req.file) {
      return res.error('请选择要上传的图片文件', 400);
    }

    const {
      title,
      description = '',
      material_preparation = '',
      primary_category_id,
      secondary_category_id,
      basic_categories = '', // 基础分类IDs，逗号分隔
      suite_id = '', // 套件ID
      suite_category_id = '', // 套件分类ID
      document_ids = '', // 文案IDs，逗号分隔
      is_public = 1,
      is_featured = 0,
      replace_image_id // 如果提供此参数，表示这是替换操作
    } = req.body;

    // 使用登录用户的ID，如果没有登录用户则查找默认用户
    let finalUploadUserId;
    if (req.user && req.user.id) {
      finalUploadUserId = req.user.id;
      console.log('使用登录用户ID:', finalUploadUserId, '用户名:', req.user.username);
    } else {
      // 如果没有登录用户，查找一个存在的用户ID作为默认值
      try {
        const users = await query('SELECT id FROM system_users WHERE is_active = 1 ORDER BY id LIMIT 1');
        if (users.length > 0) {
          finalUploadUserId = users[0].id;
          console.log('使用默认用户ID:', finalUploadUserId);
        } else {
          return res.error('系统中没有可用的用户，请先创建用户', 400);
        }
      } catch (error) {
        console.error('查询用户失败:', error);
        return res.error('查询用户信息失败', 500);
      }
    }

    if (!title) {
      return res.error('图片标题不能为空', 400);
    }

    // 生成七牛云文件key
    const fileKey = generateFileKey(req.file.originalname, 'gallery');
    console.log('生成的文件key:', fileKey);

    // 上传到七牛云
    console.log('开始上传到七牛云...');
    const qiniuResult = await uploadToQiniu(req.file.buffer, fileKey, req.file.originalname);
    console.log('七牛云上传结果:', qiniuResult);

    // 为私有Bucket生成带签名的下载URL
    let fileUrl;
    try {
      fileUrl = getPrivateFileUrl(qiniuResult.key, 86400 * 365); // 1年有效期
      console.log('生成的私有URL:', fileUrl);
    } catch (error) {
      console.error('生成私有URL失败，使用公开URL:', error);
      fileUrl = qiniuResult.url; // 降级使用公开URL
    }

    const format = req.file.mimetype.split('/')[1].toUpperCase();

    let result;
    let imageInfo;

    if (replace_image_id) {
      // 替换操作：更新现有记录
      const updateSql = `
        UPDATE display_images SET
          title = ?, filename = ?, original_name = ?, file_path = ?, thumbnail_path = ?,
          file_size = ?, width = ?, height = ?, format = ?, description = ?, material_preparation = ?,
          primary_category_id = ?, secondary_category_id = ?,
          is_public = ?, is_featured = ?, qiniu_key = ?, qiniu_hash = ?,
          updated_at = NOW()
        WHERE id = ?
      `;

      result = await query(updateSql, [
        title,
        fileKey.split('/').pop(),
        req.file.originalname,
        fileUrl,
        fileUrl,
        qiniuResult.size,
        qiniuResult.width,
        qiniuResult.height,
        format,
        description,
        material_preparation,
        primary_category_id || null,
        secondary_category_id || null,
        is_public,
        is_featured,
        qiniuResult.key,
        qiniuResult.hash,
        replace_image_id
      ]);

      imageInfo = {
        id: replace_image_id,
        title,
        filename: fileKey.split('/').pop(),
        original_name: req.file.originalname,
        file_path: fileUrl,
        thumbnail_path: fileUrl,
        file_size: qiniuResult.size,
        width: qiniuResult.width,
        height: qiniuResult.height,
        format,
        description,
        primary_category_id,
        secondary_category_id,
        is_public: parseInt(is_public),
        is_featured: parseInt(is_featured),
        qiniu_key: qiniuResult.key,
        qiniu_hash: qiniuResult.hash
      };
    } else {
      // 新增操作：插入新记录
      const insertSql = `
        INSERT INTO display_images (
          title, filename, original_name, file_path, thumbnail_path,
          file_size, width, height, format, description, material_preparation,
          primary_category_id, secondary_category_id, upload_user_id,
          is_public, is_featured, qiniu_key, qiniu_hash,
          created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
      `;

      result = await query(insertSql, [
        title,
        fileKey.split('/').pop(),
        req.file.originalname,
        fileUrl,
        fileUrl,
        qiniuResult.size,
        qiniuResult.width || 0,  // 如果没有宽度信息，使用0作为默认值
        qiniuResult.height || 0, // 如果没有高度信息，使用0作为默认值
        format,
        description,
        material_preparation,
        primary_category_id || null,
        secondary_category_id || null,
        finalUploadUserId,
        is_public,
        is_featured,
        qiniuResult.key,
        qiniuResult.hash
      ]);

      imageInfo = {
        id: result.insertId,
        title,
        filename: fileKey.split('/').pop(),
        original_name: req.file.originalname,
        file_path: fileUrl,
        thumbnail_path: fileUrl,
        file_size: qiniuResult.size,
        width: qiniuResult.width,
        height: qiniuResult.height,
        format,
        description,
        primary_category_id,
        secondary_category_id,
        is_public: parseInt(is_public),
        is_featured: parseInt(is_featured),
        qiniu_key: qiniuResult.key,
        qiniu_hash: qiniuResult.hash
      };
    }

    // 处理多分类关联
    const imageId = imageInfo.id;

    // 清除现有的分类关联和套件关联（如果是替换操作）
    if (replace_image_id) {
      await query('DELETE FROM gallery_image_categories WHERE image_id = ?', [imageId]);
      await query('DELETE FROM gallery_suite_images WHERE image_id = ?', [imageId]);
    }

    // 添加基础分类关联
    if (basic_categories) {
      const basicCategoryIds = basic_categories.split(',').filter(id => id.trim());
      for (const categoryId of basicCategoryIds) {
        await query(`
          INSERT IGNORE INTO gallery_image_categories (image_id, category_id, category_type)
          VALUES (?, ?, 'basic')
        `, [imageId, categoryId.trim()]);
      }
    }

    // 添加套件关联
    if (suite_id) {
      await query(`
        INSERT IGNORE INTO gallery_suite_images (suite_id, image_id, sort_order)
        VALUES (?, ?, 0)
      `, [suite_id, imageId]);
    } else if (suite_category_id) {
      // 通过套件分类ID关联：找到该分类下的所有套件
      const suites = await query(`
        SELECT id FROM gallery_suites
        WHERE category_id = ? AND deleted_at IS NULL AND is_visible = 1
      `, [suite_category_id]);

      console.log(`找到分类 ${suite_category_id} 下的套件:`, suites.length);

      if (suites.length > 0) {
        // 关联到该分类下的所有套件
        for (const suite of suites) {
          await query(`
            INSERT IGNORE INTO gallery_suite_images (suite_id, image_id, sort_order)
            VALUES (?, ?, 0)
          `, [suite.id, imageId]);
          console.log(`关联图片 ${imageId} 到套件 ${suite.id}`);
        }
      } else {
        // 如果该分类下没有套件，创建一个默认套件
        console.log(`分类 ${suite_category_id} 下没有套件，创建默认套件`);

        // 获取分类信息
        const categoryInfo = await query(`
          SELECT name FROM display_suits WHERE id = ?
        `, [suite_category_id]);

        if (categoryInfo.length > 0) {
          const categoryName = categoryInfo[0].name;
          const defaultSuiteName = `${categoryName}套件`;

          // 创建默认套件
          const suiteResult = await query(`
            INSERT INTO gallery_suites (name, description, category_id, is_visible, sort_order, created_at, updated_at)
            VALUES (?, ?, ?, 1, 0, NOW(), NOW())
          `, [defaultSuiteName, `${categoryName}相关的设计素材`, suite_category_id]);

          const newSuiteId = suiteResult.insertId;
          console.log(`创建默认套件: ${defaultSuiteName} (ID: ${newSuiteId})`);

          // 关联图片到新创建的套件
          await query(`
            INSERT IGNORE INTO gallery_suite_images (suite_id, image_id, sort_order)
            VALUES (?, ?, 0)
          `, [newSuiteId, imageId]);
          console.log(`关联图片 ${imageId} 到新套件 ${newSuiteId}`);
        }
      }
    }

    // 处理文案关联
    if (replace_image_id) {
      await query('DELETE FROM gallery_image_documents WHERE image_id = ?', [imageId]);
    }

    if (document_ids) {
      const documentIdList = document_ids.split(',').filter(id => id.trim());
      for (const documentId of documentIdList) {
        await query(`
          INSERT IGNORE INTO gallery_image_documents (image_id, document_id)
          VALUES (?, ?)
        `, [imageId, documentId.trim()]);
      }
    }

    const message = replace_image_id ? '图片替换成功' : '图片上传成功';
    res.success(imageInfo, message);

  } catch (error) {
    console.error('图片上传失败:', error);
    res.error('图片上传失败: ' + error.message, 500);
  }
});

// 软删除图片
router.delete('/images/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // 先检查图片是否存在且未被删除
    const imageResult = await query('SELECT id, title FROM display_images WHERE id = ? AND deleted_at IS NULL', [id]);

    if (imageResult.length === 0) {
      return res.error('图片不存在或已被删除', 404);
    }

    // 软删除：设置删除时间
    await query('UPDATE display_images SET deleted_at = NOW() WHERE id = ?', [id]);

    res.success(null, '图片删除成功');

  } catch (error) {
    console.error('删除图片失败:', error);
    res.error('删除图片失败', 500);
  }
});

// 永久删除图片（管理员功能）
router.delete('/images/:id/permanent', async (req, res) => {
  try {
    const { id } = req.params;

    // 先获取图片信息
    const imageResult = await query('SELECT qiniu_key, title FROM display_images WHERE id = ?', [id]);

    if (imageResult.length === 0) {
      return res.error('图片不存在', 404);
    }

    const image = imageResult[0];

    // 从七牛云删除文件
    if (image.qiniu_key) {
      try {
        await deleteFromQiniu(image.qiniu_key);
      } catch (qiniuError) {
        console.warn('七牛云删除文件失败:', qiniuError);
        // 继续删除数据库记录，即使七牛云删除失败
      }
    }

    // 从数据库永久删除记录
    await query('DELETE FROM display_images WHERE id = ?', [id]);

    res.success(null, '图片永久删除成功');

  } catch (error) {
    console.error('永久删除图片失败:', error);
    res.error('永久删除图片失败', 500);
  }
});

// 更新图片信息
router.put('/images/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const {
      title,
      description,
      material_preparation,
      primary_category_id,
      secondary_category_id,
      basic_categories = '', // 新的基础分类系统
      suite_category_id = '', // 套件分类ID
      document_ids,
      is_public,
      is_featured
    } = req.body;

    if (!title) {
      return res.error('图片标题不能为空', 400);
    }

    // 更新图片基本信息
    const sql = `
      UPDATE display_images
      SET title = ?, description = ?, material_preparation = ?, primary_category_id = ?,
          secondary_category_id = ?, is_public = ?, is_featured = ?, updated_at = NOW()
      WHERE id = ?
    `;

    const result = await query(sql, [
      title,
      description,
      material_preparation || null,
      primary_category_id || null,
      secondary_category_id || null,
      is_public,
      is_featured,
      id
    ]);

    if (result.affectedRows === 0) {
      return res.error('图片不存在', 404);
    }

    // 更新关联文案
    if (document_ids !== undefined) {
      // 先删除现有关联
      await query('DELETE FROM gallery_image_documents WHERE image_id = ?', [id]);

      // 添加新的关联
      if (document_ids && document_ids.length > 0) {
        const docIdArray = typeof document_ids === 'string' ? document_ids.split(',').filter(id => id) : document_ids;
        const insertPromises = docIdArray.map(docId =>
          query('INSERT INTO gallery_image_documents (image_id, document_id) VALUES (?, ?)', [id, docId])
        );
        await Promise.all(insertPromises);
      }
    }

    // 更新基础分类关联
    if (basic_categories !== undefined) {
      // 先删除现有的基础分类关联
      await query('DELETE FROM gallery_image_categories WHERE image_id = ?', [id]);

      // 添加新的基础分类关联
      if (basic_categories) {
        const categoryIds = basic_categories.split(',').filter(id => id);
        for (const categoryId of categoryIds) {
          await query(`
            INSERT IGNORE INTO gallery_image_categories (image_id, category_id)
            VALUES (?, ?)
          `, [id, categoryId]);
        }
      }
    }

    // 更新套件分类关联
    if (suite_category_id !== undefined) {
      // 先删除现有的套件关联
      await query('DELETE FROM gallery_suite_images WHERE image_id = ?', [id]);

      // 添加新的套件关联
      if (suite_category_id) {
        // 通过套件分类ID关联：找到该分类下的所有套件
        const suites = await query(`
          SELECT id FROM gallery_suites
          WHERE category_id = ? AND deleted_at IS NULL AND is_visible = 1
        `, [suite_category_id]);

        console.log(`编辑时找到分类 ${suite_category_id} 下的套件:`, suites.length);

        if (suites.length > 0) {
          // 关联到该分类下的所有套件
          for (const suite of suites) {
            await query(`
              INSERT IGNORE INTO gallery_suite_images (suite_id, image_id, sort_order)
              VALUES (?, ?, 0)
            `, [suite.id, id]);
            console.log(`编辑时关联图片 ${id} 到套件 ${suite.id}`);
          }
        } else {
          // 如果该分类下没有套件，创建一个默认套件
          console.log(`编辑时分类 ${suite_category_id} 下没有套件，创建默认套件`);

          // 获取分类信息
          const categoryInfo = await query(`
            SELECT name FROM display_suits WHERE id = ?
          `, [suite_category_id]);

          if (categoryInfo.length > 0) {
            const categoryName = categoryInfo[0].name;
            const defaultSuiteName = `${categoryName}套件`;

            // 创建默认套件
            const suiteResult = await query(`
              INSERT INTO gallery_suites (name, description, category_id, is_visible, sort_order, created_at, updated_at)
              VALUES (?, ?, ?, 1, 0, NOW(), NOW())
            `, [defaultSuiteName, `${categoryName}相关的设计素材`, suite_category_id]);

            const newSuiteId = suiteResult.insertId;
            console.log(`编辑时创建默认套件: ${defaultSuiteName} (ID: ${newSuiteId})`);

            // 关联图片到新创建的套件
            await query(`
              INSERT IGNORE INTO gallery_suite_images (suite_id, image_id, sort_order)
              VALUES (?, ?, 0)
            `, [newSuiteId, id]);
            console.log(`编辑时关联图片 ${id} 到新套件 ${newSuiteId}`);
          }
        }
      }
    }

    res.success(null, '图片信息更新成功');

  } catch (error) {
    console.error('更新图片信息失败:', error);
    res.error('更新图片信息失败', 500);
  }
});



// 批量软删除图片
router.post('/images/batch-delete', async (req, res) => {
  try {
    const { ids } = req.body;

    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return res.error('请提供要删除的图片ID列表', 400);
    }

    // 检查图片是否存在且未被删除
    const placeholders = ids.map(() => '?').join(',');
    const imageResult = await query(
      `SELECT id, title FROM display_images WHERE id IN (${placeholders}) AND deleted_at IS NULL`,
      ids
    );

    if (imageResult.length === 0) {
      return res.error('没有找到可删除的图片', 404);
    }

    // 批量软删除
    await query(
      `UPDATE display_images SET deleted_at = NOW() WHERE id IN (${placeholders}) AND deleted_at IS NULL`,
      ids
    );

    res.success(null, `成功删除 ${imageResult.length} 张图片`);

  } catch (error) {
    console.error('批量删除图片失败:', error);
    res.error('批量删除图片失败', 500);
  }
});

export default router;
