<template>
    <div class="wrap">
        <!--        <search-header :cateList="cateList" v-model="keyword" @changeCate="changeCate" @search="onSearch"/>-->
        <comp-cate-list-wrap :data="page.dataList" :cate-list="cateList" :current-cate="currentCate" :no-more="page.noMore"
                             @fetch-data="loadList"
                             @back-cate="backCate"
                             @item-click="handleClick"
                             @select-cate="selectCate"
        ></comp-cate-list-wrap>
    </div>
</template>

<script lang="ts" setup>

import {useEditor} from "@/views/Editor/app";
import {Group, Image, Line, UI} from "leafer-ui";
import {getDefaultName} from "@/views/Editor/utils/utils";
import CompCateListWrap from "@/views/Editor/layouts/panel/leftPanel/wrap/CompCateListWrap.vue";
import usePageMixin from "@/views/Editor/layouts/panel/leftPanel/wrap/mixins/pageMixin";
import {queryElementList, queryElementCategory} from "@/api/editor/materials";
import SearchHeader from "@/components/editorModules/searchHeader.vue";
import {Ellipse, Star} from "@leafer-ui/core";
import isString from "lodash/isString";
import {Arrow} from "@leafer-in/arrow";
const {editor} = useEditor()

const keyword = ref();
const currentCate = ref(null);
const cateList = ref([])

const onSearch = (value,ev) => {
    console.log('value=',value)
    console.log('keyword=',keyword.value)
    console.log('ev=',ev)
}
const { page } = usePageMixin()
page.pageSize = 30
const fetchData = () => {
    queryElementCategory().then(res =>{
        if (res.success) {
            const list = res.data.records

            // 如果没有子分类，直接加载主分类的素材
            if (list.length === 0) {
                // 为元素主分类创建一个虚拟分类项，并加载素材预览
                queryElementList({pageNum: 1, pageSize: 6, query: {categoryId: 21}}).then(elemRes => {
                    if (elemRes.success) {
                        cateList.value = [{
                            id: 21,
                            name: '元素',
                            description: '元素素材',
                            list: elemRes.data.records.map(item => ({
                                ...item,
                                url: item.preview_url || item.file_path
                            }))
                        }]
                    }
                })
            } else {
                // 如果有子分类，为每个子分类加载素材预览
                cateList.value = list.map(cat => ({
                    ...cat,
                    list: [] // 初始化为空，后续可以按需加载
                }))
            }
        }
    })
}
const handleClick = (item) => {

    item.json.name = getDefaultName(editor.contentFrame)
    if (isString(item.json.fill)){
        item.json.fill = [{
            type:'solid',
            color:item.json.fill
        }]
    }
    let group
    if (item.json.tag === 'Arrow'){
        group = new Arrow(item.json)
    }else {
        group = UI.one(item.json)
    }
    editor.add(group)
}
const backCate = () => {
    currentCate.value = null
    page.dataList = []
}
const selectCate = (cate) => {
    currentCate.value = cate
    page.query.categoryId = cate.id
    page.pageNum = 1
    page.noMore = false
    page.dataList = [] // 清空之前的数据
    loadList()
}
const loadList = () => {
    page.query.categoryId = currentCate.value.id
    queryElementList(page).then(res =>{
        if (res.success) {
            const newDataList = res.data.records
            if (newDataList.length > 0) {
                page.dataList.push(...newDataList)
                page.pageNum += 1
            }
            if (page.dataList.length >= res.data.total) {
                page.noMore = true
            } else {
                page.noMore = false
            }
        }
    })
}
// 接收active属性
const props = defineProps<{
  active: boolean
}>()

// 组件挂载时初始化数据
import { onMounted } from 'vue'
onMounted(() => {
  console.log('ElementListWrap 组件挂载，开始初始化...');
  fetchData()
})
</script>
