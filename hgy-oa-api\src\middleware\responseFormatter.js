// 响应格式化中间件
export const responseFormatter = (req, res, next) => {
  // 成功响应
  res.success = (data = null, msg = '操作成功', code = 200) => {
    res.status(200).json({
      success: true,
      code,
      msg,
      data,
      timestamp: new Date().getTime()
    });
  };

  // 错误响应
  res.error = (msg = '操作失败', code = 500, data = null) => {
    res.status(code >= 400 && code < 600 ? code : 500).json({
      success: false,
      code,
      msg,
      data,
      timestamp: new Date().getTime()
    });
  };

  next();
};
