import { query } from '../src/config/database.js';

const createFontsTable = async () => {
  try {
    console.log('🚀 开始创建 fonts 表...\n');

    // 创建 fonts 表
    const createTableSQL = `
      CREATE TABLE IF NOT EXISTS fonts (
        id int(11) NOT NULL AUTO_INCREMENT,
        name varchar(255) NOT NULL COMMENT '字体名称',
        code varchar(255) NOT NULL COMMENT '字体代码',
        preview_url varchar(500) DEFAULT NULL COMMENT '预览图URL',
        download_url varchar(500) DEFAULT NULL COMMENT '下载URL',
        file_size int(11) DEFAULT NULL COMMENT '文件大小(字节)',
        font_family varchar(255) NOT NULL COMMENT '字体族名',
        font_weight varchar(50) DEFAULT 'normal' COMMENT '字体粗细',
        font_style varchar(50) DEFAULT 'normal' COMMENT '字体样式',
        category_id int(11) DEFAULT NULL COMMENT '分类ID',
        tags json DEFAULT NULL COMMENT '标签',
        is_system tinyint(1) DEFAULT 0 COMMENT '是否系统字体：1-是，0-否',
        status tinyint(1) DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
        created_at timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        updated_at timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        PRIMARY KEY (id),
        UNIQUE KEY uk_code (code),
        KEY idx_category_id (category_id),
        KEY idx_is_system (is_system),
        KEY idx_status (status),
        KEY idx_created_at (created_at)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='字体表'
    `;

    await query(createTableSQL);
    console.log('✅ fonts 表创建成功');

    // 插入默认系统字体
    console.log('📝 插入默认系统字体...');
    
    const defaultFonts = [
      ['Arial', 'arial', 'Arial, sans-serif', 1],
      ['Times New Roman', 'times-new-roman', 'Times New Roman, serif', 1],
      ['微软雅黑', 'microsoft-yahei', 'Microsoft Yahei, sans-serif', 1],
      ['宋体', 'simsun', 'SimSun, serif', 1],
      ['黑体', 'simhei', 'SimHei, sans-serif', 1]
    ];

    for (const font of defaultFonts) {
      const sql = `
        INSERT IGNORE INTO fonts (name, code, font_family, is_system, status, created_at, updated_at)
        VALUES (?, ?, ?, ?, 1, NOW(), NOW())
      `;
      await query(sql, font);
      console.log(`   ✅ ${font[0]} 插入成功`);
    }

    console.log('\n🎉 fonts 表初始化完成！');

  } catch (error) {
    console.error('❌ 创建 fonts 表失败:', error);
  }
};

// 执行创建
createFontsTable().then(() => {
  console.log('程序执行完成');
  process.exit(0);
}).catch(error => {
  console.error('程序执行失败:', error);
  process.exit(1);
});
