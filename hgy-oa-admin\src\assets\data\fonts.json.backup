{"list": [{"id": 1, "code": "<PERSON><PERSON>zhengshus<PERSON>", "name": "方正书宋", "preview": "https://wordshub.github.io/free-font/images/font/fangzhengshusong/font.svg", "source": "https://www.foundertype.com/index.php/About/bookAuth/key/my_sysq.html", "download": "https://wordshub.github.io/free-font/assets/font/%E4%B8%AD%E6%96%87/%E6%96%B9%E6%AD%A3%E5%AD%97%E4%BD%93%E7%B3%BB%E5%88%97/%E6%96%B9%E6%AD%A3%E4%B9%A6%E5%AE%8B%E7%AE%80%E4%BD%93.ttf", "desc": "「方正书宋」源自上海印刷技术研究所的宋二，原字形距今已有近60年。最初的设计就是专门针对最常用的小字号正文排版。方正书宋的字形端正清秀，中宫适度。凭着清晰爽目、久读不易疲劳、印刷适性好的优点，长期被作为杂志和书籍的正文���选字体，所以也是一款非常经典的正文宋体。", "version": "", "license": "商免"}, {"id": 2, "code": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "方正仿宋", "preview": "https://wordshub.github.io/free-font/images/font/fangzhengfangsong/font.svg", "source": "https://www.foundertype.com/index.php/About/bookAuth/key/my_sysq.html", "download": "https://wordshub.github.io/free-font/assets/font/%E4%B8%AD%E6%96%87/%E6%96%B9%E6%AD%A3%E5%AD%97%E4%BD%93%E7%B3%BB%E5%88%97/%E6%96%B9%E6%AD%A3%E4%BB%BF%E5%AE%8B%E7%AE%80%E4%BD%93.ttf", "desc": "「仿宋体」产生于20世纪初，由当时的铅字制作者仿宋版书中的瘦细字体而制成。方正仿宋源于铅字时代的字稿，其字身略窄，笔画瘦硬，横竖笔画等粗，起笔处有斜势棱角，折笔处棱角分明，整体字形挺拔俊秀，给人以悦目之感。因清晰美观，容易辨认，长期被用于工程图纸文字和书刊中的引文。", "license": "商免"}, {"id": 3, "code": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "方正黑体", "preview": "https://wordshub.github.io/free-font/images/font/fangzhengheiti/font.svg", "source": "https://www.foundertype.com/index.php/About/bookAuth/key/my_sysq.html", "download": "https://wordshub.github.io/free-font/assets/font/%E4%B8%AD%E6%96%87/%E6%96%B9%E6%AD%A3%E5%AD%97%E4%BD%93%E7%B3%BB%E5%88%97/%E6%96%B9%E6%AD%A3%E9%BB%91%E4%BD%93%E7%AE%80%E4%BD%93.ttf", "desc": "「方正黑体」的设计源于铅字时代的黑体字稿，是一款专门为正文排版设计的黑体。方正黑体的笔画两端留有喇叭口，撇、捺、钩笔画的粗细变化较大，中宫比较紧凑。其字重适中、字形风格中性朴实，不但适用于印刷媒介，还可用于屏幕阅读，是一款非常经典的正文黑体。", "license": "商免"}, {"id": 4, "code": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "方正楷体", "preview": "https://wordshub.github.io/free-font/images/font/fangzhengkaiti/font.svg", "source": "https://www.foundertype.com/index.php/About/bookAuth/key/my_sysq.html", "download": "https://wordshub.github.io/free-font/assets/font/%E4%B8%AD%E6%96%87/%E6%96%B9%E6%AD%A3%E5%AD%97%E4%BD%93%E7%B3%BB%E5%88%97/%E6%96%B9%E6%AD%A3%E6%A5%B7%E4%BD%93%E7%AE%80%E4%BD%93.ttf", "desc": "「方正楷体」源于上海印刷技术研究所“华文楷体”字稿。原“华文楷体”诞生于20世纪40年代，新中国成立后一直是主要使用的铅字楷体。在此期间，上海字模一厂、上海印刷技术研究所等单位进行了数次改良，并添加了简体字。方正楷体的结构秀丽匀称，笔画圆润柔和，横竖笔画的粗细变化不大。这款字体被大量应用于报纸、杂志和书籍中的正文，在教科书中最为常见。", "license": "商免"}, {"id": 5, "code": "pangmenzhengdaobiaotiti", "name": "庞门正道标题体", "preview": "https://wordshub.github.io/free-font/images/font/pangmenzhengdaobiaotiti/font.svg", "source": "https://mp.weixin.qq.com/s/1ccpLCOrIn81JhV9ulwPIQ", "download": "https://wordshub.github.io/free-font/assets/font/%E4%B8%AD%E6%96%87/%E5%BA%9E%E9%97%A8%E6%AD%A3%E9%81%93%E5%AD%97%E4%BD%93%E7%B3%BB%E5%88%97/%E5%BA%9E%E9%97%A8%E6%AD%A3%E9%81%93%E6%A0%87%E9%A2%98%E4%BD%93.ttf", "desc": "「庞门正道标题体」由庞门正道联合了13位设计师于2016年推出的第一款免费商用字体。", "license": "商免"}, {"id": 6, "code": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "庞门正道粗书体", "preview": "https://wordshub.github.io/free-font/images/font/pangmenzhengdaocushuti/font.svg", "source": "https://mp.weixin.qq.com/s/LZ_PMNc-3uX-Atmri4OLGQ", "download": "https://wordshub.github.io/free-font/assets/font/%E4%B8%AD%E6%96%87/%E5%BA%9E%E9%97%A8%E6%AD%A3%E9%81%93%E5%AD%97%E4%BD%93%E7%B3%BB%E5%88%97/%E5%BA%9E%E9%97%A8%E6%AD%A3%E9%81%93%E7%B2%97%E4%B9%A6%E4%BD%93.ttf", "desc": "「庞门正道粗书体」由庞门正道公众号2018年发布的第二款免费商用字体，由车港敏设计完成", "license": "商免"}, {"id": 7, "code": "zhankugaoduanhei", "name": "站酷高端黑", "preview": "https://wordshub.github.io/free-font/images/font/zhankugaoduanhei/font.svg", "source": "https://www.zcool.com.cn/article/ZMTQyOTUy.html", "download": "https://wordshub.github.io/free-font/assets/font/%E4%B8%AD%E6%96%87/%E7%AB%99%E9%85%B7%E5%AD%97%E4%BD%93%E7%B3%BB%E5%88%97/%20%E7%AB%99%E9%85%B7%E9%AB%98%E7%AB%AF%E9%BB%91.ttf", "desc": "「站酷高端黑」由字体设计师胡晓波、刘兵克发起，参与汉字百人舞100位字体设计师共同完成。是2014年圣诞发布的第一款由站酷冠名的公益字体，字体设计师胡晓波、刘兵克在站酷网发起“汉字百人舞”的征集设计师集体造字，前后参与设计师近百人。2014年12月发布第一版，2015年11月发布最新修订版。包含6763个汉字、数字和英文字母。「庞门正道轻松体」由庞门正道在2019年发布的第三款免费字体。", "license": "商免"}, {"id": 8, "code": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "站酷文艺体", "preview": "https://wordshub.github.io/free-font/images/font/zhankuwenyiti/font.svg", "source": "https://www.zcool.com.cn/work/ZMjc2NDA5NDA=.html", "download": "https://wordshub.github.io/free-font/assets/font/%E4%B8%AD%E6%96%87/%E7%AB%99%E9%85%B7%E5%AD%97%E4%BD%93%E7%B3%BB%E5%88%97/%E7%AB%99%E9%85%B7%E6%96%87%E8%89%BA%E4%BD%93.ttf", "desc": "「站酷文艺体」由字体设计师刘兵克确定字形和规范，由刘兵克工作室以及刘兵克字体设计直播课学员共同创作完成，设计师郑庆科完成了最终的字库生成工作。字形新颖独特，简洁有力，清新淡雅，文艺范十足。包含7155个常用字，52个英文字母，10 个阿拉伯数字，67个常用标点及符号。", "license": "商免"}, {"id": 9, "code": "zhankuqingkehuangyouti", "name": "站酷庆科黄油体", "preview": "https://wordshub.github.io/free-font/images/font/zhankuqingkehuangyouti/font.svg", "source": "https://www.zcool.com.cn/work/ZMTg5MDEyMDQ=.html", "download": "https://wordshub.github.io/free-font/assets/font/%E4%B8%AD%E6%96%87/%E7%AB%99%E9%85%B7%E5%AD%97%E4%BD%93%E7%B3%BB%E5%88%97/%E7%AB%99%E9%85%B7%E5%BA%86%E7%A7%91%E9%BB%84%E6%B2%B9%E4%BD%93.ttf", "desc": "「站酷庆科黄油体」，由字体设计师郑庆科开发，线条圆润。并且字体数量多，达到了9千个多个汉字。站酷庆科黄油体是一款字型创新、线条圆润的字体。笔划的每一个直角，都被处理成了半径为4pt的圆角。在设计中需遵循笔划简化但不省略，笔划角度尽量为垂直角度。字体部首的右下角均为45°缺角，提高了字体视觉中心，并有效的解决了一些字体因笔划交叉，字体阅读困难的毛病。", "license": "商免"}, {"id": 10, "code": "<PERSON>han<PERSON>kuhe<PERSON>", "name": "站酷酷黑", "preview": "https://wordshub.github.io/free-font/images/font/zhankukuhei/font.svg", "source": "https://www.zcool.com.cn/work/ZMTc2MDM5MTY=.html", "download": "https://wordshub.github.io/free-font/assets/font/%E4%B8%AD%E6%96%87/%E7%AB%99%E9%85%B7%E5%AD%97%E4%BD%93%E7%B3%BB%E5%88%97/%E7%AB%99%E9%85%B7%E9%85%B7%E9%BB%91%E4%BD%93.ttf", "desc": "「站酷酷黑」由胡晓波发起，字游工作室成员进行基础字形设计。胡晓波设计班十名学员共同设计完成。[站酷酷黑体验版］在站酷十周年之际横空出世。字形笔画粗犷有力，用宽扁型的字面构建出厚重的字体形态，笔画细节上的修饰既增强了字体的设计感，又让字体多了一些小小的精致感，中宫的饱满使得文字之间的排版组合非常醒目好看。2016年7月发布体验版。包含3500个常用字，52个英文字母，10个阿拉伯数字。", "license": "商免"}, {"id": 11, "code": "zhankukuaileti", "name": "站酷快乐体", "preview": "https://wordshub.github.io/free-font/images/font/zhankukuaileti/font.svg", "source": "https://www.zcool.com.cn/work/ZMTMzMDQ0Mjg=.html", "download": "https://wordshub.github.io/free-font/assets/font/%E4%B8%AD%E6%96%87/%E7%AB%99%E9%85%B7%E5%AD%97%E4%BD%93%E7%B3%BB%E5%88%97/%E7%AB%99%E9%85%B7%E5%BF%AB%E4%B9%90%E4%BD%93.ttf", "desc": "「站酷快乐体」由刘兵克学员和工作室团队设计。是在“站酷快乐体”基础上耗时7个月进行的大幅修改与提升，经过多轮校审之后已正式对外公布并提供下载。2015年9月发布第一版，2016年6月发布最新修订版。采用GB2312汉字编码，共收录6763个汉字。", "license": "商免"}, {"id": 12, "code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "沐瑶软笔手写体", "preview": "https://wordshub.github.io/free-font/images/font/muyaoruanbishouxieti/font.svg", "source": "https://www.zcool.com.cn/work/ZMjg5MjAwMDQ=.html", "download": "https://wordshub.github.io/free-font/assets/font/%E4%B8%AD%E6%96%87/%E6%B2%90%E7%91%B6%E5%AD%97%E4%BD%93%E7%B3%BB%E5%88%97/%E6%B2%90%E7%91%B6%E8%BD%AF%E7%AC%94%E6%89%8B%E5%86%99%E4%BD%93.ttf", "desc": "「沐瑶软笔手写体」（Muyao-Softbrush ） 是一款手写体，自来站酷设计师@春颜秋色送给女儿的礼物。也是以女儿名字命名的一款可免费商用的手写字体。遵循GB2312标准，共包含6763个汉字加数字英文和常用标点。", "license": "商免"}, {"id": 13, "code": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "沐瑶随心手写体", "preview": "https://wordshub.github.io/free-font/images/font/muyaosuixinshouxieti/font.svg", "source": "https://www.zcool.com.cn/work/ZMzYwMzk2MjA=.html", "download": "https://wordshub.github.io/free-font/assets/font/%E4%B8%AD%E6%96%87/%E6%B2%90%E7%91%B6%E5%AD%97%E4%BD%93%E7%B3%BB%E5%88%97/%E6%B2%90%E7%91%B6%E9%9A%8F%E5%BF%83%E6%89%8B%E5%86%99%E4%BD%93.ttf", "desc": "「沐瑶随心手写体」站酷设计师@春颜秋色免费开发的第二款公益字体。沐瑶是以其女儿的名字命名的。字体本身是基于GB2312-80标准，共包含6763个汉字和数百个符号。", "license": "商免"}, {"id": 14, "code": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "包图小白体", "preview": "https://wordshub.github.io/free-font/images/font/baotuxiaobaiti/font.svg", "source": "https://act.ibaotu.com/activity/1.html", "download": "https://wordshub.github.io/free-font/assets/font/%E4%B8%AD%E6%96%87/%E5%85%B6%E4%BB%96%E5%AD%97%E4%BD%93/%E5%8C%85%E5%9B%BE%E5%B0%8F%E7%99%BD%E4%BD%93.ttf", "desc": "「包图小白体」定义为一款简单可爱的创意字体。在字形笔画上去除了折笔的弧形，换之以平直的笔画，竖弯钩转为竖折，同时弱化了钩笔画，省去了许多笔画末尾的小尾巴，显得更加直白可爱。粗短的笔画，像柯基的小短腿，相比细长的字体能给人更多的轻松感。整体形态采用了镂空的制作技巧，增强了字体的立体感，适合用于品牌标志、海报、包装、影视综艺、游戏、漫画等场景。包图小白体采用GB2312编码，共收容7707个字符，包含6763个简体汉字、110个大写字母、139个小写字母、102个数字、95个标点符号、其他特殊类符号288个、其他字母210个（包含日文平假名片假名）。", "license": "商免"}, {"id": 15, "code": "jiangxizhuokai", "name": "江西拙楷", "preview": "https://wordshub.github.io/free-font/images/font/jiangxizhuokai/font.svg", "source": "https://www.zcool.com.cn/work/ZNDE4MzY4Mjg=.html", "download": "https://wordshub.github.io/free-font/assets/font/%E4%B8%AD%E6%96%87/%E5%85%B6%E4%BB%96%E5%AD%97%E4%BD%93/%E6%B1%9F%E8%A5%BF%E6%8B%99%E6%A5%B7.ttf", "desc": "「江西拙楷」由站酷设计师@Fontree （黄煜臣）个人开发并发布，这是一套手写楷体，相比电脑中标准化制作的楷体，这套字体的笔画会带有一些书写的痕迹，每个字的笔画是没有统一标准的，所以看上去显得不够规范，但是会有一种手写的自然之感。", "license": "商免"}, {"id": 16, "code": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "问藏书房", "preview": "https://wordshub.github.io/free-font/images/font/wencangshufang/font.svg", "source": "https://www.wencang.com/font.jsp", "download": "https://wordshub.github.io/free-font/assets/font/%E4%B8%AD%E6%96%87/%E5%85%B6%E4%BB%96%E5%AD%97%E4%BD%93/%E9%97%AE%E8%97%8F%E4%B9%A6%E6%88%BF.ttf", "desc": "「问藏书房」字体是由问藏书房联合造字工房一起的设计的一款免费字体，字体整体现代简约，中宫紧凑，视觉阅读极具层次感，字字俊秀且呈现古雅之风，设计团队以精心的设计赋予了汉字全新美感！字符编码采用中华人民共和国GB2312-80字符集标准，按需添加101个汉字。共收容字数6864CJK汉字，ASCII常用字符100个。", "license": "商免"}, {"id": 17, "code": "lianmengqiyilus<PERSON>izhenruiheiti", "name": "联盟起艺卢帅正锐黑体", "preview": "https://wordshub.github.io/free-font/images/font/lianmengqiyilushuaizhenruiheiti/font.svg", "source": "https://www.zcool.com.cn/work/ZMzUxMzUzNzY=.html", "download": "https://wordshub.github.io/free-font/assets/font/%E4%B8%AD%E6%96%87/%E5%85%B6%E4%BB%96%E5%AD%97%E4%BD%93/%E8%81%94%E7%9B%9F%E8%B5%B7%E8%89%BA%E5%8D%A2%E5%B8%85%E6%AD%A3%E9%94%90%E9%BB%91%E4%BD%93.ttf", "desc": "「联盟起艺卢帅正锐黑体」由站酷设计师@设计师卢帅 在站酷发布，共计8000余字", "license": "商免"}, {"id": 18, "code": "<PERSON><PERSON><PERSON>i", "name": "贤二体", "preview": "https://wordshub.github.io/free-font/images/font/xianerti/font.svg", "source": "https://www.zcool.com.cn/article/ZNjk4NDM2.html", "download": "https://wordshub.github.io/free-font/assets/font/%E4%B8%AD%E6%96%87/%E5%85%B6%E4%BB%96%E5%AD%97%E4%BD%93/%E8%B4%A4%E4%BA%8C%E4%BD%93.ttf", "desc": "「贤二体」是龙泉专寺动漫中心与汉仪字库联合推出的一款免费商用字体。贤二体的字形偏长，起笔收笔锋利且粗细对比明显，笔画笨拙微带曲线感。字体组合结构松散，重心不稳，达到诙谐幽默，惹人喜爱的效果。", "license": "商免"}, {"id": 19, "code": "<PERSON><PERSON><PERSON><PERSON>", "name": "手书体", "preview": "https://wordshub.github.io/free-font/images/font/shoushuti/font.svg", "source": "https://www.zcool.com.cn/work/ZMjI2MDk1MDg=.html", "download": "https://wordshub.github.io/free-font/assets/font/%E4%B8%AD%E6%96%87/%E5%85%B6%E4%BB%96%E5%AD%97%E4%BD%93/%E6%89%8B%E4%B9%A6%E4%BD%93.ttf", "desc": "由站酷设计师@Joker9 设计，收录基本汉字6763个，字体风格为手写风格，可以用于slogan、banner、海报、商标等设计里面。", "license": "商免"}, {"id": 20, "code": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "字体视界法棍体", "preview": "https://wordshub.github.io/free-font/images/font/zitishijiefagunti/font.svg", "source": "https://www.17font.com/fontDay/OpenSource", "download": "https://wordshub.github.io/free-font/assets/font/%E4%B8%AD%E6%96%87/%E5%85%B6%E4%BB%96%E5%AD%97%E4%BD%93/%E5%AD%97%E4%BD%93%E8%A7%86%E7%95%8C%E6%B3%95%E6%A3%8D%E4%BD%93.ttf", "desc": "「字体视界法棍体」由原字体（《义启嘟嘟体》登记号：沪作登字-2016-**********）更名而来。由上海义启信息科技有限公司字库部员工通过字体软件进行设计与制作，为了让更多的人可以免费使用商用字体，公司决定改名为《字体视界法棍体》，并于2019年7月17日字体节这天在字体视界官网发布许可广大用户免费商用。字体视界法棍体笔画中间呈弧形外拓，充满张力，加粗字形笔画显得更可爱个性；结构空间变化不像正体那样小，以拙为巧，憨态可掬。这款字体笔画纤细充满张力，规律加粗，曲线折角充分展现它可爱不粗笨的气质笔画，结构外圆内疏，左右呼应如手写，各个字生机勃勃又融为一体，整体一气呵成。采用 GB2312 编码，共收容 7698 个字符，包含 6771 个汉字，110 个大写字母，139 个小写字母，102 个数字（包含阿拉伯数字，罗马数字等），98 个标点符号，其他特殊符号 164 个，其他字母 224 个。", "license": "商免"}, {"id": 21, "code": "zhuojianganlanjianti", "name": "卓健橄榄简体", "preview": "https://wordshub.github.io/free-font/images/font/zhuojiang<PERSON>lanjianti/font.svg", "source": "https://www.zcool.com.cn/work/ZNDA2MzA3ODQ=.html", "download": "https://wordshub.github.io/free-font/assets/font/%E4%B8%AD%E6%96%87/%E5%85%B6%E4%BB%96%E5%AD%97%E4%BD%93/%E5%8D%93%E5%81%A5%E6%A9%84%E6%A6%84%E7%AE%80%E4%BD%93.ttf", "desc": "「卓健橄榄简体」由卓米品牌设计发布，并于于2019年底授权限时免费商用。收容7050个常用字，52 个英文字母，139个阿拉伯数字及常用标点符号，卓健橄榄简体是一款相对较时尚的字体，部分笔画斜切处理，让字体更具时尚感和时尚属性，整体中心偏上，更符合现代审美。官方给出的说法是限时免费下载，免费商用。具体是指可以免费商用，限时下载，还是限时免费商用，官方没有给出明确解释，这点存有疑问。如果有商用需求，请与官方联系求证。", "license": "商免"}, {"id": 22, "code": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "优设标题黑", "preview": "https://wordshub.github.io/free-font/images/font/youshebiaotihei/font.svg", "source": "https://www.uisdc.com/uisdc-first-free-font", "download": "https://wordshub.github.io/free-font/assets/font/%E4%B8%AD%E6%96%87/%E5%85%B6%E4%BB%96%E5%AD%97%E4%BD%93/%E4%BC%98%E8%AE%BE%E6%A0%87%E9%A2%98%E9%BB%91.ttf", "desc": "「优设标题黑」由优设与字由合作完成，是一款适用性广，速度感、力量感极强的专业美术标题字体。它以黑体字型为基础，整体字形沉稳，同时采用较大字面和粗壮的笔画来强化力量感。每个字体水平倾斜 8° 的设计，赋予了字体极强的速度感，为了让字体倾斜后也能保持稳固，设计师将整体字身设定宽扁。而起笔和弯钩上独具匠心的尖角设计，不仅突显了设计的几何感，而且方便后期修改。", "license": "商免"}, {"id": 23, "code": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "锐字真言体", "preview": "https://wordshub.github.io/free-font/images/font/ruizizhenyanti/font.svg", "source": "http://reeji.com/font/rui_zi_zhen_yan_ti/", "download": "https://wordshub.github.io/free-font/assets/font/%E4%B8%AD%E6%96%87/%E5%85%B6%E4%BB%96%E5%AD%97%E4%BD%93/%E9%94%90%E5%AD%97%E7%9C%9F%E8%A8%80%E4%BD%93.ttf", "desc": "「锐字真言体」是锐字家族字体当中第一款免费可商用的字体。真言体笔触浑厚有力，笔画曲折有度，字形个性鲜明，刚柔并济，落笔简洁有序，给人以遒劲有力、端正凝练的感受。直角与圆角的错落搭配使得字体婉转有度，落落大方，具有自己独到的风格！这款字体特别适用于文字标题、竞技视觉、广告设计、个性品牌设计推广、企业宣传及时尚品牌的设计应用。", "license": "商免"}, {"id": 24, "code": "qingsongsh<PERSON>yi", "name": "清松手写体1", "preview": "https://wordshub.github.io/free-font/images/font/qingsongshouxietiyi/font.svg", "source": "https://www.facebook.com/groups/549661292148791/", "download": "https://wordshub.github.io/free-font/assets/font/%E4%B8%AD%E6%96%87/%E5%85%B6%E4%BB%96%E5%AD%97%E4%BD%93/%E6%B8%85%E6%9D%BE%E6%89%8B%E5%AF%AB%E9%AB%94/%E6%B8%85%E6%9D%BE%E6%89%8B%E5%AF%AB%E9%AB%941.ttf", "desc": "「清松手写体」它是一款来自台湾的字体，它由游清松建立，为了创建这个字体，他还成立了一个小组： 「顺其字然」，首先先在稿纸上用原子笔手写，然后，透过「守写字」网站产生出初稿字体，再透过字体软件后制加工的手写字体。「顺其字然」手写字体，没有一般计算机字型的生硬感，多了几分手写般的自然感。不论是中文、英文、符号数字，适合在计算机屏幕上阅读、排版，打印出来效果也很棒的！", "license": "商免"}, {"id": 25, "code": "qingsongsh<PERSON><PERSON><PERSON>", "name": "清松手写体2", "preview": "https://wordshub.github.io/free-font/images/font/qingsongshouxietier/font.svg", "source": "https://www.facebook.com/groups/549661292148791/", "download": "https://wordshub.github.io/free-font/assets/font/%E4%B8%AD%E6%96%87/%E5%85%B6%E4%BB%96%E5%AD%97%E4%BD%93/%E6%B8%85%E6%9D%BE%E6%89%8B%E5%AF%AB%E9%AB%94/%E6%B8%85%E6%9D%BE%E6%89%8B%E5%AF%AB%E9%AB%942.ttf", "desc": "「清松手写体」它是一款来自台湾的字体，它由游清松建立，为了创建这个字体，他还成立了一个小组： 「顺其字然」，首先先在稿纸上用原子笔手写，然后，透过「守写字」网站产生出初稿字体，再透过字体软件后制加工的手写字体。「顺其字然」手写字体，没有一般计算机字型的生硬感，多了几分手写般的自然感。不论是中文、英文、符号数字，适合在计算机屏幕上阅读、排版，打印出来效果也很棒的！", "license": "商免"}, {"id": 26, "code": "qingsongshouxietisan", "name": "清松手写体3", "preview": "https://wordshub.github.io/free-font/images/font/qingsongshouxietisan/font.svg", "source": "https://www.facebook.com/groups/549661292148791/", "download": "https://wordshub.github.io/free-font/assets/font/%E4%B8%AD%E6%96%87/%E5%85%B6%E4%BB%96%E5%AD%97%E4%BD%93/%E6%B8%85%E6%9D%BE%E6%89%8B%E5%AF%AB%E9%AB%94/%E6%B8%85%E6%9D%BE%E6%89%8B%E5%AF%AB%E9%AB%943.ttf", "desc": "「清松手写体」它是一款来自台湾的字体，它由游清松建立，为了创建这个字体，他还成立了一个小组： 「顺其字然」，首先先在稿纸上用原子笔手写，然后，透过「守写字」网站产生出初稿字体，再透过字体软件后制加工的手写字体。「顺其字然」手写字体，没有一般计算机字型的生硬感，多了几分手写般的自然感。不论是中文、英文、符号数字，适合在计算机屏幕上阅读、排版，打印出来效果也很棒的！", "license": "商免"}, {"id": 27, "code": "<PERSON><PERSON><PERSON><PERSON>", "name": "一点颜体", "preview": "https://wordshub.github.io/free-font/images/font/yidianyanti/font.svg", "source": "http://founder.acgvlyric.org/iu/doku.php/%E9%80%A0%E5%AD%97:%E9%96%8B%E6%BA%90%E5%AD%97%E5%9E%8B_i.%E9%A1%8F%E9%AB%94", "download": "https://wordshub.github.io/free-font/assets/font/%E4%B8%AD%E6%96%87/%E5%88%BB%E7%9F%B3%E5%BD%95%E7%B3%BB%E5%88%97/I.%E9%A1%8F%E9%AB%94.ttf", "desc": "「一点颜体」是由王汉宗颜体修改而来，重新制作和修整原字型中缺少的或有欠美观的部份，例如全半形英数、平假名、片假名、拉丁字母、俄文字母、阿拉伯数字、圈圈字、常用符号等。", "license": "GPL-3.0"}, {"id": 28, "code": "y<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "一点钢笔鹤体", "preview": "https://wordshub.github.io/free-font/images/font/yidiangangbiheti/font.svg", "source": "http://founder.acgvlyric.org/iu/doku.php/%E9%80%A0%E5%AD%97:%E9%96%8B%E6%BA%90%E5%AD%97%E5%9E%8B_i.%E9%8B%BC%E7%AD%86%E9%B6%B4%E9%AB%94", "download": "https://wordshub.github.io/free-font/assets/font/%E4%B8%AD%E6%96%87/%E5%88%BB%E7%9F%B3%E5%BD%95%E7%B3%BB%E5%88%97/I.%E9%8B%BC%E7%AD%86%E9%B6%B4%E9%AB%94.ttf", "desc": "「一点钢笔鹤体」由王汉宗粗钢体修改而来，刻石錄重新制作和修整原字型中缺少的或有欠美观的部份，例如半形英数、圈圈字、常用符号等。", "license": "GPL-3.0"}, {"id": 29, "code": "<PERSON><PERSON><PERSON><PERSON>", "name": "一点明体", "preview": "https://wordshub.github.io/free-font/images/font/yidianmingti/font.svg", "source": "http://founder.acgvlyric.org/iu/doku.php/%E9%80%A0%E5%AD%97:%E9%96%8B%E6%BA%90%E5%AD%97%E5%9E%8B_i.%E6%98%8E%E9%AB%94", "download": "https://wordshub.github.io/free-font/assets/font/%E4%B8%AD%E6%96%87/%E5%88%BB%E7%9F%B3%E5%BD%95%E7%B3%BB%E5%88%97/I.%E6%98%8E%E9%AB%94.ttf", "desc": "「一点明体」是一套依照传承字形标准化文件《传承字形部件检校表》的推荐字形标准，并以 TrueType格式封装、依照 Unicode 编码的 OpenType字型。I.明体 名称里的 I 是罗马数字 一，I. 念作 一点，象徵笔画的基本：点与线。目前I.明体已由开源字型组织一点字坊 全力维护。", "license": "IPA-1.0"}, {"id": 30, "code": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "文泉驿正黑", "preview": "https://wordshub.github.io/free-font/images/font/wenquanyizhenghei/font.svg", "source": "http://wenq.org/wqy2/index.cgi?ZenHei", "download": "https://wordshub.github.io/free-font/assets/font/%E4%B8%AD%E6%96%87/%E5%88%BB%E7%9F%B3%E5%BD%95%E7%B3%BB%E5%88%97/I.%E6%98%8E%E9%AB%94.ttf", "desc": "「文泉驿正黑体」是一个\"自由字体\"。该字体包含了所有常用简体中文、繁体中文所需要的汉字(最新版本包含超过27842个汉字，完整覆盖GB2312/Big5/GBK以及GB18030标准字符集)。该字体同时还包含了日文、韩文和其他几十种语言符号。除此以外，该字体还嵌入了最新版本的文泉驿点阵宋体的中英文点阵，使得屏幕汉字显示清晰锐利，易于阅读。作为黑体中文字体，文泉驿正黑为非衬线字体，笔画对比度明显，特别适合屏幕汉字显示以及文档标题字体。", "license": "GPL-2.0"}, {"id": 31, "code": "wen<PERSON><PERSON>iweimihei", "name": "文泉驿微米黑", "preview": "https://wordshub.github.io/free-font/images/font/wenquanyiweimihei/font.svg", "source": "http://wenq.org/wqy2/index.cgi?MicroHei", "download": "https://wordshub.github.io/free-font/assets/font/%E4%B8%AD%E6%96%87/%E5%88%BB%E7%9F%B3%E5%BD%95%E7%B3%BB%E5%88%97/I.%E6%98%8E%E9%AB%94.ttf", "desc": "「文泉驿微米黑」是一个\"自由字体\"。该字体包含了所有常用简体中文、繁体中文所需要的汉字(最新版本包含超过20932个汉字，完整覆盖GB2312/Big5以及GBK标准字符集)。该字体同时还包含了日文、韩文和其他几十种语言符号。以外，该字体还包含了高质量的Droid Sans拉丁符号和Droid Sans Mono等宽字体，并内置Hinting和Kerning信息。微米黑字体文件极小，特别使用于便携式电脑设备。 该字体版权受到法律保护，字体版权人为Google和文泉驿信任委员会。请遵循官方字体授权使用并衍生本字体", "license": "GPL-2.0"}, {"id": 32, "code": "hanamin", "name": "花园明朝", "preview": "https://wordshub.github.io/free-font/images/font/hanamin/font.svg", "source": "http://fonts.jp/hanazono/", "download": "https://wordshub.github.io/free-font/assets/font/%E6%97%A5%E6%96%87/%E8%8A%B1%E5%9B%AD%E6%98%8E%E6%9C%9D/HanaMinA.ttf", "desc": "「花园明朝体」（HanaMin 花园フォント）是一款基于日语的自由开源汉字字体。这款字体的特点是涵盖了目前 CJK (中日韩统一表意文字)字库中几乎所有的汉字与字符，而且作为开源项目，任何人可自由下载使用。如果在软件中找不到此字体，请查找名称：HanaMin。此字体对简体中文支持比较好。", "license": "OFL-1.1"}, {"id": 33, "code": "yuanjiemingchaoti", "name": "源界明朝体", "preview": "https://wordshub.github.io/free-font/images/font/yuanjiemingchaoti/font.svg", "source": "https://flopdesign.com/blog/font/5146/", "download": "https://wordshub.github.io/free-font/assets/font/%E6%97%A5%E6%96%87/%E6%BA%90%E7%95%8C%E6%98%8E%E6%9C%9D%E4%BD%93.ttf", "desc": "「源界明朝体」这款字体是以「思源宋体」（日文为「源ノ明朝」）为基础，加入破坏效果使其最接近可读状态，可以简单理解成思源宋体的破坏版，源界明朝在视觉上具有相当大的张力，可作为图片内的标题和大字使用，整体来说相当吸睛且颇具效果。源界明朝体为日文字体，对部分简体中文支持不太好。", "license": "OFL-1.1"}, {"id": 34, "code": "souk<PERSON><PERSON><PERSON>", "name": "装甲明朝", "preview": "https://wordshub.github.io/free-font/images/font/soukoumincho/font.svg", "source": "http://flopdesign.com/blog/font/5228/", "download": "https://wordshub.github.io/free-font/assets/font/%E6%97%A5%E6%96%87/%E8%A3%85%E7%94%B2%E6%98%8E%E6%9C%9D%E4%BD%93.ttf", "desc": "「装甲明朝」是日本网友以思源宋体为基础，修改后开放免费下载的新字型。字体风格硬朗有气派，若是拿来做越野车、大型装甲机车等玩具淘宝海报设计就非常适用了。Soukou Mincho 装甲明朝体支持繁体（分部）、简体（部分）、英文、日文以及数字符号。", "license": "OFL-1.1"}, {"id": 35, "code": "seto", "name": "濑户体", "preview": "https://wordshub.github.io/free-font/images/font/seto/font.svg", "source": "http://setofont.osdn.jp/", "download": "https://wordshub.github.io/free-font/assets/font/%E6%97%A5%E6%96%87/%E6%BF%91%E6%88%B7%E4%BD%93.ttf", "desc": "「濑户字体」是一款偏可爱风的字体，支持简体中文、繁体中文、日文，包含CJK常用汉字、平片假名、JIS第一至四级，共30000余字。这是由日本的一位业余字体设计师濑户のぞみ小姐所制作的。", "license": "OFL-1.1"}, {"id": 36, "code": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "手写杂字体", "preview": "https://wordshub.github.io/free-font/images/font/shouxiezaziti/font.svg", "source": "http://setofont.osdn.jp/", "download": "https://wordshub.github.io/free-font/assets/font/%E6%97%A5%E6%96%87/851%E6%89%8B%E5%86%99%E6%9D%82%E5%AD%97%E4%BD%93.ttf", "desc": "「手写杂字体」这是一个日本人制作的一款有点可爱风格的字体，所以会是日本字形（然而作者表示其实日本标准很多地方他都没遵循，所以叫杂字体）。作者的描述是自由使用与重新配布，可商用，但是保留著作权，字体仍在不断更新加字中。此款手写杂字体，虽为日文字体，但对中文繁体以及中文简体都支持得灰常不错。", "license": "商免"}, {"id": 37, "code": "tanukipermanentmarker", "name": "麦克笔手绘体", "preview": "https://wordshub.github.io/free-font/images/font/tanukipermanentmarker/font.svg", "source": "https://tanukifont.com/tanuki-permanent-marker/", "download": "https://wordshub.github.io/free-font/assets/font/%E6%97%A5%E6%96%87/%E9%BA%A6%E5%85%8B%E7%AC%94%E6%89%8B%E7%BB%98%E4%BD%93.ttf", "desc": "「麦克笔手绘体」一款日本网站提供的免费商用字体，たぬき油性マジック(TanukiMagic) 字型风格和马克笔手绘的 POP 效果如出一辙。", "license": "商免"}, {"id": 38, "code": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "王漢宗超黑體俏皮動物", "preview": "https://wordshub.github.io/free-font/images/font/wanghanzongchaoheiqiao<PERSON>/font.svg", "source": "https://code.google.com/archive/p/wangfonts/", "download": "https://wordshub.github.io/free-font/assets/font/%E4%B8%AD%E6%96%87/%E7%8E%8B%E6%B1%89%E5%AE%97%E5%AD%97%E4%BD%93%E7%B3%BB%E5%88%97/%E7%8E%8B%E6%BC%A2%E5%AE%97%E8%B6%85%E9%BB%91%E9%AB%94%E4%BF%8F%E7%9A%AE%E5%8B%95%E7%89%A9%E4%B8%80.ttf", "desc": "「王汉宗自由字型」由研发天蚕字库的台湾中原大学数学系王汉宗教授先分别在2000年和2004年先后捐出十套WCL系列字型和32套新字型，全部以GNU GPL许可分发。", "license": "GPL-2.0"}, {"id": 39, "code": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "王漢宗超明體繁", "preview": "https://wordshub.github.io/free-font/images/font/wanghanzongchaomingtifan/font.svg", "source": "https://code.google.com/archive/p/wangfonts/", "download": "https://wordshub.github.io/free-font/assets/font/%E4%B8%AD%E6%96%87/%E7%8E%8B%E6%B1%89%E5%AE%97%E5%AD%97%E4%BD%93%E7%B3%BB%E5%88%97/%E7%8E%8B%E6%BC%A2%E5%AE%97%E8%B6%85%E6%98%8E%E9%AB%94%E7%B9%81.ttf", "desc": "「王汉宗自由字型」由研发天蚕字库的台湾中原大学数学系王汉宗教授先分别在2000年和2004年先后捐出十套WCL系列字型和32套新字型，全部以GNU GPL许可分发。", "license": "GPL-2.0"}, {"id": 40, "code": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "王漢宗粗楷體簡", "preview": "https://wordshub.github.io/free-font/images/font/wanghanzong<PERSON>/font.svg", "source": "https://code.google.com/archive/p/wangfonts/", "download": "https://wordshub.github.io/free-font/assets/font/%E4%B8%AD%E6%96%87/%E7%8E%8B%E6%B1%89%E5%AE%97%E5%AD%97%E4%BD%93%E7%B3%BB%E5%88%97/%E7%8E%8B%E6%BC%A2%E5%AE%97%E7%B2%97%E6%A5%B7%E9%AB%94%E7%B0%A1.ttf", "desc": "「王汉宗自由字型」由研发天蚕字库的台湾中原大学数学系王汉宗教授先分别在2000年和2004年先后捐出十套WCL系列字型和32套新字型，全部以GNU GPL许可分发。", "license": "GPL-2.0"}, {"id": 41, "code": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "王漢宗粗明體繁", "preview": "https://wordshub.github.io/free-font/images/font/wanghanzongcumingtifan/font.svg", "source": "https://code.google.com/archive/p/wangfonts/", "download": "https://wordshub.github.io/free-font/assets/font/%E4%B8%AD%E6%96%87/%E7%8E%8B%E6%B1%89%E5%AE%97%E5%AD%97%E4%BD%93%E7%B3%BB%E5%88%97/%E7%8E%8B%E6%BC%A2%E5%AE%97%E7%B2%97%E6%98%8E%E9%AB%94%E7%B9%81.ttf", "desc": "「王汉宗自由字型」由研发天蚕字库的台湾中原大学数学系王汉宗教授先分别在2000年和2004年先后捐出十套WCL系列字型和32套新字型，全部以GNU GPL许可分发。", "license": "GPL-2.0"}, {"id": 42, "code": "<PERSON><PERSON><PERSON>zonggangbixingkaifan", "name": "王漢宗鋼筆行楷繁", "preview": "https://wordshub.github.io/free-font/images/font/wanghanzonggangbixingkaifan/font.svg", "source": "https://code.google.com/archive/p/wangfonts/", "download": "https://wordshub.github.io/free-font/assets/font/%E4%B8%AD%E6%96%87/%E7%8E%8B%E6%B1%89%E5%AE%97%E5%AD%97%E4%BD%93%E7%B3%BB%E5%88%97/%E7%8E%8B%E6%BC%A2%E5%AE%97%E9%8B%BC%E7%AD%86%E8%A1%8C%E6%A5%B7%E7%B9%81.ttf", "desc": "「王汉宗自由字型」由研发天蚕字库的台湾中原大学数学系王汉宗教授先分别在2000年和2004年先后捐出十套WCL系列字型和32套新字型，全部以GNU GPL许可分发。", "license": "GPL-2.0"}, {"id": 43, "code": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "王漢宗空疊圓繁", "preview": "https://wordshub.github.io/free-font/images/font/wanghanzongkongdieliufan/font.svg", "source": "https://code.google.com/archive/p/wangfonts/", "download": "https://wordshub.github.io/free-font/assets/font/%E4%B8%AD%E6%96%87/%E7%8E%8B%E6%B1%89%E5%AE%97%E5%AD%97%E4%BD%93%E7%B3%BB%E5%88%97/%E7%8E%8B%E6%BC%A2%E5%AE%97%E7%A9%BA%E7%96%8A%E5%9C%93%E7%B9%81.ttf", "desc": "「王汉宗自由字型」由研发天蚕字库的台湾中原大学数学系王汉宗教授先分别在2000年和2004年先后捐出十套WCL系列字型和32套新字型，全部以GNU GPL许可分发。", "license": "GPL-2.0"}, {"id": 44, "code": "wang<PERSON><PERSON><PERSON>lihaibao", "name": "王漢宗酷儷海報", "preview": "https://wordshub.github.io/free-font/images/font/wanghanzongkulihaibao/font.svg", "source": "https://code.google.com/archive/p/wangfonts/", "download": "https://wordshub.github.io/free-font/assets/font/%E4%B8%AD%E6%96%87/%E7%8E%8B%E6%B1%89%E5%AE%97%E5%AD%97%E4%BD%93%E7%B3%BB%E5%88%97/%E7%8E%8B%E6%BC%A2%E5%AE%97%E9%85%B7%E5%84%B7%E6%B5%B7%E5%A0%B1.ttf", "desc": "「王汉宗自由字型」由研发天蚕字库的台湾中原大学数学系王汉宗教授先分别在2000年和2004年先后捐出十套WCL系列字型和32套新字型，全部以GNU GPL许可分发。", "license": "GPL-2.0"}, {"id": 45, "code": "wanghanzongkuzhenghaibao", "name": "王漢宗酷正海報", "preview": "https://wordshub.github.io/free-font/images/font/wanghanzongkuzhenghaibao/font.svg", "source": "https://code.google.com/archive/p/wangfonts/", "download": "https://wordshub.github.io/free-font/assets/font/%E4%B8%AD%E6%96%87/%E7%8E%8B%E6%B1%89%E5%AE%97%E5%AD%97%E4%BD%93%E7%B3%BB%E5%88%97/%E7%8E%8B%E6%BC%A2%E5%AE%97%E9%85%B7%E6%AD%A3%E6%B5%B7%E5%A0%B1.ttf", "desc": "「王汉宗自由字型」由研发天蚕字库的台湾中原大学数学系王汉宗教授先分别在2000年和2004年先后捐出十套WCL系列字型和32套新字型，全部以GNU GPL许可分发。", "license": "GPL-2.0"}, {"id": 46, "code": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "王漢宗特黑體繁", "preview": "https://wordshub.github.io/free-font/images/font/wanghanzongteheitifan/font.svg", "source": "https://code.google.com/archive/p/wangfonts/", "download": "https://wordshub.github.io/free-font/assets/font/%E4%B8%AD%E6%96%87/%E7%8E%8B%E6%B1%89%E5%AE%97%E5%AD%97%E4%BD%93%E7%B3%BB%E5%88%97/%E7%8E%8B%E6%BC%A2%E5%AE%97%E7%89%B9%E9%BB%91%E9%AB%94%E7%B9%81.ttf", "desc": "「王汉宗自由字型」由研发天蚕字库的台湾中原大学数学系王汉宗教授先分别在2000年和2004年先后捐出十套WCL系列字型和32套新字型，全部以GNU GPL许可分发。", "license": "GPL-2.0"}, {"id": 47, "code": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "王漢宗特明體繁", "preview": "https://wordshub.github.io/free-font/images/font/wanghanzongtemingtifan/font.svg", "source": "https://code.google.com/archive/p/wangfonts/", "download": "https://wordshub.github.io/free-font/assets/font/%E4%B8%AD%E6%96%87/%E7%8E%8B%E6%B1%89%E5%AE%97%E5%AD%97%E4%BD%93%E7%B3%BB%E5%88%97/%E7%8E%8B%E6%BC%A2%E5%AE%97%E7%89%B9%E6%98%8E%E9%AB%94%E7%B9%81.ttf", "desc": "「王汉宗自由字型」由研发天蚕字库的台湾中原大学数学系王汉宗教授先分别在2000年和2004年先后捐出十套WCL系列字型和32套新字型，全部以GNU GPL许可分发。", "license": "GPL-2.0"}, {"id": 48, "code": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "王漢宗特圓體繁", "preview": "https://wordshub.github.io/free-font/images/font/wanghanzongteyuantifan/font.svg", "source": "https://code.google.com/archive/p/wangfonts/", "download": "https://wordshub.github.io/free-font/assets/font/%E4%B8%AD%E6%96%87/%E7%8E%8B%E6%B1%89%E5%AE%97%E5%AD%97%E4%BD%93%E7%B3%BB%E5%88%97/%E7%8E%8B%E6%BC%A2%E5%AE%97%E7%89%B9%E5%9C%93%E9%AB%94%E7%B9%81.ttf", "desc": "「王汉宗自由字型」由研发天蚕字库的台湾中原大学数学系王汉宗教授先分别在2000年和2004年先后捐出十套WCL系列字型和32套新字型，全部以GNU GPL许可分发。", "license": "GPL-2.0"}, {"id": 49, "code": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "王漢宗細黑體繁", "preview": "https://wordshub.github.io/free-font/images/font/wanghanzongxiheitifan/font.svg", "source": "https://code.google.com/archive/p/wangfonts/", "download": "https://wordshub.github.io/free-font/assets/font/%E4%B8%AD%E6%96%87/%E7%8E%8B%E6%B1%89%E5%AE%97%E5%AD%97%E4%BD%93%E7%B3%BB%E5%88%97/%E7%8E%8B%E6%BC%A2%E5%AE%97%E7%B4%B0%E9%BB%91%E9%AB%94%E7%B9%81.ttf", "desc": "「王汉宗自由字型」由研发天蚕字库的台湾中原大学数学系王汉宗教授先分别在2000年和2004年先后捐出十套WCL系列字型和32套新字型，全部以GNU GPL许可分发。", "license": "GPL-2.0"}, {"id": 50, "code": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "王漢宗細明體繁", "preview": "https://wordshub.github.io/free-font/images/font/wanghanzongximingtifan/font.svg", "source": "https://code.google.com/archive/p/wangfonts/", "download": "https://wordshub.github.io/free-font/assets/font/%E4%B8%AD%E6%96%87/%E7%8E%8B%E6%B1%89%E5%AE%97%E5%AD%97%E4%BD%93%E7%B3%BB%E5%88%97/%E7%8E%8B%E6%BC%A2%E5%AE%97%E7%B4%B0%E6%98%8E%E9%AB%94%E7%B9%81.ttf", "desc": "「王汉宗自由字型」由研发天蚕字库的台湾中原大学数学系王汉宗教授先分别在2000年和2004年先后捐出十套WCL系列字型和32套新字型，全部以GNU GPL许可分发。", "license": "GPL-2.0"}, {"id": 51, "code": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "王漢宗細新宋簡", "preview": "https://wordshub.github.io/free-font/images/font/wanghanzong<PERSON>ji<PERSON>/font.svg", "source": "https://code.google.com/archive/p/wangfonts/", "download": "https://wordshub.github.io/free-font/assets/font/%E4%B8%AD%E6%96%87/%E7%8E%8B%E6%B1%89%E5%AE%97%E5%AD%97%E4%BD%93%E7%B3%BB%E5%88%97/%E7%8E%8B%E6%BC%A2%E5%AE%97%E7%B4%B0%E6%96%B0%E5%AE%8B%E7%B0%A1.ttf", "desc": "「王汉宗自由字型」由研发天蚕字库的台湾中原大学数学系王汉宗教授先分别在2000年和2004年先后捐出十套WCL系列字型和32套新字型，全部以GNU GPL许可分发。", "license": "GPL-2.0"}, {"id": 52, "code": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>fan", "name": "王漢宗細圓體繁", "preview": "https://wordshub.github.io/free-font/images/font/wanghanzongxiyuantifan/font.svg", "source": "https://code.google.com/archive/p/wangfonts/", "download": "https://wordshub.github.io/free-font/assets/font/%E4%B8%AD%E6%96%87/%E7%8E%8B%E6%B1%89%E5%AE%97%E5%AD%97%E4%BD%93%E7%B3%BB%E5%88%97/%E7%8E%8B%E6%BC%A2%E5%AE%97%E7%B4%B0%E5%9C%93%E9%AB%94%E7%B9%81.ttf", "desc": "「王汉宗自由字型」由研发天蚕字库的台湾中原大学数学系王汉宗教授先分别在2000年和2004年先后捐出十套WCL系列字型和32套新字型，全部以GNU GPL许可分发。", "license": "GPL-2.0"}, {"id": 53, "code": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "王漢宗顏楷體繁", "preview": "https://wordshub.github.io/free-font/images/font/wanghanzongyankaitifan/font.svg", "source": "https://code.google.com/archive/p/wangfonts/", "download": "https://wordshub.github.io/free-font/assets/font/%E4%B8%AD%E6%96%87/%E7%8E%8B%E6%B1%89%E5%AE%97%E5%AD%97%E4%BD%93%E7%B3%BB%E5%88%97/%E7%8E%8B%E6%BC%A2%E5%AE%97%E9%A1%8F%E6%A5%B7%E9%AB%94%E7%B9%81.ttf", "desc": "「王汉宗自由字型」由研发天蚕字库的台湾中原大学数学系王汉宗教授先分别在2000年和2004年先后捐出十套WCL系列字型和32套新字型，全部以GNU GPL许可分发。", "license": "GPL-2.0"}, {"id": 54, "code": "<PERSON>ng<PERSON><PERSON>zhongfan<PERSON>fan", "name": "王漢宗中仿宋繁", "preview": "https://wordshub.github.io/free-font/images/font/wanghanzongzhongfangsongfan/font.svg", "source": "https://code.google.com/archive/p/wangfonts/", "download": "https://wordshub.github.io/free-font/assets/font/%E4%B8%AD%E6%96%87/%E7%8E%8B%E6%B1%89%E5%AE%97%E5%AD%97%E4%BD%93%E7%B3%BB%E5%88%97/%E7%8E%8B%E6%BC%A2%E5%AE%97%E4%B8%AD%E4%BB%BF%E5%AE%8B%E7%B9%81.ttf", "desc": "「王汉宗自由字型」由研发天蚕字库的台湾中原大学数学系王汉宗教授先分别在2000年和2004年先后捐出十套WCL系列字型和32套新字型，全部以GNU GPL许可分发。", "license": "GPL-2.0"}, {"id": 55, "code": "<PERSON>ng<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "王漢宗中仿宋簡", "preview": "https://wordshub.github.io/free-font/images/font/wanghanzongzhongfan<PERSON>/font.svg", "source": "https://code.google.com/archive/p/wangfonts/", "download": "https://wordshub.github.io/free-font/assets/font/%E4%B8%AD%E6%96%87/%E7%8E%8B%E6%B1%89%E5%AE%97%E5%AD%97%E4%BD%93%E7%B3%BB%E5%88%97/%E7%8E%8B%E6%BC%A2%E5%AE%97%E4%B8%AD%E4%BB%BF%E5%AE%8B%E7%B0%A1.ttf", "desc": "「王汉宗自由字型」由研发天蚕字库的台湾中原大学数学系王汉宗教授先分别在2000年和2004年先后捐出十套WCL系列字型和32套新字型，全部以GNU GPL许可分发。", "license": "GPL-2.0"}, {"id": 56, "code": "wanghanzongzhongkaitizhu<PERSON>n", "name": "王漢宗中楷體注音", "preview": "https://wordshub.github.io/free-font/images/font/wanghanzongzhongkaitizhuyin/font.svg", "source": "https://code.google.com/archive/p/wangfonts/", "download": "https://wordshub.github.io/free-font/assets/font/%E4%B8%AD%E6%96%87/%E7%8E%8B%E6%B1%89%E5%AE%97%E5%AD%97%E4%BD%93%E7%B3%BB%E5%88%97/%E7%8E%8B%E6%BC%A2%E5%AE%97%E4%B8%AD%E6%A5%B7%E9%AB%94%E6%B3%A8%E9%9F%B3.ttf", "desc": "「王汉宗自由字型」由研发天蚕字库的台湾中原大学数学系王汉宗教授先分别在2000年和2004年先后捐出十套WCL系列字型和32套新字型，全部以GNU GPL许可分发。", "license": "GPL-2.0"}, {"id": 57, "code": "<PERSON>nghan<PERSON>z<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "王漢宗中明體注音", "preview": "https://wordshub.github.io/free-font/images/font/wanghanzongzhongming<PERSON>huyin/font.svg", "source": "https://code.google.com/archive/p/wangfonts/", "download": "https://wordshub.github.io/free-font/assets/font/%E4%B8%AD%E6%96%87/%E7%8E%8B%E6%B1%89%E5%AE%97%E5%AD%97%E4%BD%93%E7%B3%BB%E5%88%97/%E7%8E%8B%E6%BC%A2%E5%AE%97%E4%B8%AD%E6%98%8E%E9%AB%94%E6%B3%A8%E9%9F%B3.ttf", "desc": "「王汉宗自由字型」由研发天蚕字库的台湾中原大学数学系王汉宗教授先分别在2000年和2004年先后捐出十套WCL系列字型和32套新字型，全部以GNU GPL许可分发。", "license": "GPL-2.0"}, {"id": 58, "code": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "王漢宗中隸書繁", "preview": "https://wordshub.github.io/free-font/images/font/wanghanzongzhonglishufan/font.svg", "source": "https://code.google.com/archive/p/wangfonts/", "download": "https://wordshub.github.io/free-font/assets/font/%E4%B8%AD%E6%96%87/%E7%8E%8B%E6%B1%89%E5%AE%97%E5%AD%97%E4%BD%93%E7%B3%BB%E5%88%97/%E7%8E%8B%E6%BC%A2%E5%AE%97%E4%B8%AD%E9%9A%B8%E6%9B%B8%E7%B9%81.ttf", "desc": "「王汉宗自由字型」由研发天蚕字库的台湾中原大学数学系王汉宗教授先分别在2000年和2004年先后捐出十套WCL系列字型和32套新字型，全部以GNU GPL许可分发。", "license": "GPL-2.0"}, {"id": 59, "code": "<PERSON>ng<PERSON><PERSON>z<PERSON><PERSON><PERSON><PERSON>", "name": "王漢宗中明體繁", "preview": "https://wordshub.github.io/free-font/images/font/wanghanzongzhongmingtifan/font.svg", "source": "https://code.google.com/archive/p/wangfonts/", "download": "https://wordshub.github.io/free-font/assets/font/%E4%B8%AD%E6%96%87/%E7%8E%8B%E6%B1%89%E5%AE%97%E5%AD%97%E4%BD%93%E7%B3%BB%E5%88%97/%E7%8E%8B%E6%BC%A2%E5%AE%97%E4%B8%AD%E6%98%8E%E9%AB%94%E7%B9%81.ttf", "desc": "「王汉宗自由字型」由研发天蚕字库的台湾中原大学数学系王汉宗教授先分别在2000年和2004年先后捐出十套WCL系列字型和32套新字型，全部以GNU GPL许可分发。", "license": "GPL-2.0"}, {"id": 60, "code": "<PERSON><PERSON><PERSON><PERSON>zhongweibeiji<PERSON>", "name": "王漢宗中魏碑簡", "preview": "https://wordshub.github.io/free-font/images/font/wanghanzongzhongweibeijian/font.svg", "source": "https://code.google.com/archive/p/wangfonts/", "download": "https://wordshub.github.io/free-font/assets/font/%E4%B8%AD%E6%96%87/%E7%8E%8B%E6%B1%89%E5%AE%97%E5%AD%97%E4%BD%93%E7%B3%BB%E5%88%97/%E7%8E%8B%E6%BC%A2%E5%AE%97%E4%B8%AD%E9%AD%8F%E7%A2%91%E7%B0%A1.ttf", "desc": "「王汉宗自由字型」由研发天蚕字库的台湾中原大学数学系王汉宗教授先分别在2000年和2004年先后捐出十套WCL系列字型和32套新字型，全部以GNU GPL许可分发。", "license": "GPL-2.0"}, {"id": 61, "code": "wanghanzongzhongxingshufan", "name": "王漢宗中行書繁", "preview": "https://wordshub.github.io/free-font/images/font/wanghanzongzhongxingshufan/font.svg", "source": "https://code.google.com/archive/p/wangfonts/", "download": "https://wordshub.github.io/free-font/assets/font/%E4%B8%AD%E6%96%87/%E7%8E%8B%E6%B1%89%E5%AE%97%E5%AD%97%E4%BD%93%E7%B3%BB%E5%88%97/%E7%8E%8B%E6%BC%A2%E5%AE%97%E4%B8%AD%E8%A1%8C%E6%9B%B8%E7%B9%81.ttf", "desc": "「王汉宗自由字型」由研发天蚕字库的台湾中原大学数学系王汉宗教授先分别在2000年和2004年先后捐出十套WCL系列字型和32套新字型，全部以GNU GPL许可分发。", "license": "GPL-2.0"}, {"id": 62, "code": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "王漢宗綜藝體繁", "preview": "https://wordshub.github.io/free-font/images/font/wanghanzongzongyitifan/font.svg", "source": "https://code.google.com/archive/p/wangfonts/", "download": "https://wordshub.github.io/free-font/assets/font/%E4%B8%AD%E6%96%87/%E7%8E%8B%E6%B1%89%E5%AE%97%E5%AD%97%E4%BD%93%E7%B3%BB%E5%88%97/%E7%8E%8B%E6%BC%A2%E5%AE%97%E7%B6%9C%E8%97%9D%E9%AB%94%E7%B9%81.ttf", "desc": "「王汉宗自由字型」由研发天蚕字库的台湾中原大学数学系王汉宗教授先分别在2000年和2004年先后捐出十套WCL系列字型和32套新字型，全部以GNU GPL许可分发。", "license": "GPL-2.0"}, {"id": 63, "code": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "王漢宗標楷體空心", "preview": "https://wordshub.github.io/free-font/images/font/wanghanzongbiaozhunkaitikongxin/font.svg", "source": "https://code.google.com/archive/p/wangfonts/", "download": "https://wordshub.github.io/free-font/assets/font/%E4%B8%AD%E6%96%87/%E7%8E%8B%E6%B1%89%E5%AE%97%E5%AD%97%E4%BD%93%E7%B3%BB%E5%88%97/%E7%8E%8B%E6%BC%A2%E5%AE%97%E6%A8%99%E6%A5%B7%E9%AB%94%E7%A9%BA%E5%BF%83.ttf", "desc": "「王汉宗自由字型」由研发天蚕字库的台湾中原大学数学系王汉宗教授先分别在2000年和2004年先后捐出十套WCL系列字型和32套新字型，全部以GNU GPL许可分发。", "license": "GPL-2.0"}, {"id": 64, "code": "wanghanzongbokatikongyin", "name": "王漢宗波卡體空陰", "preview": "https://wordshub.github.io/free-font/images/font/wanghanzongbokatikongyin/font.svg", "source": "https://code.google.com/archive/p/wangfonts/", "download": "https://wordshub.github.io/free-font/assets/font/%E4%B8%AD%E6%96%87/%E7%8E%8B%E6%B1%89%E5%AE%97%E5%AD%97%E4%BD%93%E7%B3%BB%E5%88%97/%E7%8E%8B%E6%BC%A2%E5%AE%97%E6%B3%A2%E5%8D%A1%E9%AB%94%E7%A9%BA%E9%99%B0.ttf", "desc": "「王汉宗自由字型」由研发天蚕字库的台湾中原大学数学系王汉宗教授先分别在2000年和2004年先后捐出十套WCL系列字型和32套新字型，全部以GNU GPL许可分发。", "license": "GPL-2.0"}, {"id": 65, "code": "wang<PERSON>zongcugangtibiaozhun", "name": "王漢宗粗鋼體標準", "preview": "https://wordshub.github.io/free-font/images/font/wanghanzongcugangtibiaozhun/font.svg", "source": "https://code.google.com/archive/p/wangfonts/", "download": "https://wordshub.github.io/free-font/assets/font/%E4%B8%AD%E6%96%87/%E7%8E%8B%E6%B1%89%E5%AE%97%E5%AD%97%E4%BD%93%E7%B3%BB%E5%88%97/%E7%8E%8B%E6%BC%A2%E5%AE%97%E7%B2%97%E9%8B%BC%E9%AB%94%E6%A8%99%E6%BA%96.ttf", "desc": "「王汉宗自由字型」由研发天蚕字库的台湾中原大学数学系王汉宗教授先分别在2000年和2004年先后捐出十套WCL系列字型和32套新字型，全部以GNU GPL许可分发。", "license": "GPL-2.0"}, {"id": 66, "code": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "王漢宗粗黑體實陰", "preview": "https://wordshub.github.io/free-font/images/font/wanghanzongcuheitishiyin/font.svg", "source": "https://code.google.com/archive/p/wangfonts/", "download": "https://wordshub.github.io/free-font/assets/font/%E4%B8%AD%E6%96%87/%E7%8E%8B%E6%B1%89%E5%AE%97%E5%AD%97%E4%BD%93%E7%B3%BB%E5%88%97/%E7%8E%8B%E6%BC%A2%E5%AE%97%E7%B2%97%E9%BB%91%E9%AB%94%E5%AF%A6%E9%99%B0.ttf", "desc": "「王汉宗自由字型」由研发天蚕字库的台湾中原大学数学系王汉宗教授先分别在2000年和2004年先后捐出十套WCL系列字型和32套新字型，全部以GNU GPL许可分发。", "license": "GPL-2.0"}, {"id": 67, "code": "wanghanzongcuyuantishuangkong", "name": "王漢宗粗圓體雙空", "preview": "https://wordshub.github.io/free-font/images/font/wanghanzongcuyuantishuangkong/font.svg", "source": "https://code.google.com/archive/p/wangfonts/", "download": "https://wordshub.github.io/free-font/assets/font/%E4%B8%AD%E6%96%87/%E7%8E%8B%E6%B1%89%E5%AE%97%E5%AD%97%E4%BD%93%E7%B3%BB%E5%88%97/%E7%8E%8B%E6%BC%A2%E5%AE%97%E7%B2%97%E5%9C%93%E9%AB%94%E9%9B%99%E7%A9%BA.ttf", "desc": "「王汉宗自由字型」由研发天蚕字库的台湾中原大学数学系王汉宗教授先分别在2000年和2004年先后捐出十套WCL系列字型和32套新字型，全部以GNU GPL许可分发。", "license": "GPL-2.0"}, {"id": 68, "code": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "王漢宗仿宋體標準", "preview": "https://wordshub.github.io/free-font/images/font/wanghanzongfangsongtibiaozhun/font.svg", "source": "https://code.google.com/archive/p/wangfonts/", "download": "https://wordshub.github.io/free-font/assets/font/%E4%B8%AD%E6%96%87/%E7%8E%8B%E6%B1%89%E5%AE%97%E5%AD%97%E4%BD%93%E7%B3%BB%E5%88%97/%E7%8E%8B%E6%BC%A2%E5%AE%97%E4%BB%BF%E5%AE%8B%E9%AB%94%E6%A8%99%E6%BA%96.ttf", "desc": "「王汉宗自由字型」由研发天蚕字库的台湾中原大学数学系王汉宗教授先分别在2000年和2004年先后捐出十套WCL系列字型和32套新字型，全部以GNU GPL许可分发。", "license": "GPL-2.0"}, {"id": 69, "code": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "王漢宗海報體半天水", "preview": "https://wordshub.github.io/free-font/images/font/wanghanzonghaibaotibantianshui/font.svg", "source": "https://code.google.com/archive/p/wangfonts/", "download": "https://wordshub.github.io/free-font/assets/font/%E4%B8%AD%E6%96%87/%E7%8E%8B%E6%B1%89%E5%AE%97%E5%AD%97%E4%BD%93%E7%B3%BB%E5%88%97/%E7%8E%8B%E6%BC%A2%E5%AE%97%E6%B5%B7%E5%A0%B1%E9%AB%94%E5%8D%8A%E5%A4%A9%E6%B0%B4.ttf", "desc": "「王汉宗自由字型」由研发天蚕字库的台湾中原大学数学系王汉宗教授先分别在2000年和2004年先后捐出十套WCL系列字型和32套新字型，全部以GNU GPL许可分发。", "license": "GPL-2.0"}, {"id": 70, "code": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "王漢宗特明體標準", "preview": "https://wordshub.github.io/free-font/images/font/wanghanzongtemingtibiaozhun/font.svg", "source": "https://code.google.com/archive/p/wangfonts/", "download": "https://wordshub.github.io/free-font/assets/font/%E4%B8%AD%E6%96%87/%E7%8E%8B%E6%B1%89%E5%AE%97%E5%AD%97%E4%BD%93%E7%B3%BB%E5%88%97/%E7%8E%8B%E6%BC%A2%E5%AE%97%E7%89%B9%E6%98%8E%E9%AB%94%E6%A8%99%E6%BA%96.ttf", "desc": "「王汉宗自由字型」由研发天蚕字库的台湾中原大学数学系王汉宗教授先分别在2000年和2004年先后捐出十套WCL系列字型和32套新字型，全部以GNU GPL许可分发。", "license": "GPL-2.0"}, {"id": 71, "code": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "王漢宗新潮體波浪", "preview": "https://wordshub.github.io/free-font/images/font/wanghanzongxinchaotibolang/font.svg", "source": "https://code.google.com/archive/p/wangfonts/", "download": "https://wordshub.github.io/free-font/assets/font/%E4%B8%AD%E6%96%87/%E7%8E%8B%E6%B1%89%E5%AE%97%E5%AD%97%E4%BD%93%E7%B3%BB%E5%88%97/%E7%8E%8B%E6%BC%A2%E5%AE%97%E6%96%B0%E6%BD%AE%E9%AB%94.ttf", "desc": "「王汉宗自由字型」由研发天蚕字库的台湾中原大学数学系王汉宗教授先分别在2000年和2004年先后捐出十套WCL系列字型和32套新字型，全部以GNU GPL许可分发。", "license": "GPL-2.0"}, {"id": 72, "code": "<PERSON><PERSON><PERSON><PERSON><PERSON>yitishuangkongyin", "name": "王漢宗綜藝體雙空陰", "preview": "https://wordshub.github.io/free-font/images/font/wanghanzongzongyitishuangkongyin/font.svg", "source": "https://code.google.com/archive/p/wangfonts/", "download": "https://wordshub.github.io/free-font/assets/font/%E4%B8%AD%E6%96%87/%E7%8E%8B%E6%B1%89%E5%AE%97%E5%AD%97%E4%BD%93%E7%B3%BB%E5%88%97/%E7%8E%8B%E6%BC%A2%E5%AE%97%E7%B6%9C%E8%97%9D%E9%AB%94%E9%9B%99%E7%A9%BA%E9%99%B0.ttf", "desc": "「王汉宗自由字型」由研发天蚕字库的台湾中原大学数学系王汉宗教授先分别在2000年和2004年先后捐出十套WCL系列字型和32套新字型，全部以GNU GPL许可分发。", "license": "GPL-2.0"}, {"id": 73, "code": "quanzikuzhengkai<PERSON>", "name": "全字庫正楷體", "preview": "https://wordshub.github.io/free-font/images/font/quanzikuzhengkaiti/font.svg", "source": "https://www.cns11643.gov.tw/pageView.jsp?ID=59", "download": "https://wordshub.github.io/free-font/assets/font/%E4%B8%AD%E6%96%87/%E5%85%A8%E5%AD%97%E5%BA%93%E7%B3%BB%E5%88%97/%E5%85%A8%E5%AD%97%E5%BA%AB%E6%AD%A3%E6%A5%B7%E9%AB%94/TW-Kai-98_1.ttf", "desc": "「全字库字体」是台湾地区为了解决电脑中文字数不足而做的一款开源字体，供全社会免费商用。", "license": "商免"}, {"id": 74, "code": "quanzikuzhengsongti", "name": "全字庫正宋體", "preview": "https://wordshub.github.io/free-font/images/font/quanzikuzhengsongti/font.svg", "source": "https://www.cns11643.gov.tw/pageView.jsp?ID=59", "download": "https://wordshub.github.io/free-font/assets/font/%E4%B8%AD%E6%96%87/%E5%85%A8%E5%AD%97%E5%BA%93%E7%B3%BB%E5%88%97/%E5%85%A8%E5%AD%97%E5%BA%AB%E6%AD%A3%E5%AE%8B%E9%AB%94/TW-Sung-98_1.ttf", "desc": "「全字库字体」是台湾地区为了解决电脑中文字数不足而做的一款开源字体，供全社会免费商用。", "license": "商免"}, {"id": 75, "code": "quanzi<PERSON><PERSON>ow<PERSON>jiezi", "name": "全字庫說文解字", "preview": "https://wordshub.github.io/free-font/images/font/quanzikushuowenjiezi/font.svg", "source": "https://www.cns11643.gov.tw/pageView.jsp?ID=59", "download": "https://wordshub.github.io/free-font/assets/font/%E4%B8%AD%E6%96%87/%E5%85%A8%E5%AD%97%E5%BA%93%E7%B3%BB%E5%88%97/%E5%85%A8%E5%AD%97%E5%BA%AB%E8%AA%AA%E6%96%87%E8%A7%A3%E5%AD%97.ttf", "desc": "「全字库字体」是台湾地区为了解决电脑中文字数不足而做的一款开源字体，供全社会免费商用。", "license": "商免"}]}