<template>
  <div class="design-task">

    <!-- 搜索筛选 -->
    <a-card style="margin-bottom: 16px;">
      <a-row :gutter="16">
        <a-col :span="4">
          <a-input
            v-model="searchForm.keyword"
            placeholder="搜索任务名称"
            allow-clear
            @press-enter="handleSearch"
          >
            <template #prefix>
              <icon-search />
            </template>
          </a-input>
        </a-col>
        <a-col :span="3">
          <a-select
            v-model="searchForm.taskType"
            placeholder="任务类型"
            allow-clear
            allow-search
            :filter-option="taskTypeFilterOption"
          >
            <a-option value="设计">设计</a-option>
            <a-option value="视频">视频</a-option>
          </a-select>
        </a-col>
        <a-col :span="3">
          <a-select
            v-model="searchForm.status"
            placeholder="处理状态"
            allow-clear
            allow-search
            :filter-option="statusFilterOption"
          >
            <a-option value="待处理">待处理</a-option>
            <a-option value="处理中">处理中</a-option>
            <a-option value="已完成">已完成</a-option>
            <a-option value="已取消">已取消</a-option>
          </a-select>
        </a-col>
        <a-col :span="3">
          <a-select
            v-model="searchForm.rating"
            placeholder="任务评级"
            allow-clear
            allow-search
            :filter-option="ratingFilterOption"
          >
            <a-option v-for="rating in TASK_RATINGS" :key="rating.name" :value="rating.name">
              {{ rating.name }}
            </a-option>
          </a-select>
        </a-col>
        <a-col :span="3">
          <a-select
            v-model="searchForm.department"
            placeholder="所属部门"
            allow-clear
            allow-search
            :filter-option="departmentFilterOption"
          >
            <a-option v-for="dept in departmentList" :key="dept.id" :value="dept.name">
              {{ dept.name }}
            </a-option>
          </a-select>
        </a-col>
        <a-col :span="8">
          <a-space>
            <a-button type="primary" @click="handleSearch">
              <template #icon>
                <icon-search />
              </template>
              搜索
            </a-button>
            <a-button @click="handleReset">
              <template #icon>
                <icon-refresh />
              </template>
              重置
            </a-button>
            <a-button @click="handleRefresh">
              <template #icon>
                <icon-refresh />
              </template>
              刷新
            </a-button>
            <a-button type="primary" @click="showCreateModal">
              <template #icon>
                <icon-plus />
              </template>
              新增任务
            </a-button>
          </a-space>
        </a-col>
      </a-row>
    </a-card>
    
    <!-- 任务表格 -->
    <a-card>
      <a-table
        :columns="columns"
        :data="taskData"
        :pagination="pagination"
        :loading="loading"
        @page-change="handlePageChange"
        @page-size-change="handlePageSizeChange"
        size="small"
        class="compact-table no-horizontal-scroll"
      >
        <template #taskType="{ record }">
          <a-tag 
            :color="getTaskTypeColor(record.task_type)"
          >
            {{ record.task_type }}
          </a-tag>
        </template>
        


        <template #rating="{ record }">
          <span class="rating-text">{{ getRatingName(record.rating).toUpperCase() }}</span>
        </template>

        <template #assignTime="{ record }">
          {{ formatDateTime(record.assign_time) }}
        </template>

        <template #deliveryTime="{ record }">
          {{ formatDateTime(record.delivery_time) }}
        </template>

        <template #completeTime="{ record }">
          {{ record.complete_time ? formatDateTime(record.complete_time) : '-' }}
        </template>

        <template #createTime="{ record }">
          {{ formatDateTime(record.create_time) }}
        </template>

        <!-- 当前状态 -->
        <template #currentStatus="{ record }">
          <a-tag
            :color="getStatusColor(record.current_status)"
            size="small"
          >
            {{ record.current_status || '设计中' }}
          </a-tag>
        </template>

        <!-- 状态切换开关 -->
        <template #statusSwitch="{ record }">
          <a-switch
            :model-value="record.current_status === '设计中'"
            :disabled="!canEditStatus(record)"
            @change="(value) => handleStatusToggle(record)"
            checked-text="设计中"
            unchecked-text="设计完"
            size="small"
          />
        </template>

        <!-- 审核开关（仅管理员可见） -->
        <template #approveSwitch="{ record }">
          <a-switch
            v-if="isAdmin"
            :model-value="record.is_approved === 0"
            @change="(value) => handleApprovalToggle(record)"
            checked-text="未审核"
            unchecked-text="已审核"
            size="small"
          />
        </template>

        <!-- 存储位置双击编辑 -->
        <template #storageLocation="{ record }">
          <div
            v-if="editingStorageId !== record.id"
            @dblclick="startEditStorage(record)"
            class="storage-location-cell"
            :title="record.storage_location"
          >
            {{ record.storage_location || '双击编辑' }}
          </div>
          <a-input
            v-else
            v-model="editingStorageValue"
            size="small"
            @blur="saveStorageLocation(record)"
            @press-enter="saveStorageLocation(record)"
            @keyup.esc="cancelEditStorage"
            ref="storageInputRef"
            class="storage-input"
          />
        </template>

        <!-- 操作列（仅管理员可见） -->
        <template #actions="{ record }">
          <a-button
            v-if="isAdmin"
            type="text"
            size="small"
            @click="showEditModal(record)"
          >
            编辑
          </a-button>
        </template>

      </a-table>
    </a-card>

    <!-- 新增/编辑任务弹窗 -->
    <a-modal
      v-model:visible="taskModalVisible"
      :title="getModalTitle()"
      width="900px"
      @ok="handleTaskSubmit"
      @cancel="handleTaskCancel"
      :ok-loading="submitLoading"
    >
      <a-form :model="taskForm" :rules="taskRules" ref="taskFormRef" layout="vertical">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="任务名称" field="task_name">
              <a-input v-model="taskForm.task_name" placeholder="请输入任务名称" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="任务类型" field="task_type">
              <a-select
                v-model="taskForm.task_type"
                placeholder="请选择任务类型"
                allow-search
                :filter-option="taskTypeFilterOption"
              >
                <a-option value="设计">设计</a-option>
                <a-option value="视频">视频</a-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="派发人" field="assigner">
              <a-select
                v-model="taskForm.assigner"
                placeholder="请选择派发人"
                allow-search
                :filter-option="userFilterOption"
                allow-clear
              >
                <a-option v-for="user in userList" :key="user.id" :value="user.nickname || user.username">
                  {{ user.nickname || user.username }} ({{ user.username }})
                </a-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="所属部门" field="department">
              <a-select
                v-model="taskForm.department"
                placeholder="请选择所属部门"
                allow-search
                :filter-option="departmentFilterOption"
              >
                <a-option v-for="dept in departmentList" :key="dept.id" :value="dept.name">
                  {{ dept.name }}
                </a-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="派发时间" field="assign_time">
              <a-date-picker 
                v-model="taskForm.assign_time" 
                show-time 
                placeholder="请选择派发时间"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="交付时间" field="delivery_time">
              <a-date-picker 
                v-model="taskForm.delivery_time" 
                show-time 
                placeholder="请选择交付时间"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item label="处理状态" field="status">
              <a-select
                v-model="taskForm.status"
                placeholder="请选择处理状态"
                allow-search
                :filter-option="statusFilterOption"
              >
                <a-option value="待处理">待处理</a-option>
                <a-option value="处理中">处理中</a-option>
                <a-option value="已完成">已完成</a-option>
                <a-option value="已取消">已取消</a-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="任务评级" field="rating">
              <a-select
                v-model="taskForm.rating"
                placeholder="请选择任务评级"
                allow-search
                :filter-option="ratingFilterOption"
              >
                <a-option v-for="rating in TASK_RATINGS" :key="rating.name" :value="rating.name">
                  {{ rating.name }} ({{ rating.score }}分)
                </a-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="指定处理人" field="assignee">
              <a-select
                v-model="taskForm.assignee"
                placeholder="请选择指定处理人"
                allow-search
                :filter-option="userFilterOption"
                allow-clear
              >
                <a-option v-for="user in userList" :key="user.id" :value="user.nickname || user.username">
                  {{ user.nickname || user.username }} ({{ user.username }})
                </a-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="存储位置" field="storage_location">
              <a-input v-model="taskForm.storage_location" placeholder="请输入存储位置" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="任务创建人" field="creator">
              <a-input
                v-model="taskForm.creator"
                placeholder="当前登录用户"
                readonly
                disabled
              />
            </a-form-item>
          </a-col>
        </a-row>

        <a-form-item label="项目概述" field="project_overview">
          <a-textarea
            v-model="taskForm.project_overview"
            placeholder="请输入项目概述，详细描述项目背景、目标和要求"
            :rows="4"
          />
        </a-form-item>

        <a-form-item label="任务备注" field="remarks">
          <a-textarea
            v-model="taskForm.remarks"
            placeholder="请输入任务备注"
            :rows="3"
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 任务详情弹窗 -->
    <a-modal
      v-model:visible="detailModalVisible"
      title="任务详情"
      width="800px"
      :footer="false"
    >
      <div v-if="currentTask" class="task-detail">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="任务名称">{{ currentTask.task_name }}</a-descriptions-item>
          <a-descriptions-item label="任务类型">
            <a-tag :color="getTaskTypeColor(currentTask.task_type)">
              {{ currentTask.task_type }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="派发人">{{ currentTask.assigner }}</a-descriptions-item>
          <a-descriptions-item label="所属部门">{{ currentTask.department }}</a-descriptions-item>
          <a-descriptions-item label="派发时间">{{ formatDateTime(currentTask.assign_time) }}</a-descriptions-item>
          <a-descriptions-item label="交付时间">{{ formatDateTime(currentTask.delivery_time) }}</a-descriptions-item>
          <a-descriptions-item label="处理状态">
            <a-tag :color="getStatusColor(currentTask.status)">
              {{ currentTask.status }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="任务评级">
            <a-tag color="gold">{{ getRatingName(currentTask.rating) }}</a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="指定处理人">{{ currentTask.assignee }}</a-descriptions-item>
          <a-descriptions-item label="完成时间">{{ currentTask.complete_time ? formatDateTime(currentTask.complete_time) : '-' }}</a-descriptions-item>
          <a-descriptions-item label="存储位置">{{ currentTask.storage_location }}</a-descriptions-item>
          <a-descriptions-item label="任务创建人">{{ currentTask.creator }}</a-descriptions-item>
          <a-descriptions-item label="任务备注" :span="2">{{ currentTask.remarks || '-' }}</a-descriptions-item>
        </a-descriptions>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, nextTick } from 'vue'
import { Message, Modal } from '@arco-design/web-vue'
import { request } from '@/utils/request'
import {
  IconRefresh,
  IconPlus,
  IconSearch
} from '@arco-design/web-vue/es/icon'
import {
  getTaskList,
  createTask,
  updateTask,
  deleteTask,
  type DesignTask,
  type TaskListParams
} from '@/api/design'
import { createSelectFilter } from '@/utils/pinyin-filter'
import { getUserList, getCurrentUser } from '@/api/user'
import { getAllDepartments } from '@/api/gallery'

// 用户权限 - 简单的权限检查
const isAdmin = computed(() => {
  try {
    const userInfo = localStorage.getItem('userInfo')
    if (userInfo) {
      const user = JSON.parse(userInfo)
      return user.role === 'admin'
    }
    return false
  } catch (error) {
    console.error('获取用户角色失败:', error)
    return false
  }
})

// 任务评级配置
const TASK_RATINGS = [
  { name: "F", score: 0.3 },
  { name: "E", score: 0.6 },
  { name: "D", score: 1.2 },
  { name: "C", score: 3 },
  { name: "B", score: 9 },
  { name: "A", score: 36 }
]

// 根据评级名称获取分数
const getRatingScore = (name: string): number => {
  const rating = TASK_RATINGS.find(r => r.name === name)
  return rating ? rating.score : 0
}

// 根据分数获取评级名称
const getRatingName = (score: number): string => {
  const rating = TASK_RATINGS.find(r => r.score === score)
  return rating ? rating.name : 'F'
}

// 检查是否可以编辑状态（当前用户是指定处理人或管理员）
const canEditStatus = (record: any): boolean => {
  // 这里需要根据实际的用户权限系统来判断
  // 暂时返回true，实际应该检查：
  // 1. 当前用户是否是record.assignee（指定处理人）
  // 2. 当前用户是否是管理员
  return true
}

// 处理状态切换
const handleStatusChange = async (record: any, isCompleted: boolean) => {
  try {
    const newStatus = isCompleted ? '已完成' : '待处理'
    const completeTime = isCompleted ? new Date().toISOString() : null

    // 调用更新API
    const response = await updateTask(record.id, {
      ...record,
      status: newStatus,
      complete_time: completeTime
    })

    if (response.success) {
      Message.success(`任务状态已更新为：${newStatus}`)
      // 刷新列表
      fetchTaskList()
    } else {
      Message.error(response.msg || '状态更新失败')
    }
  } catch (error) {
    console.error('状态更新失败:', error)
    Message.error('状态更新失败')
  }
}

// 处理任务状态切换（设计中 <-> 设计完）
const handleStatusToggle = async (record: any) => {
  try {
    // 如果要从"设计中"切换到"设计完"（开关从开启变为关闭），检查存储位置
    if (record.current_status === '设计中' || !record.current_status) {
      if (!record.storage_location || record.storage_location.trim() === '') {
        Message.error('请先填写存储位置')
        return
      }
    }

    const response = await request.patch(`/api/design/tasks/${record.id}/toggle-status`)

    if (response.success) {
      // 更新本地数据
      record.current_status = response.data.current_status
      Message.success('状态切换成功')
    } else {
      throw new Error(response.msg || '状态切换失败')
    }
  } catch (error) {
    console.error('状态切换失败:', error)
    Message.error('状态切换失败')
  }
}

// 处理管理员审核切换
const handleApprovalToggle = async (record: any) => {
  try {
    const response = await request.patch(`/api/design/tasks/${record.id}/approve`)

    if (response.success) {
      // 更新本地数据
      record.is_approved = response.data.is_approved
      record.current_status = response.data.current_status
      Message.success(response.msg || '审核状态切换成功')
    } else {
      throw new Error(response.msg || '审核操作失败')
    }
  } catch (error) {
    console.error('审核操作失败:', error)
    Message.error('审核操作失败')
  }
}

// 响应式数据
const loading = ref(false)
const taskData = ref([])
const taskModalVisible = ref(false)
const detailModalVisible = ref(false)
const submitLoading = ref(false)
const currentTask = ref(null)
const isEdit = ref(false)

// 存储位置编辑相关
const editingStorageId = ref(null)
const editingStorageValue = ref('')
const storageInputRef = ref()

// 部门列表
const departmentList = ref([])

// 用户列表
const userList = ref([])

// 当前登录用户信息
const currentUser = ref({
  id: null,
  username: '',
  nickname: ''
})

// 表单引用
const taskFormRef = ref()

// 搜索表单
const searchForm = reactive({
  keyword: '',
  taskType: '',
  status: '',
  department: '',
  rating: ''
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showTotal: true,
  showPageSize: true
})

// 表格列配置 - 根据权限动态生成
const columns = computed(() => {
  const baseColumns = [
    { title: '任务名称', dataIndex: 'task_name', width: 200, ellipsis: true, tooltip: true },
    // { title: '项目概述', dataIndex: 'project_overview', width: 140, ellipsis: true, tooltip: true },
    { title: '类型', slotName: 'taskType', width: 70, align: 'center' },
    { title: '派发人', dataIndex: 'assigner', width: 80, ellipsis: true, tooltip: true },
    { title: '派发部门', dataIndex: 'department', width: 100, ellipsis: true, tooltip: true },
    { title: '派发时间', slotName: 'assignTime', width: 100 },
    // { title: '创建人', dataIndex: 'creator', width: 80, ellipsis: true, tooltip: true },
    { title: '创建时间', slotName: 'createTime', width: 100 },
    { title: '评级', slotName: 'rating', width: 50, align: 'center' },
    { title: '处理人', dataIndex: 'assignee', width: 80, ellipsis: true, tooltip: true },
    
    { title: '当前状态', slotName: 'currentStatus', width: 80, align: 'center' },
    { title: '状态切换', slotName: 'statusSwitch', width: 60, align: 'center' },
    { title: '完成时间', slotName: 'completeTime', width: 100 },
    { title: '存储位置', slotName: 'storageLocation', width: 120, ellipsis: true, tooltip: true },
    { title: '交付时间', slotName: 'deliveryTime', width: 100 }
  ]

  // 只有管理员才能看到管理审核和操作列
  if (isAdmin.value) {
    console.log('isAdmin.value:', isAdmin.value)
    baseColumns.push(
      { title: '管理审核', slotName: 'approveSwitch', width: 60, align: 'center' },
      { title: '操作', slotName: 'actions', width: 80, align: 'center' }
    )
  }

  return baseColumns
})

// 删除子任务表格列配置和行选择配置

// 任务表单
const taskForm = reactive({
  id: null,
  task_name: '',
  task_type: '',
  assigner: '',
  department: '',
  assign_time: '',
  delivery_time: '',
  status: '待处理',
  rating: 'F',
  assignee: '',
  complete_time: '',
  storage_location: '',
  creator: '',
  project_overview: '',
  remarks: ''
})

// 表单验证规则
const taskRules = {
  task_name: [
    { required: true, message: '请输入任务名称' },
    { maxLength: 200, message: '任务名称最多200个字符' }
  ],
  task_type: [
    { required: true, message: '请选择任务类型' }
  ],
  assigner: [
    { required: true, message: '请输入派发人' }
  ],
  department: [
    { required: true, message: '请选择所属部门' }
  ],
  assign_time: [
    { required: true, message: '请选择派发时间' }
  ],
  delivery_time: [
    { required: true, message: '请选择交付时间' }
  ],
  status: [
    { required: true, message: '请选择处理状态' }
  ],
  creator: [
    { required: true, message: '请输入任务创建人' }
  ]
}

// 工具函数
const formatDateTime = (dateTime: string | Date | null | undefined): string => {
  if (!dateTime) return '-'
  const date = new Date(dateTime)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

const getTaskTypeColor = (type: string): string => {
  const colorMap: Record<string, string> = {
    '设计': 'blue',
    '视频': 'purple'
  }
  return colorMap[type] || 'gray'
}

const getStatusColor = (status: string): string => {
  const colorMap: Record<string, string> = {
    '设计中': 'blue',
    '设计完': 'green',
    '验收完': 'purple',
    '待处理': 'gray',
    '处理中': 'blue',
    '已完成': 'green',
    '已取消': 'red'
  }
  return colorMap[status] || 'gray'
}

// 获取部门列表
const fetchDepartmentList = async () => {
  try {
    const response = await getAllDepartments()
    if (response.success) {
      departmentList.value = response.data || []
    }
  } catch (error) {
    console.error('获取部门列表失败:', error)
    // 如果API失败，使用默认部门列表
    departmentList.value = [
      { id: 1, name: '技术部' },
      { id: 2, name: '南方运营' },
      { id: 3, name: '北方运营' },
      { id: 4, name: '人事部' }
    ]
  }
}

// 获取用户列表
const fetchUserList = async () => {
  try {
    const response = await getUserList({ pageNum: 1, pageSize: 1000 })
    if (response.success) {
      userList.value = response.data.records || response.data.list || []
    }
  } catch (error) {
    console.error('获取用户列表失败:', error)
  }
}

// 获取当前用户信息
const fetchCurrentUser = async () => {
  try {
    // 从localStorage获取用户信息
    const userInfo = localStorage.getItem('userInfo')
    if (userInfo) {
      const user = JSON.parse(userInfo)
      currentUser.value = {
        id: user.id,
        username: user.username,
        nickname: user.nickname || user.username
      }
    }
  } catch (error) {
    console.error('获取当前用户信息失败:', error)
  }
}

// 获取任务列表
const fetchTaskList = async () => {
  loading.value = true
  try {
    const params: TaskListParams = {
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
      keyword: searchForm.keyword,
      taskType: searchForm.taskType,
      status: searchForm.status,
      department: searchForm.department
    }

    const response = await getTaskList(params)

    if (response.success) {
      const result = response.data
      taskData.value = result.records || []
      pagination.total = result.total || 0
    } else {
      Message.error(response.msg || '获取任务列表失败')
    }

  } catch (error) {
    console.error('获取任务列表失败:', error)
    Message.error('获取任务列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.current = 1
  fetchTaskList()
}

// 重置搜索
const handleReset = () => {
  searchForm.keyword = ''
  searchForm.taskType = ''
  searchForm.status = ''
  searchForm.department = ''
  searchForm.rating = ''
  pagination.current = 1
  fetchTaskList()
}

// 刷新
const handleRefresh = () => {
  fetchTaskList()
}

// 分页处理
const handlePageChange = (page: number) => {
  pagination.current = page
  fetchTaskList()
}

const handlePageSizeChange = (pageSize: number) => {
  pagination.pageSize = pageSize
  pagination.current = 1
  fetchTaskList()
}

// 显示新增弹窗
const showCreateModal = () => {
  resetTaskForm()
  isEdit.value = false
  // 设置当前用户为创建人
  taskForm.creator = currentUser.value.nickname || currentUser.value.username
  taskModalVisible.value = true
}

// 显示编辑弹窗
const showEditModal = (record: any) => {
  resetTaskForm()
  isEdit.value = true

  // 填充表单数据
  Object.keys(taskForm).forEach(key => {
    if (key === 'rating') {
      // 将数据库中的分数转换为评级名称
      taskForm[key] = getRatingName(record[key])
    } else {
      taskForm[key] = record[key]
    }
  })

  taskModalVisible.value = true
}

// 显示详情弹窗
const showDetailModal = (record: any) => {
  currentTask.value = record
  detailModalVisible.value = true
}

// 重置任务表单
const resetTaskForm = () => {
  Object.keys(taskForm).forEach(key => {
    if (key === 'status') {
      taskForm[key] = '待处理'
    } else if (key === 'rating') {
      taskForm[key] = 'F'
    } else {
      taskForm[key] = ''
    }
  })
}

// 获取弹窗标题
const getModalTitle = () => {
  return isEdit.value ? '编辑任务' : '新增任务'
}

// 删除子任务相关函数

// 提交任务表单
const handleTaskSubmit = async () => {
  try {
    const errors = await taskFormRef.value.validate()
    if (errors) {
      return
    }

    submitLoading.value = true

    const taskData = {
      task_name: taskForm.task_name,
      task_type: taskForm.task_type,
      assigner: taskForm.assigner,
      department: taskForm.department,
      assign_time: taskForm.assign_time,
      delivery_time: taskForm.delivery_time,
      status: taskForm.status,
      rating: getRatingScore(taskForm.rating), // 转换为分数
      assignee: taskForm.assignee,
      complete_time: taskForm.complete_time,
      storage_location: taskForm.storage_location,
      creator: taskForm.creator,
      project_overview: taskForm.project_overview,
      remarks: taskForm.remarks
    }

    let response: any
    if (isEdit.value) {
      response = await updateTask(taskForm.id, taskData)
    } else {
      response = await createTask(taskData)
    }

    if (response.success) {
      const message = isEdit.value ? '任务更新成功' : '任务创建成功'
      Message.success(message)
      taskModalVisible.value = false
      fetchTaskList()
    } else {
      Message.error(response.msg || '操作失败')
    }

  } catch (error) {
    console.error('提交任务失败:', error)
    Message.error('提交任务失败')
  } finally {
    submitLoading.value = false
  }
}

// 取消任务表单
const handleTaskCancel = () => {
  taskModalVisible.value = false
  resetTaskForm()
}

// 存储位置编辑相关方法
const startEditStorage = (record: any) => {
  editingStorageId.value = record.id
  editingStorageValue.value = record.storage_location || ''

  // 下一帧聚焦输入框
  nextTick(() => {
    if (storageInputRef.value) {
      storageInputRef.value.focus()
    }
  })
}

const saveStorageLocation = async (record: any) => {
  try {
    const response = await updateTask(record.id, {
      ...record,
      storage_location: editingStorageValue.value
    })

    if (response.success) {
      // 更新本地数据
      const index = taskData.value.findIndex(item => item.id === record.id)
      if (index !== -1) {
        taskData.value[index].storage_location = editingStorageValue.value
      }

      Message.success('存储位置更新成功')
    } else {
      Message.error(response.message || '更新失败')
    }
  } catch (error) {
    console.error('更新存储位置失败:', error)
    Message.error('更新存储位置失败')
  } finally {
    cancelEditStorage()
  }
}

const cancelEditStorage = () => {
  editingStorageId.value = null
  editingStorageValue.value = ''
}

// 删除任务
const handleDelete = (record: any) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除任务"${record.task_name}"吗？此操作不可恢复。`,
    onOk: async () => {
      try {
        const response = await deleteTask(record.id)

        if (response.success) {
          Message.success('任务删除成功')
          fetchTaskList()
        } else {
          Message.error(response.msg || '删除失败')
        }
      } catch (error) {
        console.error('删除任务失败:', error)
        Message.error('删除任务失败')
      }
    }
  })
}

// 拼音筛选函数
const departmentFilterOption = createSelectFilter('name')
const userFilterOption = (inputValue: string, option: any) => {
  if (!inputValue) return true
  const label = option.label || option.children || ''
  const searchLower = inputValue.toLowerCase()

  // 支持用户名和昵称筛选
  return label.toLowerCase().includes(searchLower)
}
const taskTypeFilterOption = (inputValue: string, option: any) => {
  if (!inputValue) return true
  const label = option.label || option.children || ''
  const searchLower = inputValue.toLowerCase()

  // 支持中英文筛选
  if (label.includes('设计') && (searchLower.includes('s') || searchLower.includes('she') || searchLower.includes('design'))) {
    return true
  }
  if (label.includes('视频') && (searchLower.includes('s') || searchLower.includes('shi') || searchLower.includes('video'))) {
    return true
  }

  return label.toLowerCase().includes(searchLower)
}

const statusFilterOption = (inputValue: string, option: any) => {
  if (!inputValue) return true
  const label = option.label || option.children || ''
  const searchLower = inputValue.toLowerCase()

  // 支持中英文筛选
  if (label.includes('待处理') && (searchLower.includes('d') || searchLower.includes('dai') || searchLower.includes('pending'))) {
    return true
  }
  if (label.includes('处理中') && (searchLower.includes('c') || searchLower.includes('chu') || searchLower.includes('processing'))) {
    return true
  }
  if (label.includes('已完成') && (searchLower.includes('y') || searchLower.includes('yi') || searchLower.includes('complete'))) {
    return true
  }
  if (label.includes('已取消') && (searchLower.includes('q') || searchLower.includes('qu') || searchLower.includes('cancel'))) {
    return true
  }

  return label.toLowerCase().includes(searchLower)
}

const ratingFilterOption = (inputValue: string, option: any) => {
  if (!inputValue) return true
  const label = option.label || option.children || ''
  const searchLower = inputValue.toLowerCase()

  // 支持拼音和英文筛选
  return label.toLowerCase().includes(searchLower)
}

// 页面加载时获取数据
onMounted(() => {
  fetchDepartmentList()
  fetchUserList()
  fetchCurrentUser()
  fetchTaskList()
})
</script>

<style scoped>
.design-task {
  padding: 0;
  max-width: 100%;
  overflow: hidden;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #1d2129;
}

.header-actions {
  display: flex;
  gap: 8px;
}

/* 删除子任务样式 */

/* 任务详情样式 */
.task-detail {
  max-height: 600px;
  overflow-y: auto;
}

/* 表格样式优化 */
:deep(.arco-table) {
  font-size: 14px;
}

:deep(.arco-table-th) {
  background-color: #fafafa;
  font-weight: 600;
  white-space: nowrap;
}

:deep(.arco-table-td) {
  border-bottom: 1px solid #f0f0f0;
  padding: 12px 16px;
}

/* 操作按钮样式 */
:deep(.arco-table-td .arco-space-item .arco-btn-text) {
  padding: 2px 6px;
  font-size: 12px;
  height: auto;
  min-height: 24px;
}

/* 标签样式 */
:deep(.arco-tag) {
  font-size: 12px;
}

/* 评分样式 */
:deep(.arco-rate) {
  font-size: 14px;
}

/* 紧凑表格样式 */
.compact-table :deep(.arco-table-td),
.compact-table :deep(.arco-table-th) {
  font-size: 12px !important;
  padding: 6px 8px !important;
  white-space: nowrap !important;
}

.compact-table :deep(.arco-table-tbody .arco-table-tr) {
  height: auto !important;
}

.compact-table :deep(.arco-tag) {
  font-size: 11px !important;
  padding: 2px 6px !important;
}

.compact-table :deep(.arco-switch) {
  font-size: 11px !important;
}

/* 防止横向滚动条 */
.no-horizontal-scroll :deep(.arco-table-container) {
  overflow-x: hidden !important;
}

.no-horizontal-scroll :deep(.arco-table) {
  table-layout: fixed !important;
  width: 100% !important;
}

/* 固定列宽，超出显示省略号 */
.no-horizontal-scroll :deep(.arco-table-td),
.no-horizontal-scroll :deep(.arco-table-th) {
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  white-space: nowrap !important;
}

/* 确保单行显示 */
.no-horizontal-scroll :deep(.arco-table-tbody .arco-table-tr) {
  height: 40px !important;
}

/* 特殊处理操作列 */
.no-horizontal-scroll :deep(.arco-table-operation) {
  white-space: nowrap !important;
}

/* 评级文本样式 */
.rating-text {
  font-weight: normal !important;
  font-size: 13px !important;
  color: #1d2129 !important;
  letter-spacing: 0.5px !important;
}

/* 存储位置编辑样式 */
.storage-location-cell {
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.2s;
  min-height: 20px;
  display: flex;
  align-items: center;
}

.storage-location-cell:hover {
  background-color: #f7f8fa;
}

.storage-location-cell:empty::before {
  content: '双击编辑';
  color: #86909c;
  font-style: italic;
}

.storage-input {
  width: 100% !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }
}
</style>
