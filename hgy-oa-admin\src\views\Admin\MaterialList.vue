<template>
  <div class="material-list">
    <div class="page-header">
      <h1>素材列表管理</h1>
      <div class="header-actions">
        <a-button @click="handleRefresh" style="margin-right: 8px;">
          <template #icon>
            <IconRefresh />
          </template>
          刷新
        </a-button>
        <a-button type="primary" @click="showUploadModal">
          <template #icon>
            <IconUpload />
          </template>
          上传素材
        </a-button>
      </div>
    </div>

    <!-- 搜索筛选 -->
    <a-card style="margin-bottom: 16px;">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-input v-model="searchForm.keyword" placeholder="搜索素材标题" allow-clear @press-enter="handleSearch">
            <template #prefix>
              <IconSearch />
            </template>
          </a-input>
        </a-col>
        <a-col :span="4">
          <a-select v-model="searchForm.category" placeholder="分类" allow-clear>
            <a-option v-for="category in primaryCategories" :key="category.key" :value="parseInt(category.key)">
              {{ category.title }}
            </a-option>
          </a-select>
        </a-col>
        <a-col :span="6">
          <a-button type="primary" @click="handleSearch">搜索</a-button>
          <a-button style="margin-left: 8px;" @click="handleReset">重置</a-button>
        </a-col>
      </a-row>
    </a-card>

    <a-card>
      <a-table :columns="columns" :data="materialData" :pagination="pagination" :loading="loading" @page-change="handlePageChange" @page-size-change="handlePageSizeChange">
        <template #thumbnail="{ record }">
          <div class="thumbnail-wrapper">
            <img v-if="record.type === 'image'" :src="record.thumbnail_path || record.file_path" :alt="record.title" class="thumbnail-image" @click="showPreviewModal(record)" />
            <div v-else class="text-preview" @click="showPreviewModal(record)">
              <IconFile />
              <span>{{ record.title }}</span>
            </div>
          </div>
        </template>

        <template #size="{ record }">
          <div v-if="record.type === 'image'">{{ record.width }} × {{ record.height }}</div>
          <div class="file-size">{{ formatFileSize(record.file_size) }}</div>
        </template>

        <template #category="{ record }">
          <span v-if="record.category_display">
            {{ record.category_display }}
          </span>
          <span v-else>-</span>
        </template>

        <template #upload_user="{ record }">
          {{ record.upload_user_nickname || record.upload_user || '-' }}
        </template>

        <template #status="{ record }">
          <a-switch :model-value="record.status === 1" @change="(value) => handleStatusChange(record, value)" size="small" />
        </template>

        <template #created_at="{ record }">
          <div style="font-size: 12px; color: #86909c;">
            {{ formatDateTime(record.created_at) }}
          </div>
        </template>

        <template #actions="{ record }">
          <a-space size="mini">
            <a-button type="text" size="mini" @click="showPreviewModal(record)">
              预览
            </a-button>
            <a-button type="text" size="mini" @click="editMaterial(record)">
              编辑
            </a-button>
            <a-button type="text" size="mini" status="danger" @click="deleteMaterial(record)">
              删除
            </a-button>
          </a-space>
        </template>
      </a-table>
    </a-card>

    <!-- 上传弹窗 -->
    <a-modal
      v-model:visible="uploadModalVisible"
      title="上传素材"
      width="600px"
      :confirm-loading="uploadLoading"
      @ok="handleUploadSubmit"
      @cancel="handleUploadCancel"
    >
      <a-form ref="uploadFormRef" :model="uploadForm" :rules="uploadRules" layout="vertical">
        <a-form-item label="素材标题" field="title" required>
          <a-input v-model="uploadForm.title" placeholder="请输入素材标题" />
        </a-form-item>
        


        <a-form-item label="分类" field="category" required>
          <a-select v-model="uploadForm.category" placeholder="请选择分类">
            <a-option v-for="category in primaryCategories" :key="category.key" :value="parseInt(category.key)">
              {{ category.title }}
            </a-option>
          </a-select>
        </a-form-item>

        <a-form-item label="图片文件" field="file">
          <a-upload
            ref="uploadRef"
            :file-list="fileList"
            :auto-upload="false"
            :show-file-list="true"
            :limit="1"
            accept="image/*"
            @change="handleFileChange"
            @before-upload="handleBeforeUpload"
          >
            <template #upload-button>
              <div class="upload-area">
                <IconUpload style="font-size: 48px; color: #c9cdd4;" />
                <div style="margin-top: 8px; color: #86909c;">点击或拖拽上传图片</div>
                <div style="margin-top: 4px; color: #c9cdd4; font-size: 12px;">
                  支持 JPG、PNG、GIF 格式，大小不超过 10MB
                </div>
              </div>
            </template>
          </a-upload>
        </a-form-item>



        <a-form-item label="描述">
          <a-textarea v-model="uploadForm.description" placeholder="请输入素材描述" :rows="3" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 预览弹窗 -->
    <a-modal
      v-model:visible="previewModalVisible"
      :title="previewData.title"
      width="800px"
      :footer="false"
    >
      <div class="preview-content">
        <div v-if="previewData.type === 'image'" class="image-preview">
          <img :src="previewData.file_path" :alt="previewData.title" class="preview-full-image" />
        </div>
        <div v-else class="text-preview-content">
          <div class="text-content">{{ previewData.content }}</div>
        </div>
        <div class="preview-info">
          <a-descriptions :column="2" bordered>
            <a-descriptions-item label="标题">{{ previewData.title }}</a-descriptions-item>
            <a-descriptions-item label="类型">{{ previewData.type === 'image' ? '图片素材' : '文字素材' }}</a-descriptions-item>
            <a-descriptions-item v-if="previewData.type === 'image'" label="尺寸">{{ previewData.width }} × {{ previewData.height }}</a-descriptions-item>
            <a-descriptions-item v-if="previewData.type === 'image'" label="格式">{{ previewData.format }}</a-descriptions-item>
            <a-descriptions-item label="大小">{{ formatFileSize(previewData.file_size) }}</a-descriptions-item>
            <a-descriptions-item label="上传时间">{{ formatDateTime(previewData.created_at) }}</a-descriptions-item>
            <a-descriptions-item v-if="previewData.description" label="描述" :span="2">{{ previewData.description }}</a-descriptions-item>
          </a-descriptions>
        </div>
      </div>
    </a-modal>

    <!-- 编辑素材弹窗 -->
    <a-modal v-model:visible="editModalVisible" title="编辑素材信息" width="600px" @ok="handleEditSave" @cancel="handleEditCancel">
      <a-form :model="editForm" layout="vertical" ref="editFormRef" :rules="editRules">
        <a-form-item label="当前图片">
          <div class="current-image-preview">
            <img :src="currentEditMaterial?.thumbnail_path || currentEditMaterial?.file_path" :alt="editForm.title" style="max-width: 200px; max-height: 150px; object-fit: cover; border-radius: 4px;" />
          </div>
        </a-form-item>

        <a-form-item label="替换图片">
          <a-upload
            ref="editUploadRef"
            :file-list="editFileList"
            :auto-upload="false"
            :show-file-list="true"
            :before-upload="handleEditBeforeUpload"
            @change="handleEditFileChange"
            accept="image/*"
            :limit="1"
            :custom-request="() => {}"
          >
            <template #upload-button>
              <div class="upload-button">
                <IconUpload />
                <div>点击替换图片</div>
              </div>
            </template>
          </a-upload>
          <div class="upload-tip">
            支持 JPG、PNG、GIF 格式，文件大小不超过 10MB
          </div>
        </a-form-item>

        <a-form-item label="素材标题" field="title" required>
          <a-input v-model="editForm.title" placeholder="请输入素材标题" />
        </a-form-item>

        <a-form-item label="描述">
          <a-textarea v-model="editForm.description" placeholder="请输入描述" :rows="3" />
        </a-form-item>

        <a-form-item label="分类" field="category" required>
          <a-select v-model="editForm.category" placeholder="请选择分类">
            <a-option v-for="category in primaryCategories" :key="category.key" :value="parseInt(category.key)">
              {{ category.title }}
            </a-option>
          </a-select>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { Message, Modal } from '@arco-design/web-vue'
// 移除旧的分类API导入，使用直接的fetch调用
import {
  getMaterialList,
  createMaterial,
  updateMaterial,
  deleteMaterial as deleteMaterialAPI,
  toggleMaterialStatus,
  uploadMaterial,
  uploadMaterialJSON,
  updateMaterialWithFile,
  type MaterialListParams
} from '@/api/material'

// 文件转base64函数
const fileToBase64 = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.readAsDataURL(file)
    reader.onload = () => {
      const result = reader.result as string
      // 移除data:image/jpeg;base64,前缀，只保留base64数据
      const base64Data = result.split(',')[1]
      resolve(base64Data)
    }
    reader.onerror = error => reject(error)
  })
}

// 响应式数据
const loading = ref(false)
const uploadLoading = ref(false)
const uploadModalVisible = ref(false)
const previewModalVisible = ref(false)

// 搜索表单
const searchForm = reactive({
  keyword: '',
  category: null as number | null
})

// 上传表单
const uploadForm = reactive({
  title: '',
  type: 'image' as const,
  category: null as number | null,
  description: ''
})

// 文件列表
const fileList = ref([])
const uploadRef = ref()
const editUploadRef = ref()

// 预览数据
const previewData = ref<any>({})

// 编辑相关状态
const editModalVisible = ref(false)
const editFormRef = ref()
const editForm = reactive({
  id: null as number | null,
  title: '',
  description: '',
  category: null as number | null
})
const currentEditMaterial = ref<any>(null)
const editFileList = ref([])
const editHasNewFile = ref(false)

// 素材数据
const materialData = ref<any[]>([])

// 分类数据（一级分类）
const categoryTree = ref<any[]>([])
const primaryCategories = computed(() => {
  return categoryTree.value // 所有分类都是一级分类
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0,
  showTotal: true,
  showPageSize: true
})

// 表格列配置
const columns = [
  { title: '缩略图', slotName: 'thumbnail', width: 100, align: 'center' },
  { title: '标题', dataIndex: 'title', width: 200 },
  { title: '尺寸/大小', slotName: 'size', width: 120 },
  { title: '格式', dataIndex: 'format', width: 80, align: 'center' },
  { title: '分类', slotName: 'category', width: 150 },
  { title: '上传用户', slotName: 'upload_user', width: 100 },
  { title: '上传时间', slotName: 'created_at', width: 120 },
  { title: '状态', slotName: 'status', width: 80, align: 'center' },
  { title: '操作', slotName: 'actions', width: 100, align: 'center', fixed: 'right' }
]

// 表单引用
const uploadFormRef = ref()

// 表单验证规则
const uploadRules = {
  title: [{ required: true, message: '请输入素材标题' }],
  category: [{ required: true, message: '请选择分类' }]
}

const editRules = {
  title: [{ required: true, message: '请输入素材标题' }],
  category: [{ required: true, message: '请选择分类' }]
}

// 工具方法
const formatFileSize = (size: number) => {
  if (!size) return '-'
  const units = ['B', 'KB', 'MB', 'GB']
  let index = 0
  while (size >= 1024 && index < units.length - 1) {
    size /= 1024
    index++
  }
  return `${size.toFixed(1)} ${units[index]}`
}

const formatDateTime = (dateString: string) => {
  if (!dateString) return '-'

  // 如果后端已经返回了格式化的日期，直接使用
  if (typeof dateString === 'string' && /^\d{4}-\d{2}-\d{2}$/.test(dateString)) {
    return dateString
  }

  // 否则格式化日期
  const date = new Date(dateString)
  if (isNaN(date.getTime())) return '-'

  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')

  return `${year}-${month}-${day}`
}

// 获取分类数据
const fetchCategories = async () => {
  try {
    console.log('开始获取分类数据...')
    // 使用统一的API配置
    const { getEndpointUrl } = await import('../../config/api')
    const response = await fetch(getEndpointUrl('MATERIAL_CATEGORIES'))

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const result = await response.json()

    if (result.success) {
      // 转换为组件需要的格式
      categoryTree.value = result.data.map((item: any) => ({
        key: item.id.toString(),
        title: item.name,
        icon: item.icon,
        sort_order: item.sort_order,
        description: item.description,
        count: item.count,
        level: 1, // 一级分类
        children: [] // 一级分类没有子分类
      }))
      console.log('分类数据加载成功:', categoryTree.value)
    } else {
      console.error('API返回错误:', result.msg)
      categoryTree.value = []
      Message.error('获取分类失败: ' + result.msg)
    }
  } catch (error) {
    console.error('获取分类失败:', error)
    categoryTree.value = []
    Message.error('获取分类失败')
  }
}

// 获取素材列表
const fetchMaterialList = async () => {
  try {
    loading.value = true
    const params = {
      page: pagination.current,
      pageSize: pagination.pageSize,
      keyword: searchForm.keyword || undefined,
      categoryId: searchForm.category || undefined
    }

    const response = await getMaterialList(params)
    materialData.value = response.data.list
    pagination.total = response.data.total
  } catch (error) {
    console.error('获取素材列表失败:', error)
    Message.error('获取素材列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索相关方法
const handleSearch = () => {
  pagination.current = 1
  fetchMaterialList()
}

const handleReset = () => {
  searchForm.keyword = ''
  searchForm.category = null
  pagination.current = 1
  fetchMaterialList()
}

const handleRefresh = () => {
  fetchMaterialList()
}

// 分页方法
const handlePageChange = (page: number) => {
  pagination.current = page
  fetchMaterialList()
}

const handlePageSizeChange = (pageSize: number) => {
  pagination.pageSize = pageSize
  pagination.current = 1
  fetchMaterialList()
}

// 上传相关方法
const showUploadModal = () => {
  resetUploadForm()
  uploadModalVisible.value = true
}

const resetUploadForm = () => {
  uploadForm.title = ''
  uploadForm.type = 'image'
  uploadForm.category = null
  uploadForm.description = ''
  fileList.value = []
}

// 文件上传前的处理
const handleBeforeUpload = (file: any) => {
  // 检查文件类型
  const isImage = file.type.startsWith('image/')
  if (!isImage) {
    Message.error('只能上传图片文件!')
    return false
  }

  // 检查文件大小
  const isLt10M = file.size / 1024 / 1024 < 10
  if (!isLt10M) {
    Message.error('图片大小不能超过 10MB!')
    return false
  }

  return true
}

// 文件变化处理
const handleFileChange = (fileListData: any, fileItem: any) => {
  console.log('文件变化:', fileListData, fileItem)
  fileList.value = fileListData
}

const handleUploadSubmit = async () => {
  try {
    console.log('开始提交，当前文件列表:', fileList.value)
    console.log('文件列表长度:', fileList.value.length)

    // 检查表单引用
    if (!uploadFormRef.value) {
      Message.error('表单初始化失败')
      return
    }

    const errors = await uploadFormRef.value.validate()
    if (errors) {
      console.log('表单验证失败:', errors)
      return
    }

    if (fileList.value.length === 0) {
      console.log('文件列表为空，无法提交')
      Message.error('请选择要上传的图片')
      return
    }

    const fileItem = fileList.value[0]

    // 获取原始文件对象
    const file = fileItem.originFile || fileItem.file || fileItem

    if (!file || !(file instanceof File)) {
      Message.error('文件不存在或格式不正确')
      return
    }

    uploadLoading.value = true

    // 转换文件为base64
    const fileBase64 = await fileToBase64(file)

    // 使用选择的分类
    const categoryId = uploadForm.category

    // 使用JSON上传（解决中文编码问题）
    const uploadData = {
      title: uploadForm.title,
      description: uploadForm.description || '',
      file: fileBase64,
      filename: file.name,
      category_id: categoryId || 1
    }

    console.log('准备上传的数据:', {
      title: uploadData.title,
      description: uploadData.description,
      filename: uploadData.filename,
      category_id: uploadData.category_id,
      fileSize: fileBase64.length
    })

    // 调用JSON上传API
    const response = await uploadMaterialJSON(uploadData)

    console.log('上传响应:', response)

    // 检查响应结构，可能是 response.success 而不是 response.data.success
    const isSuccess = response.success || response.data?.success
    const message = response.msg || response.data?.msg || '上传失败'

    if (isSuccess) {
      Message.success('素材上传成功')
      uploadModalVisible.value = false
      resetUploadForm()
      // 刷新素材列表
      await fetchMaterialList()
    } else {
      Message.error(message)
    }
  } catch (error: any) {
    console.error('上传素材失败:', error)
    Message.error('上传素材失败: ' + (error.message || '未知错误'))
  } finally {
    uploadLoading.value = false
  }
}

const handleUploadCancel = () => {
  uploadModalVisible.value = false
  resetUploadForm()
}

// 操作方法
const showPreviewModal = (record: any) => {
  previewData.value = record
  previewModalVisible.value = true
}

const editMaterial = (record: any) => {
  console.log('编辑素材数据:', record)

  // 回显数据
  editForm.id = record.id
  editForm.title = record.title
  editForm.description = record.description || ''

  // 设置分类信息
  editForm.category = record.category_id || null

  console.log('回显的分类:', {
    category: editForm.category
  })

  // 保存当前素材信息
  currentEditMaterial.value = record

  // 重置文件列表
  editFileList.value = []
  editHasNewFile.value = false

  editModalVisible.value = true
}

// 编辑文件上传前处理
const handleEditBeforeUpload = (file: any) => {
  // 检查文件类型
  const isImage = file.type.startsWith('image/')
  if (!isImage) {
    Message.error('只能上传图片文件!')
    return false
  }

  // 检查文件大小
  const isLt10M = file.size / 1024 / 1024 < 10
  if (!isLt10M) {
    Message.error('图片大小不能超过 10MB!')
    return false
  }

  return false // 阻止自动上传
}

// 编辑文件变化处理
const handleEditFileChange = (fileListData: any) => {
  editFileList.value = fileListData
  editHasNewFile.value = fileListData.length > 0
}

// 保存编辑
const handleEditSave = async () => {
  try {
    // 检查表单引用
    if (!editFormRef.value) {
      Message.error('表单初始化失败')
      return
    }

    const errors = await editFormRef.value.validate()
    if (errors) {
      return
    }

    // 如果有新文件，使用文件上传更新
    if (editHasNewFile.value && editFileList.value.length > 0) {
      const fileItem = editFileList.value[0]
      const file = fileItem.originFile || fileItem.file || fileItem

      if (!file || !(file instanceof File)) {
        Message.error('文件不存在或格式不正确')
        return
      }

      // 创建FormData上传新文件
      const formData = new FormData()
      formData.append('file', file)
      formData.append('id', editForm.id?.toString() || '')
      formData.append('title', editForm.title)
      formData.append('description', editForm.description || '')
      // 使用选择的分类
      const categoryId = editForm.category
      formData.append('category_id', categoryId?.toString() || '')

      const response = await updateMaterialWithFile(formData)
      console.log('文件更新响应:', response)

      if (response.data.success) {
        Message.success('素材更新成功')
        editModalVisible.value = false
        resetEditForm()
        await fetchMaterialList()
      } else {
        Message.error(response.data.msg || '素材更新失败')
      }
    } else {
      // 只更新信息，不替换文件
      // 使用选择的分类
      const categoryId = editForm.category
      const response = await updateMaterial(editForm.id!, {
        title: editForm.title,
        description: editForm.description,
        category_id: categoryId
      })
      console.log('信息更新响应:', response)

      if (response.data.success) {
        Message.success('素材信息更新成功')
        editModalVisible.value = false
        resetEditForm()
        await fetchMaterialList()
      } else {
        Message.error(response.data.msg || '更新失败')
      }
    }
  } catch (error: any) {
    console.error('更新素材失败:', error)
    Message.error('更新素材失败: ' + (error.message || '未知错误'))
  }
}

// 取消编辑
const handleEditCancel = () => {
  editModalVisible.value = false
  resetEditForm()
}

// 重置编辑表单
const resetEditForm = () => {
  editForm.id = null
  editForm.title = ''
  editForm.description = ''
  editForm.category = null
  editFileList.value = []
  editHasNewFile.value = false
  currentEditMaterial.value = null
}

const deleteMaterial = (record: any) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除素材"${record.title}"吗？`,
    onOk: async () => {
      try {
        await deleteMaterialAPI(record.id)
        Message.success('删除成功')
        fetchMaterialList()
      } catch (error) {
        console.error('删除失败:', error)
        Message.error('删除失败')
      }
    }
  })
}

const handleStatusChange = async (record: any, value: boolean) => {
  try {
    await toggleMaterialStatus(record.id, value ? 1 : 0)
    record.status = value ? 1 : 0
    Message.success(`${record.title} 状态已${value ? '启用' : '禁用'}`)
  } catch (error) {
    console.error('状态切换失败:', error)
    Message.error('状态切换失败')
  }
}

// 生命周期
onMounted(() => {
  fetchCategories()
  fetchMaterialList()
})
</script>

<style scoped>
.material-list {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0;
  color: #262626;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.material-id {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  color: #86909c;
}

.thumbnail-wrapper {
  position: relative;
  width: 60px;
  height: 60px;
  border-radius: 4px;
  overflow: hidden;
  cursor: pointer;
}

.thumbnail-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.2s;
}

.thumbnail-image:hover {
  transform: scale(1.1);
}

.text-preview {
  width: 100%;
  height: 100%;
  background: #f7f8fa;
  border: 1px solid #e5e6eb;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s;
}

.text-preview:hover {
  background: #f2f3f5;
}

.text-preview span {
  font-size: 12px;
  color: #86909c;
  margin-top: 4px;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 50px;
}

.file-size {
  font-size: 12px;
  color: #86909c;
  margin-top: 2px;
}

.upload-area {
  width: 100%;
  min-height: 200px;
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: border-color 0.3s;
}

.upload-area:hover {
  border-color: #1890ff;
}

.upload-placeholder {
  text-align: center;
  color: #999;
}

.upload-tip {
  font-size: 12px;
  color: #ccc;
  margin-top: 8px;
}

.preview-container {
  position: relative;
  width: 100%;
  height: 200px;
}

.preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 4px;
}

.upload-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  color: white;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s;
}

.preview-container:hover .upload-overlay {
  opacity: 1;
}

.preview-content {
  max-height: 70vh;
  overflow-y: auto;
}

.image-preview {
  text-align: center;
  margin-bottom: 16px;
}

.preview-full-image {
  max-width: 100%;
  max-height: 400px;
  border-radius: 4px;
}

.text-preview-content {
  margin-bottom: 16px;
}

.text-content {
  background: #f7f8fa;
  padding: 16px;
  border-radius: 4px;
  white-space: pre-wrap;
  word-break: break-word;
}

.preview-info {
  border-top: 1px solid #e5e6eb;
  padding-top: 16px;
}

.current-image-preview {
  margin-bottom: 16px;
  text-align: center;
}

.current-image-preview img {
  border: 1px solid #e5e6eb;
  border-radius: 4px;
}

.upload-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 16px;
  border: 1px dashed #c9cdd4;
  border-radius: 4px;
  background-color: #fafbfc;
  cursor: pointer;
  transition: all 0.3s;
}

.upload-button:hover {
  border-color: #165dff;
  background-color: #f2f3ff;
}
</style>
