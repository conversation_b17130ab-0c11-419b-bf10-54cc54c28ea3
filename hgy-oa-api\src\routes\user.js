import express from 'express';
import { query, paginate } from '../config/database.js';

const router = express.Router();

// 获取用户列表
router.get('/list', async (req, res) => {
  try {
    const { pageNum = 1, pageSize = 10, keyword = '', status = '', role = '' } = req.query;
    
    let sql = `
      SELECT
        u.id, u.username, u.nickname, u.avatar_url as avatar, u.created_at, u.updated_at,
        u.is_active, u.last_login, u.department_id, u.role,
        d.name as department_name, d.code as department_code
      FROM system_users u
      LEFT JOIN system_depts d ON u.department_id = d.id
      WHERE 1=1
    `;
    const params = [];

    // 关键词搜索
    if (keyword) {
      sql += ` AND (u.username LIKE ? OR u.nickname LIKE ?)`;
      const searchTerm = `%${keyword}%`;
      params.push(searchTerm, searchTerm);
    }

    // 状态筛选
    if (status) {
      sql += ` AND u.is_active = ?`;
      params.push(status);
    }

    sql += ` ORDER BY u.created_at DESC`;

    const result = await paginate(sql, params, pageNum, pageSize);
    
    res.success(result, '获取用户列表成功');
  } catch (error) {
    console.error('获取用户列表失败:', error);
    res.error('获取用户列表失败', 500);
  }
});

// 获取用户详情
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    const users = await query(`
      SELECT
        u.id, u.username, u.nickname, u.avatar_url as avatar, u.created_at, u.updated_at,
        u.is_active, u.last_login, u.department_id, u.role,
        d.name as department_name, d.code as department_code
      FROM system_users u
      LEFT JOIN system_depts d ON u.department_id = d.id
      WHERE u.id = ?
    `, [id]);
    
    if (users.length === 0) {
      return res.error('用户不存在', 404);
    }
    
    res.success(users[0], '获取用户详情成功');
  } catch (error) {
    console.error('获取用户详情失败:', error);
    res.error('获取用户详情失败', 500);
  }
});

// 创建用户
router.post('/', async (req, res) => {
  try {
    const { username, nickname, password, department_id, role = 'user', is_active = 1, status } = req.body;
    // 兼容旧的status字段
    const activeStatus = is_active !== undefined ? is_active : status !== undefined ? status : 1;

    if (!username || !password) {
      return res.error('用户名和密码不能为空', 400);
    }

    // 验证角色值
    const validRoles = ['admin', 'manager', 'designer', 'developer', 'salesman', 'guest'];
    if (!validRoles.includes(role)) {
      return res.error('无效的角色类型', 400);
    }

    // 检查用户名是否已存在
    const existingUsers = await query('SELECT id FROM system_users WHERE username = ?', [username]);
    if (existingUsers.length > 0) {
      return res.error('用户名已存在', 400);
    }

    // 简单密码加密（实际项目中应使用bcrypt）
    const hashedPassword = Buffer.from(password).toString('base64');

    const result = await query(
      `INSERT INTO system_users (username, nickname, password_hash, department_id, role, is_active, created_at, updated_at)
       VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())`,
      [username, nickname || null, hashedPassword, department_id || null, role, activeStatus]
    );

    res.success({ id: result.insertId }, '创建用户成功');
  } catch (error) {
    console.error('创建用户失败:', error);
    res.error('创建用户失败', 500);
  }
});

// 更新用户
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { username, nickname, department_id, role, is_active, status } = req.body;
    // 兼容旧的status字段
    const activeStatus = is_active !== undefined ? is_active : status;

    if (!username) {
      return res.error('用户名不能为空', 400);
    }

    // 验证角色值（如果提供了角色）
    if (role) {
      const validRoles = ['admin', 'manager', 'designer', 'developer', 'salesman', 'guest'];
      if (!validRoles.includes(role)) {
        return res.error('无效的角色类型', 400);
      }
    }

    // 检查用户是否存在
    const existingUsers = await query('SELECT id FROM system_users WHERE id = ?', [id]);
    if (existingUsers.length === 0) {
      return res.error('用户不存在', 404);
    }

    // 检查用户名是否被其他用户使用
    const duplicateUsers = await query(
      'SELECT id FROM system_users WHERE username = ? AND id != ?',
      [username, id]
    );
    if (duplicateUsers.length > 0) {
      return res.error('用户名已被其他用户使用', 400);
    }

    // 构建更新SQL
    let updateSql = `UPDATE system_users SET username = ?, nickname = ?, department_id = ?, role = ?, is_active = ?, updated_at = NOW() WHERE id = ?`;
    let updateParams = [
      username,
      nickname || null,
      department_id || null,
      role || 'user',
      activeStatus !== undefined ? activeStatus : 1,
      id
    ];

    await query(updateSql, updateParams);

    res.success(null, '更新用户成功');
  } catch (error) {
    console.error('更新用户失败:', error);
    res.error('更新用户失败', 500);
  }
});

// 删除用户
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    // 检查用户是否存在
    const existingUsers = await query('SELECT id FROM system_users WHERE id = ?', [id]);
    if (existingUsers.length === 0) {
      return res.error('用户不存在', 404);
    }

    await query('DELETE FROM system_users WHERE id = ?', [id]);
    
    res.success(null, '删除用户成功');
  } catch (error) {
    console.error('删除用户失败:', error);
    res.error('删除用户失败', 500);
  }
});

// 批量删除用户
router.delete('/batch/:ids', async (req, res) => {
  try {
    const { ids } = req.params;
    const userIds = ids.split(',').map(id => parseInt(id)).filter(id => !isNaN(id));
    
    if (userIds.length === 0) {
      return res.error('请提供有效的用户ID', 400);
    }

    const placeholders = userIds.map(() => '?').join(',');
    await query(`DELETE FROM system_users WHERE id IN (${placeholders})`, userIds);
    
    res.success(null, `成功删除 ${userIds.length} 个用户`);
  } catch (error) {
    console.error('批量删除用户失败:', error);
    res.error('批量删除用户失败', 500);
  }
});

// 更新用户状态
router.patch('/:id/status', async (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body;
    
    if (status === undefined) {
      return res.error('状态值不能为空', 400);
    }

    // 检查用户是否存在
    const existingUsers = await query('SELECT id FROM system_users WHERE id = ?', [id]);
    if (existingUsers.length === 0) {
      return res.error('用户不存在', 404);
    }

    await query('UPDATE system_users SET is_active = ?, updated_at = NOW() WHERE id = ?', [status, id]);
    
    res.success(null, '更新用户状态成功');
  } catch (error) {
    console.error('更新用户状态失败:', error);
    res.error('更新用户状态失败', 500);
  }
});

// 重置用户密码
router.patch('/:id/password', async (req, res) => {
  try {
    const { id } = req.params;
    const { password } = req.body;
    
    if (!password) {
      return res.error('新密码不能为空', 400);
    }

    // 检查用户是否存在
    const existingUsers = await query('SELECT id FROM system_users WHERE id = ?', [id]);
    if (existingUsers.length === 0) {
      return res.error('用户不存在', 404);
    }

    // 简单密码加密
    const hashedPassword = Buffer.from(password).toString('base64');

    await query('UPDATE system_users SET password_hash = ?, updated_at = NOW() WHERE id = ?', [hashedPassword, id]);
    
    res.success(null, '重置密码成功');
  } catch (error) {
    console.error('重置密码失败:', error);
    res.error('重置密码失败', 500);
  }
});

// 获取用户统计信息
router.get('/stats/overview', async (req, res) => {
  try {
    const totalUsers = await query('SELECT COUNT(*) as count FROM system_users');
    const activeUsers = await query('SELECT COUNT(*) as count FROM system_users WHERE is_active = 1');
    const todayRegistered = await query(
      'SELECT COUNT(*) as count FROM system_users WHERE DATE(created_at) = CURDATE()'
    );

    const stats = {
      total: totalUsers[0].count,
      active: activeUsers[0].count,
      admin: 0, // 暂时设为0，因为没有role字段
      todayRegistered: todayRegistered[0].count
    };

    res.success(stats, '获取用户统计成功');
  } catch (error) {
    console.error('获取用户统计失败:', error);
    res.error('获取用户统计失败', 500);
  }
});

export default router;
