import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { query } from '../src/config/database.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function initDesignTables() {
  try {
    console.log('开始创建设计任务相关表...');

    // 创建设计任务表
    await query(`
      CREATE TABLE IF NOT EXISTS design_tasks (
        id INT AUTO_INCREMENT PRIMARY KEY,
        task_name VARCHAR(200) NOT NULL COMMENT '任务名称',
        task_type VARCHAR(50) NOT NULL COMMENT '任务类型',
        assigner VARCHAR(100) NOT NULL COMMENT '派发人',
        department VARCHAR(100) NOT NULL COMMENT '所属部门',
        assign_time DATETIME NOT NULL COMMENT '派发时间',
        delivery_time DATETIME NOT NULL COMMENT '交付时间',
        status VARCHAR(50) NOT NULL DEFAULT '待处理' COMMENT '处理状态',
        rating INT DEFAULT 0 COMMENT '任务评级(1-5星)',
        assignee VARCHAR(100) COMMENT '指定处理人',
        complete_time DATETIME NULL COMMENT '完成时间',
        storage_location VARCHAR(500) COMMENT '存储位置',
        creator VARCHAR(100) NOT NULL COMMENT '任务创建人',
        remarks TEXT COMMENT '任务备注',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        deleted_at TIMESTAMP NULL DEFAULT NULL COMMENT '删除时间'
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='设计任务表'
    `);
    console.log('✅ 设计任务表创建成功');

    // 创建设计子任务表
    await query(`
      CREATE TABLE IF NOT EXISTS design_subtasks (
        id INT AUTO_INCREMENT PRIMARY KEY,
        task_id INT NOT NULL COMMENT '主任务ID',
        name VARCHAR(200) NOT NULL COMMENT '子任务名称',
        type VARCHAR(50) COMMENT '子任务类型',
        assignee VARCHAR(100) COMMENT '处理人',
        status VARCHAR(50) DEFAULT '待处理' COMMENT '状态',
        description TEXT COMMENT '子任务描述',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        deleted_at TIMESTAMP NULL DEFAULT NULL COMMENT '删除时间',
        INDEX idx_task_id (task_id)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='设计子任务表'
    `);
    console.log('✅ 设计子任务表创建成功');

    // 创建索引
    try {
      await query('CREATE INDEX idx_design_tasks_status ON design_tasks(status)');
      await query('CREATE INDEX idx_design_tasks_type ON design_tasks(task_type)');
      await query('CREATE INDEX idx_design_tasks_department ON design_tasks(department)');
      await query('CREATE INDEX idx_design_tasks_assignee ON design_tasks(assignee)');
      await query('CREATE INDEX idx_design_tasks_assign_time ON design_tasks(assign_time)');
      await query('CREATE INDEX idx_design_tasks_delivery_time ON design_tasks(delivery_time)');
      await query('CREATE INDEX idx_design_tasks_deleted_at ON design_tasks(deleted_at)');
      await query('CREATE INDEX idx_design_subtasks_status ON design_subtasks(status)');
      await query('CREATE INDEX idx_design_subtasks_deleted_at ON design_subtasks(deleted_at)');
      console.log('✅ 索引创建成功');
    } catch (error) {
      console.log('⚠️ 索引可能已存在，跳过创建');
    }

    // 插入示例数据
    const existingTasks = await query('SELECT COUNT(*) as count FROM design_tasks');
    if (existingTasks[0].count === 0) {
      console.log('插入示例数据...');
      
      // 插入主任务
      const task1 = await query(`
        INSERT INTO design_tasks (
          task_name, task_type, assigner, department, assign_time, delivery_time,
          status, rating, assignee, storage_location, creator, remarks
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        '首页Banner设计', '设计', '张三', '技术部', 
        '2025-01-01 09:00:00', '2025-01-03 18:00:00',
        '处理中', 4, '李四', '/projects/banner/2025/', '王五',
        '需要响应式设计，支持移动端'
      ]);

      const task2 = await query(`
        INSERT INTO design_tasks (
          task_name, task_type, assigner, department, assign_time, delivery_time,
          status, rating, assignee, storage_location, creator, remarks
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        '产品宣传海报', '紧急', '赵六', '南方运营部',
        '2025-01-02 10:00:00', '2025-01-02 20:00:00',
        '已完成', 5, '钱七', '/projects/poster/2025/', '孙八',
        '紧急任务，需要当天完成'
      ]);

      const task3 = await query(`
        INSERT INTO design_tasks (
          task_name, task_type, assigner, department, assign_time, delivery_time,
          status, rating, assignee, storage_location, creator, remarks
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        'Logo优化设计', '优化', '周九', '技术部',
        '2025-01-03 14:00:00', '2025-01-05 17:00:00',
        '待处理', 3, '吴十', '/projects/logo/2025/', '郑十一',
        'Logo需要现代化改进，保持品牌识别度'
      ]);

      // 插入子任务
      await query(`
        INSERT INTO design_subtasks (task_id, name, type, assignee, status, description) VALUES
        (?, ?, ?, ?, ?, ?),
        (?, ?, ?, ?, ?, ?),
        (?, ?, ?, ?, ?, ?)
      `, [
        task1.insertId, '设计稿制作', '设计', '李四', '已完成', '制作首页Banner设计稿',
        task1.insertId, '移动端适配', '优化', '李四', '处理中', '适配移动端显示效果',
        task1.insertId, '交互效果设计', '设计', '李四', '待处理', '添加鼠标悬停等交互效果'
      ]);

      await query(`
        INSERT INTO design_subtasks (task_id, name, type, assignee, status, description) VALUES
        (?, ?, ?, ?, ?, ?),
        (?, ?, ?, ?, ?, ?),
        (?, ?, ?, ?, ?, ?)
      `, [
        task3.insertId, '现有Logo分析', '分析', '吴十', '待处理', '分析当前Logo的优缺点',
        task3.insertId, '设计方案制作', '设计', '吴十', '待处理', '制作3-5个优化方案',
        task3.insertId, '方案评审', '评审', '吴十', '待处理', '内部评审选择最佳方案'
      ]);

      console.log('✅ 示例数据插入成功');
    } else {
      console.log('⚠️ 数据已存在，跳过示例数据插入');
    }

    console.log('🎉 设计任务表初始化完成！');

  } catch (error) {
    console.error('❌ 初始化失败:', error);
    process.exit(1);
  }
}

// 执行初始化
initDesignTables().then(() => {
  console.log('初始化完成，程序退出');
  process.exit(0);
}).catch(error => {
  console.error('初始化失败:', error);
  process.exit(1);
});
