@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=D:\code\gitlab\oa\hgy-oa-api\node_modules\.pnpm\xlsx@0.18.5\node_modules\xlsx\bin\node_modules;D:\code\gitlab\oa\hgy-oa-api\node_modules\.pnpm\xlsx@0.18.5\node_modules\xlsx\node_modules;D:\code\gitlab\oa\hgy-oa-api\node_modules\.pnpm\xlsx@0.18.5\node_modules;D:\code\gitlab\oa\hgy-oa-api\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=D:\code\gitlab\oa\hgy-oa-api\node_modules\.pnpm\xlsx@0.18.5\node_modules\xlsx\bin\node_modules;D:\code\gitlab\oa\hgy-oa-api\node_modules\.pnpm\xlsx@0.18.5\node_modules\xlsx\node_modules;D:\code\gitlab\oa\hgy-oa-api\node_modules\.pnpm\xlsx@0.18.5\node_modules;D:\code\gitlab\oa\hgy-oa-api\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\xlsx\bin\xlsx.njs" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\xlsx\bin\xlsx.njs" %*
)
