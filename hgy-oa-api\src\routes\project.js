import express from 'express';
import { query, paginate } from '../config/database.js';
import { optionalAuth } from '../middleware/auth.js';

const router = express.Router();

// 项目分类枚举
const PROJECT_CATEGORIES = ['工具型', '创收型'];

// 项目阶段枚举
const PROJECT_STAGES = [
  '1.开发意向',
  '2.需求收集',
  '3.功能预评',
  '4.设计预评',
  '5.正式评审',
  '6.开发阶段',
  '7.测试阶段',
  '8.交付完成',
  '9.上线维护',
  '10.已经废弃'
];

// 获取项目列表
router.get('/list', optionalAuth, async (req, res) => {
  try {
    const {
      page = 1,
      pageSize = 20,
      keyword,
      category,
      current_stage
    } = req.query;

    const offset = (parseInt(page) - 1) * parseInt(pageSize);
    
    let whereClauses = ['p.id IS NOT NULL'];
    let params = [];

    if (keyword && keyword.trim() !== '') {
      whereClauses.push('(p.name LIKE ? OR p.progress_description LIKE ?)');
      params.push(`%${keyword}%`, `%${keyword}%`);
    }

    if (category && category.trim() !== '') {
      whereClauses.push('p.category = ?');
      params.push(category);
    }

    if (current_stage && current_stage.trim() !== '') {
      whereClauses.push('p.current_stage = ?');
      params.push(current_stage);
    }

    const whereClause = whereClauses.join(' AND ');

    // 先使用最简单的查询，确保能工作
    const listSql = `
      SELECT
        p.id, p.name, p.category, p.start_date, p.current_stage,
        p.product_doc_url, p.ui_design_url,
        COALESCE(p.participants, '[]') as participants,
        p.progress_description, p.launch_date, p.user_id, p.created_at, p.updated_at,
        COALESCE(u.nickname, 'Unknown') as creator_name
      FROM projects p
      LEFT JOIN users u ON p.user_id = u.id
      ORDER BY p.created_at DESC
      LIMIT 20
    `;

    console.log('使用简单查询:', listSql);

    const projects = await query(listSql, []);

    // 获取总数
    const countSql = `SELECT COUNT(*) as total FROM projects p`;
    const [countResult] = await query(countSql, []);

    // 格式化数据
    const formattedProjects = projects.map(project => ({
      ...project,
      participants: project.participants ? JSON.parse(project.participants) : [],
      start_date: project.start_date ? new Date(project.start_date).toISOString().split('T')[0] : '',
      launch_date: project.launch_date ? new Date(project.launch_date).toISOString().split('T')[0] : '',
      created_at: project.created_at ? new Date(project.created_at).toISOString().split('T')[0] : '',
      updated_at: project.updated_at ? new Date(project.updated_at).toISOString().split('T')[0] : ''
    }));

    res.success({
      list: formattedProjects,
      total: countResult.total,
      page: parseInt(page),
      pageSize: parseInt(pageSize)
    });

  } catch (error) {
    console.error('获取项目列表失败:', error);
    res.error('获取项目列表失败');
  }
});

// 获取项目详情
router.get('/:id', optionalAuth, async (req, res) => {
  try {
    const { id } = req.params;

    const projectSql = `
      SELECT
        p.id, p.name, p.category, p.start_date, p.current_stage,
        p.product_doc_url, p.ui_design_url, p.participants,
        p.progress_description, p.launch_date, p.user_id, p.created_at, p.updated_at,
        u.nickname as creator_name
      FROM projects p
      LEFT JOIN users u ON p.user_id = u.id
      WHERE p.id = ?
    `;

    const [project] = await query(projectSql, [id]);

    if (!project) {
      return res.error('项目不存在', 404);
    }

    // 格式化数据
    const formattedProject = {
      ...project,
      participants: project.participants ? JSON.parse(project.participants) : [],
      start_date: project.start_date ? new Date(project.start_date).toISOString().split('T')[0] : '',
      launch_date: project.launch_date ? new Date(project.launch_date).toISOString().split('T')[0] : '',
      created_at: project.created_at ? new Date(project.created_at).toISOString().split('T')[0] : '',
      updated_at: project.updated_at ? new Date(project.updated_at).toISOString().split('T')[0] : ''
    };

    res.success(formattedProject);

  } catch (error) {
    console.error('获取项目详情失败:', error);
    res.error('获取项目详情失败');
  }
});

// 创建项目
router.post('/', optionalAuth, async (req, res) => {
  try {
    const {
      name,
      category,
      start_date,
      current_stage = '1.开发意向',
      product_doc_url,
      ui_design_url,
      participants = [],
      progress_description,
      launch_date
    } = req.body;

    if (!name || !category || !start_date) {
      return res.error('项目名称、分类和启动日期不能为空', 400);
    }

    if (!PROJECT_CATEGORIES.includes(category)) {
      return res.error('无效的项目分类', 400);
    }

    if (!PROJECT_STAGES.includes(current_stage)) {
      return res.error('无效的项目阶段', 400);
    }

    // 获取当前用户ID
    let userId = req.user?.id || 1;
    
    // 验证用户是否存在
    const userCheck = await query('SELECT id FROM users WHERE id = ?', [userId]);
    if (userCheck.length === 0) {
      const firstUser = await query('SELECT id FROM users LIMIT 1');
      if (firstUser.length > 0) {
        userId = firstUser[0].id;
      } else {
        return res.error('系统中没有可用用户', 400);
      }
    }

    const insertSql = `
      INSERT INTO projects (
        name, category, start_date, current_stage,
        product_doc_url, ui_design_url, participants,
        progress_description, launch_date, user_id, description,
        canvas_data, width, height, is_public
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const result = await query(insertSql, [
      name,
      category,
      start_date,
      current_stage,
      product_doc_url || null,
      ui_design_url || null,
      JSON.stringify(participants),
      progress_description || '',
      launch_date || null,
      userId,
      progress_description || '',
      '{}', // 空的canvas_data
      800, // 默认宽度
      600, // 默认高度
      1 // 公开
    ]);

    res.success({ id: result.insertId }, '创建项目成功');

  } catch (error) {
    console.error('创建项目失败:', error);
    res.error('创建项目失败');
  }
});

// 更新项目
router.put('/:id', optionalAuth, async (req, res) => {
  try {
    const { id } = req.params;
    const {
      name,
      category,
      start_date,
      current_stage,
      product_doc_url,
      ui_design_url,
      participants,
      progress_description,
      launch_date
    } = req.body;

    if (!name || !category || !start_date || !current_stage) {
      return res.error('项目名称、分类、启动日期和当前阶段不能为空', 400);
    }

    if (!PROJECT_CATEGORIES.includes(category)) {
      return res.error('无效的项目分类', 400);
    }

    if (!PROJECT_STAGES.includes(current_stage)) {
      return res.error('无效的项目阶段', 400);
    }

    // 检查项目是否存在
    const [project] = await query('SELECT id FROM projects WHERE id = ?', [id]);
    if (!project) {
      return res.error('项目不存在', 404);
    }

    const updateSql = `
      UPDATE projects
      SET name = ?, category = ?, start_date = ?, current_stage = ?,
          product_doc_url = ?, ui_design_url = ?, participants = ?,
          progress_description = ?, launch_date = ?, description = ?, updated_at = NOW()
      WHERE id = ?
    `;

    await query(updateSql, [
      name,
      category,
      start_date,
      current_stage,
      product_doc_url || null,
      ui_design_url || null,
      JSON.stringify(participants || []),
      progress_description || '',
      launch_date || null,
      progress_description || '',
      id
    ]);

    res.success(null, '更新项目成功');

  } catch (error) {
    console.error('更新项目失败:', error);
    res.error('更新项目失败');
  }
});

// 删除项目
router.delete('/:id', optionalAuth, async (req, res) => {
  try {
    const { id } = req.params;

    // 检查项目是否存在
    const [project] = await query('SELECT id FROM projects WHERE id = ?', [id]);
    if (!project) {
      return res.error('项目不存在', 404);
    }

    // 删除项目
    await query('DELETE FROM projects WHERE id = ?', [id]);

    res.success(null, '删除项目成功');

  } catch (error) {
    console.error('删除项目失败:', error);
    res.error('删除项目失败');
  }
});

// 获取项目分类和阶段枚举
router.get('/enums/all', (req, res) => {
  res.success({
    categories: PROJECT_CATEGORIES,
    stages: PROJECT_STAGES
  });
});

export default router;
