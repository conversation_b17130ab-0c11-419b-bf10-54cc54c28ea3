import { request } from '@/utils/request'

// 保存模板
export const saveTemplate = (formData: FormData) => {
  return request.post('/api/template/save', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 获取模板列表
export const getTemplateList = (params: any) => {
  return request.get('/api/template/templateList', { params })
}

// 获取模板详情
export const getTemplateDetail = (id: number) => {
  return request.get(`/api/template/template/${id}`)
}

// 获取模板管理列表
export const getTemplateAdminList = (params: any) => {
  return request.get('/api/template/admin/list', { params })
}

// 更新模板信息（管理员功能）
export const updateTemplateAdmin = (id: number, data: any) => {
  return request.put(`/api/template/admin/${id}`, data)
}

// 删除模板（管理员功能）
export const deleteTemplateAdmin = (id: number) => {
  return request.delete(`/api/template/admin/${id}`)
}

// 获取用户模板列表
export const getUserTemplates = (params?: any) => {
  return request.get('/api/template/user/list', { params })
}

// 删除用户模板
export const deleteUserTemplate = (id: number) => {
  return request.delete(`/api/template/user/${id}`)
}

// 更新用户模板
export const updateUserTemplate = (id: number, data: any) => {
  return request.put(`/api/template/user/${id}`, data)
}

// 模板相关类型定义
export interface Template {
  id: number
  name: string
  description?: string
  category: string
  category_id?: number
  thumbnail_url?: string
  canvas_data: any
  width: number
  height: number
  is_public: number
  is_featured?: number
  created_by: number
  created_at: string
  updated_at: string
  usage_count: number
  tags?: any
  cover?: string
  creator_name?: string
  category_name?: string
  parent_category_name?: string
  category_display?: string
}

export interface SaveTemplateData {
  name: string
  description?: string
  category?: string
  canvas_data: any
  width: number
  height: number
  is_public?: number
  tags?: any[]
}
