import fs from 'fs-extra';
import path from 'path';
import { fileURLToPath } from 'url';
import { query, testConnection } from '../src/config/database.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 数据文件路径
const dataDir = path.join(__dirname, '../../src/assets/data');

// 读取JSON文件
const readJsonFile = (filename) => {
  const filePath = path.join(dataDir, filename);
  if (fs.existsSync(filePath)) {
    return JSON.parse(fs.readFileSync(filePath, 'utf8'));
  }
  return null;
};

// 迁移模板数据
const migrateTemplates = async () => {
  console.log('开始迁移模板数据...');
  const templateData = readJsonFile('templateData.json');
  
  if (!templateData || !templateData.list) {
    console.log('模板数据文件不存在或格式错误');
    return;
  }

  let successCount = 0;
  let errorCount = 0;

  for (const template of templateData.list) {
    try {
      const sql = `
        INSERT INTO templates (id, cover, title, json, state, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, NOW(), NOW())
        ON DUPLICATE KEY UPDATE
        cover = VALUES(cover),
        title = VALUES(title),
        json = VALUES(json),
        state = VALUES(state),
        updated_at = NOW()
      `;
      
      const jsonStr = JSON.stringify(template.json);
      await query(sql, [template.id, template.cover, template.title, jsonStr, template.state]);
      successCount++;
    } catch (error) {
      console.error(`迁移模板 ${template.id} 失败:`, error.message);
      errorCount++;
    }
  }

  console.log(`模板数据迁移完成: 成功 ${successCount} 条, 失败 ${errorCount} 条`);
};

// 迁移文字素材数据
const migrateTextMaterials = async () => {
  console.log('开始迁移文字素材数据...');
  const textData = readJsonFile('textData.json');
  
  if (!textData || !textData.list) {
    console.log('文字素材数据文件不存在或格式错误');
    return;
  }

  let successCount = 0;
  let errorCount = 0;

  for (const item of textData.list) {
    try {
      const sql = `
        INSERT INTO text_materials (title, content, preview_url, tags, status, created_at, updated_at)
        VALUES (?, ?, ?, ?, 1, NOW(), NOW())
      `;
      
      const tags = item.tags ? JSON.stringify(item.tags) : null;
      await query(sql, [item.title || item.name, item.content || item.text, item.preview_url, tags]);
      successCount++;
    } catch (error) {
      console.error(`迁移文字素材失败:`, error.message);
      errorCount++;
    }
  }

  console.log(`文字素材数据迁移完成: 成功 ${successCount} 条, 失败 ${errorCount} 条`);
};

// 迁移图片素材数据
const migrateImageMaterials = async () => {
  console.log('开始迁移图片素材数据...');
  const imageData = readJsonFile('imageData.json');
  
  if (!imageData || !imageData.list) {
    console.log('图片素材数据文件不存在或格式错误');
    return;
  }

  let successCount = 0;
  let errorCount = 0;

  for (const item of imageData.list) {
    try {
      const sql = `
        INSERT INTO image_materials (title, content, preview_url, tags, status, created_at, updated_at)
        VALUES (?, ?, ?, ?, 1, NOW(), NOW())
      `;
      
      const tags = item.tags ? JSON.stringify(item.tags) : null;
      await query(sql, [item.title || item.name, item.url || item.content, item.preview_url || item.url, tags]);
      successCount++;
    } catch (error) {
      console.error(`迁移图片素材失败:`, error.message);
      errorCount++;
    }
  }

  console.log(`图片素材数据迁移完成: 成功 ${successCount} 条, 失败 ${errorCount} 条`);
};

// 迁移字体数据
const migrateFonts = async () => {
  console.log('开始迁移字体数据...');
  const fontData = readJsonFile('fonts.json');
  
  if (!fontData || !fontData.list) {
    console.log('字体数据文件不存在或格式错误');
    return;
  }

  let successCount = 0;
  let errorCount = 0;

  for (const font of fontData.list) {
    try {
      const sql = `
        INSERT INTO fonts (name, code, preview_url, download_url, font_family, font_weight, font_style, is_system, status, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, 1, NOW(), NOW())
        ON DUPLICATE KEY UPDATE
        name = VALUES(name),
        preview_url = VALUES(preview_url),
        download_url = VALUES(download_url),
        font_family = VALUES(font_family),
        font_weight = VALUES(font_weight),
        font_style = VALUES(font_style),
        is_system = VALUES(is_system),
        updated_at = NOW()
      `;
      
      await query(sql, [
        font.name,
        font.code,
        font.preview || font.preview_url,
        font.download || font.download_url,
        font.fontFamily || font.font_family || font.code,
        font.fontWeight || font.font_weight || 'normal',
        font.fontStyle || font.font_style || 'normal',
        font.isSystem || font.is_system ? 1 : 0
      ]);
      successCount++;
    } catch (error) {
      console.error(`迁移字体 ${font.code} 失败:`, error.message);
      errorCount++;
    }
  }

  console.log(`字体数据迁移完成: 成功 ${successCount} 条, 失败 ${errorCount} 条`);
};

// 迁移图形数据
const migrateGraphs = async () => {
  console.log('开始迁移图形数据...');
  const graphData = readJsonFile('graphData.json');
  
  if (!graphData) {
    console.log('图形数据文件不存在或格式错误');
    return;
  }

  // 先迁移分类
  if (graphData.cate && Array.isArray(graphData.cate)) {
    console.log('迁移图形分类...');
    for (const category of graphData.cate) {
      try {
        const sql = `
          INSERT INTO graph_categories (id, name, icon, sort_order, status, created_at, updated_at)
          VALUES (?, ?, ?, ?, 1, NOW(), NOW())
          ON DUPLICATE KEY UPDATE
          name = VALUES(name),
          icon = VALUES(icon),
          sort_order = VALUES(sort_order),
          updated_at = NOW()
        `;
        
        await query(sql, [category.id, category.name, category.icon, category.sort || 0]);
      } catch (error) {
        console.error(`迁移图形分类 ${category.id} 失败:`, error.message);
      }
    }
  }

  // 再迁移图形数据
  if (graphData.list && Array.isArray(graphData.list)) {
    let successCount = 0;
    let errorCount = 0;

    for (const graph of graphData.list) {
      try {
        const sql = `
          INSERT INTO graphs (name, preview_url, svg_content, category_id, tags, width, height, status, created_at, updated_at)
          VALUES (?, ?, ?, ?, ?, ?, ?, 1, NOW(), NOW())
        `;
        
        const tags = graph.tags ? JSON.stringify(graph.tags) : null;
        await query(sql, [
          graph.name,
          graph.preview_url || graph.preview,
          graph.svg || graph.svg_content,
          graph.category,
          tags,
          graph.width,
          graph.height
        ]);
        successCount++;
      } catch (error) {
        console.error(`迁移图形失败:`, error.message);
        errorCount++;
      }
    }

    console.log(`图形数据迁移完成: 成功 ${successCount} 条, 失败 ${errorCount} 条`);
  }
};

// 主迁移函数
const migrate = async () => {
  console.log('开始数据迁移...');
  
  // 测试数据库连接
  const connected = await testConnection();
  if (!connected) {
    console.error('数据库连接失败，请检查配置');
    process.exit(1);
  }

  try {
    await migrateTemplates();
    await migrateTextMaterials();
    await migrateImageMaterials();
    await migrateFonts();
    await migrateGraphs();
    
    console.log('所有数据迁移完成！');
  } catch (error) {
    console.error('数据迁移过程中发生错误:', error);
  }
};

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  migrate();
}

export default migrate;
