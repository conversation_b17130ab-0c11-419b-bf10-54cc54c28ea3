{"tableName": "element_categories", "createSQL": "CREATE TABLE `element_categories` (\n  `id` int NOT NULL AUTO_INCREMENT,\n  `name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,\n  `sort_order` int DEFAULT '0',\n  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,\n  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,\n  `icon` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '分类图标URL',\n  `status` tinyint(1) DEFAULT '1' COMMENT '状态：1-启用，0-禁用',\n  PRIMARY KEY (`id`),\n  KEY `idx_sort_order` (`sort_order`)\n) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci", "data": [{"id": 1, "name": "箭头", "sort_order": 0, "created_at": "2025-06-30T06:22:01.000Z", "updated_at": "2025-07-01T03:52:46.000Z", "icon": null, "status": 1}, {"id": 2, "name": "圆环", "sort_order": 0, "created_at": "2025-06-30T06:22:01.000Z", "updated_at": "2025-07-01T03:52:46.000Z", "icon": null, "status": 1}, {"id": 3, "name": "线条", "sort_order": 0, "created_at": "2025-06-30T06:22:01.000Z", "updated_at": "2025-07-01T03:52:46.000Z", "icon": null, "status": 1}, {"id": 4, "name": "星标", "sort_order": 0, "created_at": "2025-06-30T06:22:01.000Z", "updated_at": "2025-07-01T03:52:46.000Z", "icon": null, "status": 1}], "backupTime": "2025-07-08T08:47:22.631Z", "recordCount": 4}