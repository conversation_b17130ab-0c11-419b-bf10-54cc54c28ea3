<template>
  <div class="material-wrap">
    <div class="material-list">
      <div 
        v-for="item in materialList" 
        :key="item.id"
        class="material-item"
        @click="addToCanvas(item)"
      >
        <div class="material-preview">
          <img 
            v-if="item.preview_url || item.thumbnail_path || item.file_path" 
            :src="item.preview_url || item.thumbnail_path || item.file_path" 
            :alt="item.title"
            @error="handleImageError"
          />
          <div v-else class="no-preview">
            <icon-image :size="32" />
          </div>
        </div>
        <div class="material-info">
          <div class="material-title">{{ item.title }}</div>
        </div>
      </div>
    </div>
    
    <!-- 加载更多 -->
    <div v-if="hasMore" class="load-more" @click="loadMore">
      <a-button :loading="loading" type="text">
        {{ loading ? '加载中...' : '加载更多' }}
      </a-button>
    </div>
    
    <!-- 空状态 -->
    <div v-if="!loading && materialList.length === 0" class="empty-state">
      <icon-empty :size="48" />
      <p>暂无{{ categoryName }}素材</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, defineProps } from 'vue'
import { getMaterialList } from '@/api/material'
import { useEditor } from '@/views/Editor/app'
import { Text, Image } from 'leafer-ui'
import { getDefaultName } from '@/views/Editor/utils/utils'

// 接收props
const props = defineProps<{
  categoryId: number
  categoryName: string
  active: boolean
}>()

// 响应式数据
const materialList = ref<any[]>([])
const loading = ref(false)
const hasMore = ref(true)
const page = ref(1)
const pageSize = 20

// 获取编辑器实例，处理可能的undefined情况
const editorInstance = useEditor()
const editor = editorInstance?.editor

// 获取素材列表
const fetchMaterialList = async (isLoadMore = false) => {
  if (loading.value) return
  
  try {
    loading.value = true
    
    const params = {
      page: isLoadMore ? page.value : 1,
      pageSize,
      categoryId: props.categoryId
    }
    
    console.log(`获取${props.categoryName}素材列表:`, params)
    
    const response = await getMaterialList(params)
    
    if (response.success) {
      const newList = response.data.list || []
      
      if (isLoadMore) {
        materialList.value = [...materialList.value, ...newList]
      } else {
        materialList.value = newList
        page.value = 1
      }
      
      // 判断是否还有更多数据
      hasMore.value = newList.length === pageSize
      
      console.log(`${props.categoryName}素材加载成功，共 ${materialList.value.length} 个`)
    } else {
      console.error(`获取${props.categoryName}素材失败:`, response.msg)
    }
  } catch (error) {
    console.error(`获取${props.categoryName}素材失败:`, error)
  } finally {
    loading.value = false
  }
}

// 加载更多
const loadMore = () => {
  if (hasMore.value && !loading.value) {
    page.value++
    fetchMaterialList(true)
  }
}

// 添加到画布
const addToCanvas = async (item: any) => {
  console.log(`添加${props.categoryName}素材到画布:`, item)

  if (!editor) {
    console.error('编辑器未初始化')
    return
  }
  
  try {
    // 根据素材类型创建相应的元素并添加到画布
    let element: any = null

    if (props.categoryName === '文字') {
      // 创建文字元素
      element = new Text({
        name: getDefaultName(editor.contentFrame),
        editable: true,
        x: 100,
        y: 100,
        text: item.content || item.title || '文字内容',
        fontSize: 24,
        fill: [{
          type: 'solid',
          color: '#000000'
        }],
        fontFamily: 'Arial, sans-serif'
      })
    } else if (props.categoryName === '背景') {
      // 创建背景图片元素
      element = new Image({
        name: getDefaultName(editor.contentFrame),
        editable: true,
        x: 0,
        y: 0,
        width: editor.contentFrame.width,
        height: editor.contentFrame.height,
        url: item.file_path || item.url
      })
    } else {
      // 创建图片素材元素
      element = new Image({
        name: getDefaultName(editor.contentFrame),
        editable: true,
        x: 100,
        y: 100,
        width: 200,
        height: 200,
        url: item.file_path || item.url
      })
    }

    if (element) {
      console.log('准备添加元素到画布:', element)

      // 使用editor.add方法添加元素
      editor.add(element)

      console.log(`${props.categoryName}素材添加成功`)
    }
  } catch (error) {
    console.error(`添加${props.categoryName}素材失败:`, error)
  }
}

// 图片加载错误处理
const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.style.display = 'none'
}

// 组件挂载时获取数据
onMounted(() => {
  console.log(`MaterialWrap 组件挂载 - ${props.categoryName}`)
  fetchMaterialList()
})
</script>

<style scoped>
.material-wrap {
  padding: 16px;
  height: 100%;
  overflow-y: auto;
}

.material-list {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
  margin-bottom: 16px;
}

.material-item {
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.2s;
  background: #fff;
}

.material-item:hover {
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.material-preview {
  width: 100%;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
  overflow: hidden;
}

.material-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.no-preview {
  color: #ccc;
}

.material-info {
  padding: 8px;
}

.material-title {
  font-size: 12px;
  color: #333;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.load-more {
  text-align: center;
  padding: 16px 0;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #999;
}

.empty-state p {
  margin-top: 12px;
  font-size: 14px;
}
</style>
