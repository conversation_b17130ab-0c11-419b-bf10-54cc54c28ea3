<template>
  <div class="product-container">
    <!-- 操作栏 -->
    <div class="action-bar">
      <a-space>
        <a-button type="primary" @click="showCreateModal">
          <template #icon>
            <icon-plus />
          </template>
          新建项目
        </a-button>
        <a-input-search
          v-model="filters.keyword"
          placeholder="搜索项目名称或描述"
          style="width: 300px"
          @search="fetchProjectList"
          @clear="fetchProjectList"
          allow-clear
        />
        <a-select
          v-model="filters.category"
          placeholder="项目分类"
          style="width: 120px"
          allow-clear
          @change="fetchProjectList"
        >
          <a-option value="">全部分类</a-option>
          <a-option
            v-for="category in enums.categories"
            :key="category"
            :value="category"
          >
            {{ category }}
          </a-option>
        </a-select>
        <a-select
          v-model="filters.current_stage"
          placeholder="当前阶段"
          style="width: 140px"
          allow-clear
          @change="fetchProjectList"
        >
          <a-option value="">全部阶段</a-option>
          <a-option
            v-for="stage in enums.stages"
            :key="stage"
            :value="stage"
          >
            {{ stage }}
          </a-option>
        </a-select>
        <a-button @click="resetFilters">重置</a-button>
        <a-button @click="fetchProjectList">
          <template #icon>
            <icon-refresh />
          </template>
          刷新
        </a-button>
      </a-space>
    </div>

    <!-- 项目表格 -->
    <a-table
      :columns="columns"
      :data="projectData"
      :loading="loading"
      :pagination="pagination"
      @page-change="handlePageChange"
      @page-size-change="handlePageSizeChange"
      row-key="id"
    >
      <template #category="{ record }">
        <a-tag :color="record.category === '工具型' ? 'blue' : 'green'">
          {{ record.category }}
        </a-tag>
      </template>

      <template #current_stage="{ record }">
        <a-tag :color="getStageColor(record.current_stage)">
          {{ record.current_stage }}
        </a-tag>
      </template>

      <template #product_doc_url="{ record }">
        <a v-if="record.product_doc_url" :href="record.product_doc_url" target="_blank">
          查看文档
        </a>
        <span v-else>-</span>
      </template>

      <template #ui_design_url="{ record }">
        <a v-if="record.ui_design_url" :href="record.ui_design_url" target="_blank">
          查看设计
        </a>
        <span v-else>-</span>
      </template>

      <template #participants="{ record }">
        <div class="participants">
          <a-tag
            v-for="(participant, index) in record.participants.slice(0, 3)"
            :key="index"
            size="small"
          >
            {{ participant.name }}({{ participant.role }})
          </a-tag>
          <span v-if="record.participants.length > 3" class="more-count">
            +{{ record.participants.length - 3 }}
          </span>
        </div>
      </template>

      <template #progress_description="{ record }">
        <div class="progress-desc">
          {{ record.progress_description || '-' }}
        </div>
      </template>

      <template #actions="{ record }">
        <a-space size="mini">
          <a-button type="text" size="small" @click="showEditModal(record)">
            编辑
          </a-button>
          <a-button type="text" size="small" status="danger" @click="deleteProject(record)">
            删除
          </a-button>
        </a-space>
      </template>
    </a-table>

    <!-- 新建/编辑项目弹窗 -->
    <a-modal
      v-model:visible="modalVisible"
      :title="isEdit ? '编辑项目' : '新建项目'"
      width="800px"
      @ok="handleSubmit"
      @cancel="handleCancel"
    >
      <a-form ref="formRef" :model="form" :rules="rules" layout="vertical">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="项目名称" field="name" required>
              <a-input v-model="form.name" placeholder="请输入项目名称" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="项目分类" field="category" required>
              <a-select v-model="form.category" placeholder="请选择项目分类">
                <a-option
                  v-for="category in enums.categories"
                  :key="category"
                  :value="category"
                >
                  {{ category }}
                </a-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item label="启动日期" field="start_date" required>
              <a-date-picker
                v-model="form.start_date"
                style="width: 100%"
                placeholder="请选择启动日期"
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="当前阶段" field="current_stage" required>
              <a-select v-model="form.current_stage" placeholder="请选择当前阶段">
                <a-option
                  v-for="stage in enums.stages"
                  :key="stage"
                  :value="stage"
                >
                  {{ stage }}
                </a-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="上线日期" field="launch_date">
              <a-date-picker
                v-model="form.launch_date"
                style="width: 100%"
                placeholder="请选择上线日期"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="产品文档" field="product_doc_url">
              <a-input v-model="form.product_doc_url" placeholder="请输入产品文档链接" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="UI设计稿" field="ui_design_url">
              <a-input v-model="form.ui_design_url" placeholder="请输入UI设计稿链接" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-form-item label="参与者" field="participants">
          <div class="participants-form">
            <div
              v-for="(participant, index) in form.participants"
              :key="index"
              class="participant-item"
            >
              <a-input
                v-model="participant.name"
                placeholder="姓名"
                style="width: 120px; margin-right: 8px"
              />
              <a-input
                v-model="participant.role"
                placeholder="角色"
                style="width: 120px; margin-right: 8px"
              />
              <a-button
                type="text"
                status="danger"
                @click="removeParticipant(index)"
              >
                删除
              </a-button>
            </div>
            <a-button type="dashed" @click="addParticipant">
              <template #icon>
                <icon-plus />
              </template>
              添加参与者
            </a-button>
          </div>
        </a-form-item>

        <a-form-item label="进展描述" field="progress_description">
          <a-textarea
            v-model="form.progress_description"
            placeholder="请输入项目进展描述"
            :rows="4"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { Message, Modal } from '@arco-design/web-vue'
import { IconRefresh } from '@arco-design/web-vue/es/icon'
import {
  getProjectList,
  createProject,
  updateProject,
  deleteProject as deleteProjectApi,
  getProjectEnums,
  type Project,
  type ProjectEnums
} from '@/api/project'

// 响应式数据
const loading = ref(false)
const projectData = ref<Project[]>([])
const modalVisible = ref(false)
const isEdit = ref(false)
const formRef = ref()

// 枚举数据
const enums = reactive<ProjectEnums>({
  categories: [],
  stages: []
})

// 筛选条件
const filters = reactive({
  keyword: '',
  category: '',
  current_stage: ''
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0,
  showTotal: true,
  showPageSize: true
})

// 表单数据
const form = reactive({
  id: null as number | null,
  name: '',
  category: '',
  start_date: '',
  current_stage: '1.开发意向',
  launch_date: '',
  product_doc_url: '',
  ui_design_url: '',
  participants: [] as Array<{ name: string; role: string }>,
  progress_description: ''
})

// 表格列配置
const columns = [
  {
    title: '项目名称',
    dataIndex: 'name',
    width: 180
  },
  {
    title: '项目分类',
    dataIndex: 'category',
    slotName: 'category',
    width: 100
  },
  {
    title: '启动日期',
    dataIndex: 'start_date',
    width: 110
  },
  {
    title: '当前阶段',
    dataIndex: 'current_stage',
    slotName: 'current_stage',
    width: 120
  },
  {
    title: '上线日期',
    dataIndex: 'launch_date',
    width: 110
  },
  {
    title: '产品文档',
    dataIndex: 'product_doc_url',
    slotName: 'product_doc_url',
    width: 100
  },
  {
    title: 'UI设计稿',
    dataIndex: 'ui_design_url',
    slotName: 'ui_design_url',
    width: 100
  },
  {
    title: '参与者',
    dataIndex: 'participants',
    slotName: 'participants',
    width: 180
  },
  {
    title: '进度描述',
    dataIndex: 'progress_description',
    slotName: 'progress_description',
    width: 220
  },
  {
    title: '操作',
    slotName: 'actions',
    width: 120,
    fixed: 'right'
  }
]

// 表单验证规则
const rules = {
  name: [{ required: true, message: '请输入项目名称' }],
  category: [{ required: true, message: '请选择项目分类' }],
  start_date: [{ required: true, message: '请选择启动日期' }],
  current_stage: [{ required: true, message: '请选择当前阶段' }]
}

// 获取阶段颜色
const getStageColor = (stage: string) => {
  const colorMap: Record<string, string> = {
    '1.开发意向': 'gray',
    '2.需求收集': 'blue',
    '3.功能预评': 'cyan',
    '4.设计预评': 'purple',
    '5.正式评审': 'orange',
    '6.开发阶段': 'arcoblue',
    '7.测试阶段': 'lime',
    '8.交付完成': 'cyan',
    '9.上线维护': 'green',
    '10.已经废弃': 'red'
  }
  return colorMap[stage] || 'gray'
}

// 获取项目列表
const fetchProjectList = async () => {
  try {
    loading.value = true
    const response = await getProjectList({
      page: pagination.current,
      pageSize: pagination.pageSize,
      ...filters
    })

    console.log('项目列表API响应:', response)

    if (response.success) {
      projectData.value = response.data.list
      pagination.total = response.data.total

    } else {
      Message.error(response.msg || '获取项目列表失败')
    }
  } catch (error) {
    console.error('获取项目列表失败:', error)
    Message.error('获取项目列表失败')
  } finally {
    loading.value = false
  }
}

// 获取枚举数据
const fetchEnums = async () => {
  try {
    const response = await getProjectEnums()
    if (response.success) {
      enums.categories = response.data.categories
      enums.stages = response.data.stages

    } else {
      Message.error(response.msg || '获取枚举数据失败')
    }
  } catch (error) {
    console.error('获取枚举数据失败:', error)
  }
}

// 显示新建弹窗
const showCreateModal = () => {
  isEdit.value = false
  resetForm()
  modalVisible.value = true
}

// 显示编辑弹窗
const showEditModal = (record: Project) => {
  isEdit.value = true
  form.id = record.id
  form.name = record.name
  form.category = record.category
  form.start_date = record.start_date
  form.current_stage = record.current_stage
  form.launch_date = record.launch_date || ''
  form.product_doc_url = record.product_doc_url || ''
  form.ui_design_url = record.ui_design_url || ''
  form.participants = [...record.participants]
  form.progress_description = record.progress_description || ''
  modalVisible.value = true
}

// 重置表单
const resetForm = () => {
  form.id = null
  form.name = ''
  form.category = ''
  form.start_date = ''
  form.current_stage = '1.开发意向'
  form.launch_date = ''
  form.product_doc_url = ''
  form.ui_design_url = ''
  form.participants = []
  form.progress_description = ''
}

// 添加参与者
const addParticipant = () => {
  form.participants.push({ name: '', role: '' })
}

// 删除参与者
const removeParticipant = (index: number) => {
  form.participants.splice(index, 1)
}

// 提交表单
const handleSubmit = async () => {
  try {
    const errors = await formRef.value?.validate()
    if (errors) return

    const submitData = {
      ...form,
      participants: form.participants.filter(p => p.name && p.role)
    }

    if (isEdit.value) {
      const response = await updateProject(form.id!, submitData)
      if (response.success) {
        Message.success('更新项目成功')
        modalVisible.value = false
        fetchProjectList()
      } else {
        Message.error(response.msg || '更新项目失败')
      }
    } else {
      const response = await createProject(submitData)
      if (response.success) {
        Message.success('创建项目成功')
        modalVisible.value = false
        fetchProjectList()
      } else {
        Message.error(response.msg || '创建项目失败')
      }
    }
  } catch (error) {
    console.error('提交失败:', error)
    Message.error('操作失败')
  }
}

// 取消操作
const handleCancel = () => {
  modalVisible.value = false
  resetForm()
}

// 删除项目
const deleteProject = (record: Project) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除项目"${record.name}"吗？此操作不可恢复。`,
    onOk: async () => {
      try {
        const response = await deleteProjectApi(record.id)
        if (response.success) {
          Message.success('删除成功')
          fetchProjectList()
        } else {
          Message.error(response.msg || '删除失败')
        }
      } catch (error) {
        console.error('删除失败:', error)
        Message.error('删除失败')
      }
    }
  })
}

// 重置筛选条件
const resetFilters = () => {
  filters.keyword = ''
  filters.category = ''
  filters.current_stage = ''
  pagination.current = 1
  fetchProjectList()
}

// 分页处理
const handlePageChange = (page: number) => {
  pagination.current = page
  fetchProjectList()
}

const handlePageSizeChange = (pageSize: number) => {
  pagination.pageSize = pageSize
  pagination.current = 1
  fetchProjectList()
}

// 初始化
onMounted(async () => {
  try {
    // 先获取枚举数据，再获取项目列表
    await fetchEnums()
    await fetchProjectList()
  } catch (error) {
    console.error('初始化失败:', error)
  }
})
</script>

<style scoped>
.product-container {
  padding: 20px;
  background: #f7f8fa;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 30px;
}

.header h1 {
  font-size: 32px;
  font-weight: 600;
  color: #1d2129;
  margin-bottom: 8px;
}

.header p {
  font-size: 16px;
  color: #86909c;
}

.action-bar {
  background: white;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.participants {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  align-items: center;
}

.more-count {
  font-size: 12px;
  color: #86909c;
}

.progress-desc {
  max-width: 250px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.participants-form {
  border: 1px solid #e5e6eb;
  border-radius: 6px;
  padding: 12px;
  background: #f7f8fa;
}

.participant-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.participant-item:last-of-type {
  margin-bottom: 12px;
}
</style>
