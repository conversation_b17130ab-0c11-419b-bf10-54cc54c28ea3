<template>
  <div class="product-container">
    <!-- 操作栏 -->
    <div class="action-bar">
      <a-space>
        <a-button type="primary" @click="showCreateModal">
          <template #icon>
            <icon-plus />
          </template>
          新建项目
        </a-button>
        <a-input-search
          v-model="filters.keyword"
          placeholder="搜索项目名称或描述"
          style="width: 300px"
          @search="fetchProjectList"
          @clear="fetchProjectList"
          allow-clear
        />
        <a-select
          v-model="filters.category"
          placeholder="项目分类"
          style="width: 120px"
          allow-clear
          @change="fetchProjectList"
        >
          <a-option value="">全部分类</a-option>
          <a-option
            v-for="category in enums.categories"
            :key="category"
            :value="category"
          >
            {{ category }}
          </a-option>
        </a-select>
        <a-select
          v-model="filters.current_stage"
          placeholder="当前阶段"
          style="width: 140px"
          allow-clear
          @change="fetchProjectList"
        >
          <a-option value="">全部阶段</a-option>
          <a-option
            v-for="stage in enums.stages"
            :key="stage"
            :value="stage"
          >
            {{ stage }}
          </a-option>
        </a-select>
        <a-button @click="resetFilters">重置</a-button>
        <a-button @click="fetchProjectList">
          <template #icon>
            <icon-refresh />
          </template>
          刷新
        </a-button>
      </a-space>
    </div>

    <!-- 项目表格 -->
    <a-table
      :columns="columns"
      :data="projectData"
      :loading="loading"
      :pagination="pagination"
      @page-change="handlePageChange"
      @page-size-change="handlePageSizeChange"
      row-key="id"
    >
      <template #category="{ record }">
        <a-tag :color="record.category === '工具型' ? 'blue' : 'green'">
          {{ record.category }}
        </a-tag>
      </template>

      <template #terminal_type="{ record }">
        <a-tag :color="getTerminalTypeColor(record.terminal_type)">
          {{ record.terminal_type }}
        </a-tag>
      </template>

      <template #current_stage="{ record }">
        <a-tag :color="getStageColor(record.current_stage)">
          {{ record.current_stage }}
        </a-tag>
      </template>

      <template #product_doc="{ record }">
        <span v-if="record.product_doc && record.product_doc !== '#'">
          {{ record.product_doc }}
        </span>
        <span v-else>-</span>
      </template>

      <template #ui_design="{ record }">
        <span v-if="record.ui_design && record.ui_design !== '#'">
          {{ record.ui_design }}
        </span>
        <span v-else>-</span>
      </template>

      <template #progress_desc="{ record }">
        <div class="progress-desc">
          {{ record.progress_desc || '-' }}
        </div>
      </template>

      <template #actions="{ record }">
        <a-space size="mini">
          <a-button type="text" size="small" @click="showEditModal(record)">
            编辑
          </a-button>
          <a-button type="text" size="small" status="danger" @click="deleteProject(record)">
            删除
          </a-button>
        </a-space>
      </template>
    </a-table>

    <!-- 新建/编辑项目弹窗 -->
    <a-modal
      :visible="modalVisible"
      :title="isEdit ? '编辑项目' : '新建项目'"
      width="800px"
      @ok="handleSubmit"
      @update:visible="modalVisible = $event"
      @cancel="handleCancel"
    >
      <a-form ref="formRef" :model="form" :rules="rules" layout="vertical">
        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item label="项目编号" field="code" required>
              <a-input v-model="form.code" placeholder="请输入项目编号" />
            </a-form-item>
          </a-col>
          <a-col :span="16">
            <a-form-item label="项目名称" field="name" required>
              <a-input v-model="form.name" placeholder="请输入项目名称" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item label="项目分类" field="category" required>
              <a-select v-model="form.category" placeholder="请选择项目分类">
                <a-option
                  v-for="category in enums.categories"
                  :key="category"
                  :value="category"
                >
                  {{ category }}
                </a-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="终端类型" field="terminal_type" required>
              <a-select v-model="form.terminal_type" placeholder="请选择终端类型">
                <a-option
                  v-for="type in enums.terminal_types"
                  :key="type"
                  :value="type"
                >
                  {{ type }}
                </a-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="启动日期" field="start_date" required>
              <a-date-picker
                v-model="form.start_date"
                style="width: 100%"
                placeholder="请选择启动日期"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item label="当前阶段" field="current_stage" required>
              <a-select v-model="form.current_stage" placeholder="请选择当前阶段">
                <a-option
                  v-for="stage in enums.stages"
                  :key="stage"
                  :value="stage"
                >
                  {{ stage }}
                </a-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="产品文档" field="product_doc">
              <a-input v-model="form.product_doc" placeholder="请输入产品文档" />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="UI设计稿" field="ui_design">
              <a-input v-model="form.ui_design" placeholder="请输入UI设计稿" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-form-item label="进度描述" field="progress_desc">
          <a-textarea
            v-model="form.progress_desc"
            placeholder="请输入进度描述"
            :rows="3"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { Message, Modal } from '@arco-design/web-vue'
import { IconRefresh } from '@arco-design/web-vue/es/icon'
import {
  getProjectList,
  createProject,
  updateProject,
  deleteProject as deleteProjectApi,
  getProjectEnums,
  type Project,
  type ProjectEnums
} from '@/api/project'

// 响应式数据
const loading = ref(false)
const projectData = ref<Project[]>([])
const modalVisible = ref(false)
const isEdit = ref(false)
const formRef = ref()

// 枚举数据
const enums = reactive<ProjectEnums>({
  categories: [],
  stages: []
})

// 筛选条件
const filters = reactive({
  keyword: '',
  category: '',
  current_stage: ''
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0,
  showTotal: true,
  showPageSize: true
})

// 表单数据
const form = reactive({
  id: null as number | null,
  code: '',
  name: '',
  category: '',
  terminal_type: '',
  start_date: '',
  current_stage: '1.开发意向',
  product_doc: '',
  ui_design: '',
  progress_desc: ''
})

// 表格列配置
const columns = [
  {
    title: '编号',
    dataIndex: 'code',
    width: 80
  },
  {
    title: '项目名称',
    dataIndex: 'name',
    width: 150
  },
  {
    title: '项目分类',
    dataIndex: 'category',
    slotName: 'category',
    width: 100
  },
  {
    title: '终端类型',
    dataIndex: 'terminal_type',
    slotName: 'terminal_type',
    width: 120
  },
  {
    title: '启动日期',
    dataIndex: 'start_date',
    width: 110
  },
  {
    title: '当前阶段',
    dataIndex: 'current_stage',
    slotName: 'current_stage',
    width: 120
  },
  {
    title: '产品文档',
    dataIndex: 'product_doc',
    slotName: 'product_doc',
    width: 100
  },
  {
    title: 'UI设计稿',
    dataIndex: 'ui_design',
    slotName: 'ui_design',
    width: 100
  },
  {
    title: '进度描述',
    dataIndex: 'progress_desc',
    slotName: 'progress_description',
    width: 220
  },
  {
    title: '操作',
    slotName: 'actions',
    width: 120,
    fixed: 'right'
  }
]

// 表单验证规则
const rules = {
  name: [{ required: true, message: '请输入项目名称' }],
  category: [{ required: true, message: '请选择项目分类' }],
  start_date: [{ required: true, message: '请选择启动日期' }],
  current_stage: [{ required: true, message: '请选择当前阶段' }]
}

// 获取阶段颜色
const getStageColor = (stage: string) => {
  const colorMap: Record<string, string> = {
    '1.开发意向': 'gray',
    '2.需求收集': 'blue',
    '3.功能预评': 'cyan',
    '4.设计预评': 'purple',
    '5.正式评审': 'orange',
    '6.开发阶段': 'arcoblue',
    '7.测试阶段': 'lime',
    '8.交付完成': 'cyan',
    '9.上线维护': 'green',
    '10.已经废弃': 'red'
  }
  return colorMap[stage] || 'gray'
}

// 获取项目列表
const fetchProjectList = async () => {
  try {
    loading.value = true
    const response = await getProjectList({
      page: pagination.current,
      pageSize: pagination.pageSize,
      ...filters
    })

    console.log('项目列表API响应:', response)

    if (response.success) {
      projectData.value = response.data.list
      pagination.total = response.data.total

    } else {
      Message.error(response.msg || '获取项目列表失败')
    }
  } catch (error) {
    console.error('获取项目列表失败:', error)
    Message.error('获取项目列表失败')
  } finally {
    loading.value = false
  }
}

// 获取枚举数据
const fetchEnums = async () => {
  try {
    const response = await getProjectEnums()
    if (response.success) {
      enums.categories = response.data.categories
      enums.stages = response.data.stages

    } else {
      Message.error(response.msg || '获取枚举数据失败')
    }
  } catch (error) {
    console.error('获取枚举数据失败:', error)
  }
}

// 显示新建弹窗
const showCreateModal = () => {
  isEdit.value = false
  resetForm()
  modalVisible.value = true
}

// 显示编辑弹窗
const showEditModal = (record: Project) => {
  isEdit.value = true
  form.id = record.id
  form.code = record.code
  form.name = record.name
  form.category = record.category
  form.terminal_type = record.terminal_type
  form.start_date = record.start_date
  form.current_stage = record.current_stage
  form.product_doc = record.product_doc || ''
  form.ui_design = record.ui_design || ''
  form.progress_desc = record.progress_desc || ''
  modalVisible.value = true
}

// 重置表单
const resetForm = () => {
  form.id = null
  form.code = ''
  form.name = ''
  form.category = ''
  form.terminal_type = ''
  form.start_date = ''
  form.current_stage = '1.开发意向'
  form.product_doc = ''
  form.ui_design = ''
  form.progress_desc = ''
}

// 提交表单
const handleSubmit = async () => {
  try {
    const errors = await formRef.value?.validate()
    if (errors) return

    const submitData = {
      ...form
    }

    if (isEdit.value) {
      const response = await updateProject(form.id!, submitData)
      if (response.success) {
        Message.success('更新项目成功')
        modalVisible.value = false
        fetchProjectList()
      } else {
        Message.error(response.msg || '更新项目失败')
      }
    } else {
      const response = await createProject(submitData)
      if (response.success) {
        Message.success('创建项目成功')
        modalVisible.value = false
        fetchProjectList()
      } else {
        Message.error(response.msg || '创建项目失败')
      }
    }
  } catch (error) {
    console.error('提交失败:', error)
    Message.error('操作失败')
  }
}

// 取消操作
const handleCancel = () => {
  modalVisible.value = false
  resetForm()
}

// 删除项目
const deleteProject = (record: Project) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除项目"${record.name}"吗？此操作不可恢复。`,
    onOk: async () => {
      try {
        const response = await deleteProjectApi(record.id)
        if (response.success) {
          Message.success('删除成功')
          fetchProjectList()
        } else {
          Message.error(response.msg || '删除失败')
        }
      } catch (error) {
        console.error('删除失败:', error)
        Message.error('删除失败')
      }
    }
  })
}

// 重置筛选条件
const resetFilters = () => {
  filters.keyword = ''
  filters.category = ''
  filters.current_stage = ''
  pagination.current = 1
  fetchProjectList()
}

// 分页处理
const handlePageChange = (page: number) => {
  pagination.current = page
  fetchProjectList()
}

const handlePageSizeChange = (pageSize: number) => {
  pagination.pageSize = pageSize
  pagination.current = 1
  fetchProjectList()
}

// 初始化
onMounted(async () => {
  try {
    // 先获取枚举数据，再获取项目列表
    await fetchEnums()
    await fetchProjectList()
  } catch (error) {
    console.error('初始化失败:', error)
  }
})
</script>

<style scoped>
.product-container {
  padding: 20px;
  background: #f7f8fa;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 30px;
}

.header h1 {
  font-size: 32px;
  font-weight: 600;
  color: #1d2129;
  margin-bottom: 8px;
}

.header p {
  font-size: 16px;
  color: #86909c;
}

.action-bar {
  background: white;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.participants {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  align-items: center;
}

.more-count {
  font-size: 12px;
  color: #86909c;
}

.progress-desc {
  max-width: 250px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.participants-form {
  border: 1px solid #e5e6eb;
  border-radius: 6px;
  padding: 12px;
  background: #f7f8fa;
}

.participant-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.participant-item:last-of-type {
  margin-bottom: 12px;
}
</style>
