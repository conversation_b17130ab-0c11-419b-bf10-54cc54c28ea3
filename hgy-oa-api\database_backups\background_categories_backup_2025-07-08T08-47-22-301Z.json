{"tableName": "background_categories", "createSQL": "CREATE TABLE `background_categories` (\n  `id` int NOT NULL AUTO_INCREMENT,\n  `name` varchar(255) NOT NULL COMMENT '分类名称',\n  `icon` varchar(500) DEFAULT NULL COMMENT '分类图标URL',\n  `sort_order` int DEFAULT '0' COMMENT '排序',\n  `status` tinyint(1) DEFAULT '1' COMMENT '状态：1-启用，0-禁用',\n  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',\n  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',\n  PRIMARY KEY (`id`),\n  KEY `idx_sort_order` (`sort_order`),\n  KEY `idx_status` (`status`)\n) ENGINE=InnoDB AUTO_INCREMENT=17 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='背景分类表'", "data": [{"id": 16, "name": "测试背景", "icon": null, "sort_order": 0, "status": 1, "created_at": "2025-07-02T03:37:39.000Z", "updated_at": "2025-07-02T03:38:10.000Z"}], "backupTime": "2025-07-08T08:47:22.412Z", "recordCount": 1}