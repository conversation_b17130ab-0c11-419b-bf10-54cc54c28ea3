import { query } from '../src/config/database.js';

// 简单的拼音转换映射（常用汉字）
const pinyinMap = {
  '杨': 'yang', '同': 'tong', '杰': 'jie',
  '邓': 'deng', '静': 'jing', '雅': 'ya',
  '刘': 'liu', '洪': 'hong', '廷': 'ting',
  '炜': 'wei', '祺': 'qi',
  '程': 'cheng', '梅': 'mei', '薇': 'wei',
  '袁': 'yuan', '思': 'si', '维': 'wei',
  '樊': 'fan', '志': 'zhi', '亮': 'liang',
  '胡': 'hu', '柯': 'ke', '鑫': 'xin',
  '游': 'you', '佳': 'jia', '豪': 'hao',
  '高': 'gao', '鹏': 'peng', '智': 'zhi',
  '烈': 'lie', '娟': 'juan',
  '吴': 'wu', '晨': 'chen',
  '张': 'zhang', '少': 'shao', '成': 'cheng',
  '梦': 'meng', '萱': 'xuan',
  '咏': 'yong', '琪': 'qi',
  '黄': 'huang', '安': 'an',
  '何': 'he', '希': 'xi',
  '徐': 'xu', '乐': 'le', '莹': 'ying',
  '洁': 'jie', '斌': 'bin',
  '孙': 'sun', '陈': 'chen', '樟': 'zhang',
  '文': 'wen',
  '余': 'yu', '湘': 'xiang', '晴': 'qing',
  '蔡': 'cai', '恒': 'heng', '林': 'lin',
  '王': 'wang', '叶': 'ye', '红': 'hong',
  '西': 'xi', '柚': 'you', '子': 'zi',
  '书': 'shu', '玉': 'yu',
  '小': 'xiao', '强': 'qiang',
  '赵': 'zhao', '楚': 'chu',
  '谢': 'xie', '远': 'yuan',
  '雅': 'ya', '婕': 'jie',
  '滕': 'teng', '淑': 'shu', '慧': 'hui',
  '未': 'wei', '江': 'jiang', '华': 'hua',
  '笑': 'xiao',
  '方': 'fang', '源': 'yuan',
  '魏': 'wei', '汉': 'han', '霞': 'xia',
  '雪': 'xue', '纯': 'chun',
  '温': 'wen', '世': 'shi', '仙': 'xian',
  '宁': 'ning',
  '阳': 'yang', '浩': 'hao',
  '启': 'qi', '扬': 'yang',
  '彭': 'peng', '录': 'lu',
  '海': 'hai', '燕': 'yan',
  '裘': 'qiu', '旦': 'dan',
  '俊': 'jun',
  '汪': 'wang',
  '娴': 'xian',
  '李': 'li', '建': 'jian', '峰': 'feng',
  '韩': 'han', '美': 'mei',
  '屈': 'qu', '帅': 'shuai',
  '辉': 'hui',
  '朱': 'zhu', '涵': 'han',
  '兰': 'lan',
  '婷': 'ting',
  '曾': 'zeng', '迎': 'ying', '春': 'chun',
  '润': 'run',
  '邵': 'shao',
  '寒': 'han',
  '琳': 'lin',
  '贺': 'he', '蓝': 'lan',
  '颜': 'yan', '旸': 'yang',
  '勇': 'yong',
  '万': 'wan', '锋': 'feng',
  '泽': 'ze',
  '阎': 'yan', '莉': 'li',
  '欣': 'xin',
  '吟': 'yin',
  '邱': 'qiu',
  '芦': 'lu', '欢': 'huan',
  '玮': 'wei',
  '龚': 'gong', '龙': 'long',
  '赞': 'zan',
  '杰': 'jie',
  '晏': 'yan', '金': 'jin', '伟': 'wei',
  '熊': 'xiong', '嘉': 'jia', '颖': 'ying',
  '果': 'guo', '师': 'shi', '兄': 'xiong',
  '田': 'tian', '殷': 'yin',
  '周': 'zhou', '姿': 'zi',
  '媛': 'yuan',
  '永': 'yong', '基': 'ji',
  '丽': 'li', '增': 'zeng', '秀': 'xiu',
  '贞': 'zhen',
  '桂': 'gui', '语': 'yu',
  '宝': 'bao', '哥': 'ge',
  '裕': 'yu', '毫': 'hao',
  '晗': 'han',
  '怡': 'yi',
  '芝': 'zhi',
  '丰': 'feng',
  '雨': 'yu',
  '浩': 'hao',
  '范': 'fan', '瑞': 'rui',
  '段': 'duan', '夏': 'xia',
  '轩': 'xuan',
  '艳': 'yan',
  '邹': 'zou', '珍': 'zhen',
  '芳': 'fang',
  '叮': 'ding', '当': 'dang',
  '贺': 'he',
  '青': 'qing',
  '肖': 'xiao',
  '晓': 'xiao', '雯': 'wen',
  '涛': 'tao',
  '劲': 'jin',
  '利': 'li',
  '城': 'cheng',
  '荆': 'jing', '璐': 'lu',
  '霈': 'pei', '蔚': 'wei',
  '彬': 'bin',
  '衫': 'shan',
  '平': 'ping',
  '易': 'yi', '宣': 'xuan',
  '宇': 'yu',
  '娜': 'na',
  '晴': 'qing',
  '历': 'li', '鹏': 'peng',
  '夏': 'xia',
  '相': 'xiang', '里': 'li',
  '辜': 'gu',
  '磊': 'lei',
  '广': 'guang',
  '萍': 'ping',
  '亚': 'ya', '男': 'nan',
  '硕': 'shuo',
  '峰': 'feng',
  '宽': 'kuan',
  '旖': 'yi',
  '冰': 'bing', '倩': 'qian',
  '晶': 'jing', '焱': 'yan',
  '康': 'kang',
  '姝': 'shu',
  '蒲': 'pu',
  '义': 'yi',
  '贾': 'jia', '贤': 'xian',
  '厉': 'li',
  '饶': 'rao', '星': 'xing',
  '月': 'yue', '蓉': 'rong',
  '柠': 'ning', '檬': 'meng', '学': 'xue', '姐': 'jie',
  '郑': 'zheng', '依': 'yi',
  '税': 'shui', '衡': 'heng',
  '瑶': 'yao',
  '继': 'ji',
  '虎': 'hu',
  '影': 'ying',
  '任': 'ren', '唤': 'huan',
  '戴': 'dai',
  '秀': 'xiu',
  '喻': 'yu',
  '曼': 'man',
  '蒲': 'pu', '飞': 'fei',
  '晔': 'ye',
  '付': 'fu',
  '杜': 'du', '玲': 'ling',
  '乐': 'le',
  '洪': 'hong',
  '小': 'xiao', '芍': 'shao',
  '彤': 'tong',
  '东': 'dong',
  '振': 'zhen',
  '旭': 'xu',
  '茅': 'mao', '悦': 'yue',
  '川': 'chuan',
  '曹': 'cao',
  '芷': 'zhi', '帆': 'fan',
  '武': 'wu',
  '家': 'jia'
};

// 将中文名转换为拼音用户名
function chineseToUsername(chineseName) {
  let username = '';
  for (let char of chineseName) {
    if (pinyinMap[char]) {
      username += pinyinMap[char];
    } else {
      // 如果没有映射，使用字符的Unicode编码
      username += char.charCodeAt(0).toString(16);
    }
  }
  return username.toLowerCase();
}

// 员工数据
const employees = [
  // 技术部员工
  { name: '杨同杰', department: '技术部' },
  { name: '邓静雅', department: '技术部' },
  { name: '刘洪廷', department: '技术部' },
  { name: '刘炜祺', department: '技术部' },
  { name: '程梅薇', department: '技术部' },
  { name: '袁思维', department: '技术部' },
  { name: '樊志亮', department: '技术部' },
  { name: '胡柯鑫', department: '技术部' },
  { name: '游佳豪', department: '技术部' },
  { name: '高鹏智', department: '技术部' },
  { name: '游烈娟', department: '技术部' },
  { name: '吴晨', department: '技术部' },
  { name: '张少成', department: '技术部' },
  { name: '胡梦萱', department: '技术部' },
  { name: '刘咏琪', department: '技术部' },
  { name: '黄安琪', department: '技术部' },
  { name: '何希思', department: '技术部' },
  { name: '徐乐莹', department: '技术部' },
  { name: '徐洁斌', department: '技术部' },
  { name: '孙陈樟', department: '技术部' },
  { name: '刘佳文', department: '技术部' },
  { name: '余湘晴', department: '技术部' },

  // 其它部门员工
  { name: '蔡恒林', department: '其它' },
  { name: '王叶红', department: '其它' },
  { name: '西柚子', department: '其它' },
  { name: '杨书玉', department: '其它' },
  { name: '张小强', department: '其它' },
  { name: '赵楚琪', department: '其它' },
  { name: '谢思远', department: '其它' },
  { name: '刘雅婕', department: '其它' },
  { name: '滕淑慧', department: '其它' },
  { name: '未江华', department: '其它' },
  { name: '游笑', department: '其它' },
  { name: '方源', department: '其它' },
  { name: '魏汉霞', department: '其它' },
  { name: '王雪纯', department: '其它' },
  { name: '温世仙', department: '其它' },
  { name: '胡宁', department: '其它' },
  { name: '阳文浩', department: '其它' },
  { name: '胡启扬', department: '其它' },
  { name: '彭录', department: '其它' },
  { name: '谢海燕', department: '其它' },
  { name: '裘旦', department: '其它' },
  { name: '胡俊', department: '其它' },
  { name: '汪佳佳', department: '其它' },
  { name: '陈娴静', department: '其它' },
  { name: '李建峰', department: '其它' },
  { name: '韩思美', department: '其它' },
  { name: '屈帅华', department: '其它' },
  { name: '李游', department: '其它' },
  { name: '王辉', department: '其它' },
  { name: '朱涵', department: '其它' },
  { name: '兰程', department: '其它' },
  { name: '高高', department: '其它' },
  { name: '张婷婷', department: '其它' },
  { name: '曾迎春', department: '其它' },
  { name: '朱润杰', department: '其它' },
  { name: '陈燕', department: '其它' },
  { name: '邵帅', department: '其它' },
  { name: '李子寒', department: '其它' },
  { name: '刘琳琳', department: '其它' },
  { name: '贺蓝', department: '其它' },
  { name: '颜子旸', department: '其它' },
  { name: '游慧娟', department: '其它' },
  { name: '胡高勇', department: '其它' },
  { name: '万锋', department: '其它' },
  { name: '李泽文', department: '其它' },
  { name: '阎静莉', department: '其它' },
  { name: '王欣', department: '其它' },
  { name: '朱思吟', department: '其它' },
  { name: '邱婷婷', department: '其它' },
  { name: '芦文欢', department: '其它' },
  { name: '梅玮', department: '其它' },
  { name: '龚龙春', department: '其它' },
  { name: '胡赞', department: '其它' },
  { name: '黄文杰', department: '其它' },
  { name: '晏金伟', department: '其它' },
  { name: '熊嘉颖', department: '其它' },
  { name: '果子师兄', department: '其它' },
  { name: '田殷鹏', department: '其它' },
  { name: '李阳', department: '其它' },
  { name: '周姿欣', department: '其它' },
  { name: '张媛', department: '其它' },
  { name: '刘伟杰', department: '其它' },
  { name: '刘永基', department: '其它' },
  { name: '王丽娟', department: '其它' },
  { name: '王增秀', department: '其它' },
  { name: '李慧', department: '其它' },
  { name: '徐文静', department: '其它' },
  { name: '万文贞', department: '其它' },
  { name: '桂子语', department: '其它' },
  { name: '宝哥', department: '其它' },
  { name: '李裕毫', department: '其它' },
  { name: '熊梦', department: '其它' },
  { name: '袁晗', department: '其它' },
  { name: '黄子怡', department: '其它' },
  { name: '李熊慧芝', department: '其它' },
  { name: '张丽丽', department: '其它' },
  { name: '杨丰', department: '其它' },
  { name: '张文慧', department: '其它' },
  { name: '黄雨萱', department: '其它' },
  { name: '汤浩', department: '其它' },
  { name: '范瑞欢', department: '其它' },
  { name: '段晓夏', department: '其它' },
  { name: '邓文轩', department: '其它' },
  { name: '程慧艳', department: '其它' },
  { name: '邹珍洁', department: '其它' },
  { name: '李艳红', department: '其它' },
  { name: '王梦芳', department: '其它' },
  { name: '叮当', department: '其它' },
  { name: '杨贺文', department: '其它' },
  { name: '汪青琳', department: '其它' },
  { name: '高肖薇', department: '其它' },
  { name: '王晓雯', department: '其它' },
  { name: '胡涛', department: '其它' },
  { name: '万劲', department: '其它' },
  { name: '邓海利', department: '其它' },
  { name: '安城', department: '其它' },
  { name: '李艳文', department: '其它' },
  { name: '荆璐雅', department: '其它' },
  { name: '徐霈蔚', department: '其它' },
  { name: '邹彬', department: '其它' },
  { name: '黄衫', department: '其它' },
  { name: '张丽平', department: '其它' },
  { name: '王易宣', department: '其它' },
  { name: '谢宇希', department: '其它' },
  { name: '刘娜', department: '其它' },
  { name: '余雅婷', department: '其它' },
  { name: '朱子晴', department: '其它' },
  { name: '历泽鹏', department: '其它' },
  { name: '肖夏婷', department: '其它' },
  { name: '相里志辉', department: '其它' },
  { name: '辜帅', department: '其它' },
  { name: '张磊', department: '其它' },
  { name: '王广', department: '其它' },
  { name: '桂雅萍', department: '其它' },
  { name: '刘丽婷', department: '其它' },
  { name: '吴亚男', department: '其它' },
  { name: '李硕', department: '其它' },
  { name: '万峰', department: '其它' },
  { name: '刘志宽', department: '其它' },
  { name: '陈旖', department: '其它' },
  { name: '陈冰倩', department: '其它' },
  { name: '江思源', department: '其它' },
  { name: '赵晶焱', department: '其它' },
  { name: '康欢', department: '其它' },
  { name: '陈姝', department: '其它' },
  { name: '蒲阳', department: '其它' },
  { name: '孙义维', department: '其它' },
  { name: '贾龙贤', department: '其它' },
  { name: '厉媛', department: '其它' },
  { name: '饶星亮', department: '其它' },
  { name: '蓝洪阳', department: '其它' },
  { name: '康月蓉', department: '其它' },
  { name: '柠檬学姐', department: '其它' },
  { name: '韩磊', department: '其它' },
  { name: '郑依豪', department: '其它' },
  { name: '税雨衡', department: '其它' },
  { name: '王晨霞', department: '其它' },
  { name: '王婷', department: '其它' },
  { name: '陈梦瑶', department: '其它' },
  { name: '姚继辉', department: '其它' },
  { name: '虎哥', department: '其它' },
  { name: '徐文影', department: '其它' },
  { name: '任金唤', department: '其它' },
  { name: '熊寒', department: '其它' },
  { name: '戴欢欣', department: '其它' },
  { name: '邓文秀', department: '其它' },
  { name: '喻丽雯', department: '其它' },
  { name: '胡曼', department: '其它' },
  { name: '杨浩', department: '其它' },
  { name: '蒲飞', department: '其它' },
  { name: '范晔', department: '其它' },
  { name: '付梦琪', department: '其它' },
  { name: '杜玲', department: '其它' },
  { name: '林文静', department: '其它' },
  { name: '杨乐', department: '其它' },
  { name: '邱磊', department: '其它' },
  { name: '小芍子', department: '其它' },
  { name: '邓浩彤', department: '其它' },
  { name: '邓思东', department: '其它' },
  { name: '李振', department: '其它' },
  { name: '李欣欣', department: '其它' },
  { name: '陈旭', department: '其它' },
  { name: '茅雨悦', department: '其它' },
  { name: '陈川', department: '其它' },
  { name: '黄月红', department: '其它' },
  { name: '曹勇', department: '其它' },
  { name: '贾金依', department: '其它' },
  { name: '王芷帆', department: '其它' },
  { name: '武志霞', department: '其它' },
  { name: '王家瑶', department: '其它' }
];

async function importEmployees() {
  try {
    console.log('开始导入员工数据...');

    // 获取部门信息
    const departments = await query('SELECT id, name FROM system_depts');
    const deptMap = {};
    departments.forEach(dept => {
      deptMap[dept.name] = dept.id;
    });

    console.log('部门映射:', deptMap);

    let successCount = 0;
    let errorCount = 0;

    for (const employee of employees) {
      try {
        const username = chineseToUsername(employee.name);
        const departmentId = deptMap[employee.department] || null;
        const defaultPassword = '123456'; // 默认密码
        
        // 检查用户名是否已存在
        const existingUsers = await query('SELECT id FROM system_users WHERE username = ?', [username]);
        if (existingUsers.length > 0) {
          console.log(`用户 ${employee.name} (${username}) 已存在，跳过`);
          continue;
        }

        // 简单密码加密
        const hashedPassword = Buffer.from(defaultPassword).toString('base64');

        // 插入用户
        await query(
          `INSERT INTO system_users (username, nickname, password_hash, department_id, is_active, created_at, updated_at)
           VALUES (?, ?, ?, ?, ?, NOW(), NOW())`,
          [username, employee.name, hashedPassword, departmentId, 1]
        );

        console.log(`✅ 成功添加用户: ${employee.name} (${username}) - ${employee.department}`);
        successCount++;

      } catch (error) {
        console.error(`❌ 添加用户 ${employee.name} 失败:`, error.message);
        errorCount++;
      }
    }

    console.log(`\n导入完成！成功: ${successCount}, 失败: ${errorCount}`);
    process.exit(0);

  } catch (error) {
    console.error('导入员工数据失败:', error);
    process.exit(1);
  }
}

importEmployees();
