import { request } from '@/utils/request'

// 素材分类相关接口类型定义
export interface MaterialCategory {
  id: number
  name: string
  parent_id?: number
  icon?: string
  sort_order: number
  description?: string
  status: number
  created_at: string
  updated_at: string
  children?: MaterialCategory[]
}

export interface MaterialCategoryTreeNode {
  key: string
  title: string
  level: number
  count: number
  icon?: string
  description?: string
  sort_order: number
  children?: MaterialCategoryTreeNode[]
}

export interface CreateMaterialCategoryParams {
  name: string
  parent_id?: number
  icon?: string
  sort_order?: number
  description?: string
}

export interface UpdateMaterialCategoryParams {
  name: string
  parent_id?: number
  icon?: string
  sort_order?: number
  description?: string
}

// 获取素材分类树形结构
export const getMaterialCategoryTree = () => {
  return request.get<MaterialCategoryTreeNode[]>('/api/material/categories')
}

// 创建素材分类
export const createMaterialCategory = (data: CreateMaterialCategoryParams) => {
  return request.post('/api/material/categories', data)
}

// 更新素材分类
export const updateMaterialCategory = (id: number, data: UpdateMaterialCategoryParams) => {
  return request.put(`/api/material/categories/${id}`, data)
}

// 删除素材分类
export const deleteMaterialCategory = (id: number) => {
  return request.delete(`/api/material/categories/${id}`)
}
