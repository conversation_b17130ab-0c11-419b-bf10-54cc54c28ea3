import mysql from 'mysql2/promise';
import dotenv from 'dotenv';

dotenv.config();

// 数据库连接配置
const dbConfig = {
  host: process.env.DB_HOST || '*************',
  user: process.env.DB_USER || 'image_design',
  password: process.env.DB_PASSWORD || 'Mr7ybjSWaxz5aYZT',
  database: process.env.DB_NAME || 'image_design',
  port: process.env.DB_PORT || 3306,
  charset: 'utf8mb4'
};

// 阶段名称映射
const stageMapping = {
  '开发意向': '1.开发意向',
  '需求收集': '2.需求收集',
  '功能预评审': '3.功能预评',
  'UI预评审': '4.设计预评',
  '正式评审': '5.正式评审',
  '开发中': '6.开发阶段',
  '测试中': '7.测试阶段',
  '已交付': '8.交付完成',
  '上线维护中': '9.上线维护',
  '废弃': '10.已经废弃',
  // 处理已有的带编号但名称不对的阶段
  '2.需求收集': '2.需求收集', // 已经正确
  '4.UI预评审': '4.设计预评',
  '6.开发中': '6.开发阶段',
  '9.上线维护中': '9.上线维护'
};

async function updateProjectStages() {
  let connection;
  
  try {
    console.log('🔗 连接数据库...');
    connection = await mysql.createConnection(dbConfig);
    
    console.log('📊 检查现有项目阶段数据...');
    const [rows] = await connection.execute('SELECT DISTINCT current_stage FROM projects');
    
    console.log('当前数据库中的阶段:', rows.map(row => row.current_stage));
    
    // 更新每个阶段
    for (const [oldStage, newStage] of Object.entries(stageMapping)) {
      console.log(`🔄 更新阶段: "${oldStage}" -> "${newStage}"`);
      
      const [result] = await connection.execute(
        'UPDATE projects SET current_stage = ? WHERE current_stage = ?',
        [newStage, oldStage]
      );
      
      if (result.affectedRows > 0) {
        console.log(`✅ 成功更新 ${result.affectedRows} 条记录`);
      } else {
        console.log(`ℹ️  没有找到阶段为 "${oldStage}" 的记录`);
      }
    }
    
    console.log('📊 检查更新后的项目阶段数据...');
    const [updatedRows] = await connection.execute('SELECT DISTINCT current_stage FROM projects');
    console.log('更新后数据库中的阶段:', updatedRows.map(row => row.current_stage));
    
    console.log('🎉 项目阶段更新完成！');
    
  } catch (error) {
    console.error('❌ 更新项目阶段失败:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 数据库连接已关闭');
    }
  }
}

// 运行更新
updateProjectStages();
