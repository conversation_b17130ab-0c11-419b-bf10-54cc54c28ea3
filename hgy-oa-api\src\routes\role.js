import express from 'express';
import { query } from '../config/database.js';

const router = express.Router();

// 获取角色列表（支持分页）
router.get('/list', async (req, res) => {
  try {
    const { pageNum = 1, pageSize = 10, keyword = '' } = req.query;
    const offset = (pageNum - 1) * pageSize;

    let sql = `
      SELECT r.id, r.name, r.display_name as description, r.created_at as createTime,
             COUNT(u.id) as userCount
      FROM system_roles r
      LEFT JOIN system_users u ON u.role = r.name
      WHERE 1=1
    `;
    const params = [];

    if (keyword) {
      sql += ` AND (r.name LIKE ? OR r.display_name LIKE ?)`;
      const searchTerm = `%${keyword}%`;
      params.push(searchTerm, searchTerm);
    }

    sql += ` GROUP BY r.id, r.name, r.display_name, r.created_at`;
    sql += ` ORDER BY r.id LIMIT ${parseInt(pageSize)} OFFSET ${offset}`;

    const roles = await query(sql, params);

    // 获取总数
    let countSql = `SELECT COUNT(DISTINCT r.id) as total FROM system_roles r WHERE 1=1`;
    const countParams = [];
    if (keyword) {
      countSql += ` AND (r.name LIKE ? OR r.display_name LIKE ?)`;
      const searchTerm = `%${keyword}%`;
      countParams.push(searchTerm, searchTerm);
    }
    const countResult = await query(countSql, countParams);
    const total = countResult[0]?.total || 0;

    // 为每个角色添加导航权限信息
    const rolesWithNavs = await Promise.all(roles.map(async (role) => {
      // 这里可以从数据库获取导航权限，暂时使用默认配置
      const allowedNavs = getDefaultNavPermissions(role.name);
      return {
        ...role,
        allowedNavs
      };
    }));

    res.success({
      list: rolesWithNavs,
      total,
      pageNum: parseInt(pageNum),
      pageSize: parseInt(pageSize)
    }, '获取角色列表成功');
  } catch (error) {
    console.error('获取角色列表失败:', error);
    res.error('获取角色列表失败', 500);
  }
});

// 获取所有角色（兼容旧接口）
router.get('/', async (req, res) => {
  try {
    const roles = await query(`
      SELECT id, name, display_name, description, created_at, updated_at
      FROM system_roles
      ORDER BY id
    `);

    res.success(roles, '获取角色列表成功');
  } catch (error) {
    console.error('获取角色列表失败:', error);
    res.error('获取角色列表失败', 500);
  }
});

// 获取角色权限
router.get('/:id/permissions', async (req, res) => {
  try {
    const { id } = req.params;
    
    const permissions = await query(`
      SELECT p.id, p.name, p.code, p.description, p.module
      FROM system_permissions p
      INNER JOIN system_role_permissions rp ON p.id = rp.permission_id
      WHERE rp.role_id = ?
      ORDER BY p.module, p.id
    `, [id]);
    
    res.success(permissions, '获取角色权限成功');
  } catch (error) {
    console.error('获取角色权限失败:', error);
    res.error('获取角色权限失败', 500);
  }
});

// 获取用户角色
router.get('/user/:userId', async (req, res) => {
  try {
    const { userId } = req.params;
    
    const userRoles = await query(`
      SELECT r.id, r.name, r.display_name, r.description
      FROM system_roles r
      INNER JOIN system_user_roles ur ON r.id = ur.role_id
      WHERE ur.user_id = ?
    `, [userId]);
    
    res.success(userRoles, '获取用户角色成功');
  } catch (error) {
    console.error('获取用户角色失败:', error);
    res.error('获取用户角色失败', 500);
  }
});

// 检查用户权限
router.get('/user/:userId/permissions', async (req, res) => {
  try {
    const { userId } = req.params;
    
    const permissions = await query(`
      SELECT DISTINCT p.code, p.name, p.module
      FROM system_permissions p
      INNER JOIN system_role_permissions rp ON p.id = rp.permission_id
      INNER JOIN system_user_roles ur ON rp.role_id = ur.role_id
      WHERE ur.user_id = ?
      ORDER BY p.module, p.code
    `, [userId]);
    
    res.success(permissions, '获取用户权限成功');
  } catch (error) {
    console.error('获取用户权限失败:', error);
    res.error('获取用户权限失败', 500);
  }
});

// 分配用户角色
router.post('/user/:userId/assign', async (req, res) => {
  try {
    const { userId } = req.params;
    const { roleIds } = req.body;
    
    if (!Array.isArray(roleIds)) {
      return res.error('角色ID必须是数组', 400);
    }
    
    // 先删除用户现有角色
    await query('DELETE FROM system_user_roles WHERE user_id = ?', [userId]);
    
    // 添加新角色
    for (const roleId of roleIds) {
      await query(
        'INSERT INTO system_user_roles (user_id, role_id) VALUES (?, ?)',
        [userId, roleId]
      );
    }
    
    res.success(null, '分配用户角色成功');
  } catch (error) {
    console.error('分配用户角色失败:', error);
    res.error('分配用户角色失败', 500);
  }
});

// 获取可用的导航菜单
router.get('/available-navs', async (req, res) => {
  try {
    const availableNavs = [
      {
        key: 'index',
        name: '图片演示站',
        path: '/index',
        description: '图片库首页，浏览和搜索图片'
      },
      {
        key: 'editor',
        name: '图片制作站',
        path: '/editor',
        description: '在线设计编辑器'
      },
      {
        key: 'design',
        name: '设计工作表',
        path: '/design',
        description: '设计团队任务管理'
      },
      {
        key: 'product',
        name: '项目开发表',
        path: '/product',
        description: '项目管理与进度跟踪'
      },
      {
        key: 'admin',
        name: '管理后台',
        path: '/admin',
        description: '系统管理和配置'
      }
    ];

    res.success(availableNavs, '获取可用导航菜单成功');
  } catch (error) {
    console.error('获取可用导航菜单失败:', error);
    res.error('获取可用导航菜单失败', 500);
  }
});

// 创建角色
router.post('/', async (req, res) => {
  try {
    const { name, description, allowedNavs = [] } = req.body;

    if (!name) {
      return res.error('角色名称不能为空', 400);
    }

    // 检查角色名是否已存在
    const existingRoles = await query('SELECT id FROM system_roles WHERE name = ?', [name]);
    if (existingRoles.length > 0) {
      return res.error('角色名称已存在', 400);
    }

    const result = await query(
      'INSERT INTO system_roles (name, display_name, description, created_at, updated_at) VALUES (?, ?, ?, NOW(), NOW())',
      [name, description, description]
    );

    // 这里可以保存导航权限到数据库，暂时跳过

    res.success({ id: result.insertId }, '创建角色成功');
  } catch (error) {
    console.error('创建角色失败:', error);
    res.error('创建角色失败', 500);
  }
});

// 更新角色
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { name, description, allowedNavs = [] } = req.body;

    if (!name) {
      return res.error('角色名称不能为空', 400);
    }

    // 检查角色是否存在
    const existingRoles = await query('SELECT id FROM system_roles WHERE id = ?', [id]);
    if (existingRoles.length === 0) {
      return res.error('角色不存在', 404);
    }

    await query(
      'UPDATE system_roles SET name = ?, display_name = ?, description = ?, updated_at = NOW() WHERE id = ?',
      [name, description, description, id]
    );

    // 这里可以更新导航权限到数据库，暂时跳过

    res.success(null, '更新角色成功');
  } catch (error) {
    console.error('更新角色失败:', error);
    res.error('更新角色失败', 500);
  }
});

// 删除角色
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // 检查角色是否存在
    const existingRoles = await query('SELECT id FROM system_roles WHERE id = ?', [id]);
    if (existingRoles.length === 0) {
      return res.error('角色不存在', 404);
    }

    // 检查是否有用户使用此角色
    const usersWithRole = await query('SELECT id FROM system_users WHERE role = (SELECT name FROM system_roles WHERE id = ?)', [id]);
    if (usersWithRole.length > 0) {
      return res.error('该角色下还有用户，无法删除', 400);
    }

    await query('DELETE FROM system_roles WHERE id = ?', [id]);

    res.success(null, '删除角色成功');
  } catch (error) {
    console.error('删除角色失败:', error);
    res.error('删除角色失败', 500);
  }
});

// 获取默认导航权限配置
function getDefaultNavPermissions(roleName) {
  const defaultPermissions = {
    'admin': [
      { key: 'index', name: '图片演示站' },
      { key: 'editor', name: '图片制作站' },
      { key: 'design', name: '设计工作表' },
      { key: 'product', name: '项目开发表' },
      { key: 'admin', name: '管理后台' }
    ],
    'manager': [
      { key: 'index', name: '图片演示站' },
      { key: 'editor', name: '图片制作站' },
      { key: 'design', name: '设计工作表' },
      { key: 'product', name: '项目开发表' }
    ],
    'designer': [
      { key: 'index', name: '图片演示站' },
      { key: 'editor', name: '图片制作站' },
      { key: 'design', name: '设计工作表' }
    ],
    'developer': [
      { key: 'index', name: '图片演示站' },
      { key: 'editor', name: '图片制作站' },
      { key: 'product', name: '项目开发表' }
    ],
    'salesman': [
      { key: 'index', name: '图片演示站' }
    ],
    'guest': [
      { key: 'index', name: '图片演示站' }
    ],
    'user': [
      { key: 'index', name: '图片演示站' },
      { key: 'editor', name: '图片制作站' }
    ]
  };

  return defaultPermissions[roleName] || defaultPermissions['guest'];
}

export default router;
