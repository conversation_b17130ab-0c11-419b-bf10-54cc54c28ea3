import sqlite3 from 'sqlite3';
import { open } from 'sqlite';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// SQLite数据库文件路径
const dbPath = path.join(__dirname, '../../data/image_design.db');

let db = null;

// 初始化数据库连接
export const initDatabase = async () => {
  try {
    db = await open({
      filename: dbPath,
      driver: sqlite3.Database
    });
    
    console.log('✅ SQLite数据库连接成功');
    
    // 创建必要的表
    await createTables();
    
    return true;
  } catch (error) {
    console.error('❌ SQLite数据库连接失败:', error.message);
    return false;
  }
};

// 创建表结构
const createTables = async () => {
  try {
    // 模板表
    await db.exec(`
      CREATE TABLE IF NOT EXISTS templates (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        cover TEXT,
        name TEXT NOT NULL,
        json TEXT,
        canvas_data TEXT,
        state INTEGER DEFAULT 1,
        category TEXT DEFAULT 'default',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // 字体表
    await db.exec(`
      CREATE TABLE IF NOT EXISTS fonts (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        code TEXT NOT NULL UNIQUE,
        preview_url TEXT,
        download_url TEXT,
        font_family TEXT,
        is_system INTEGER DEFAULT 0,
        status INTEGER DEFAULT 1,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // 文字素材表
    await db.exec(`
      CREATE TABLE IF NOT EXISTS text_materials (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        title TEXT NOT NULL,
        content TEXT NOT NULL,
        preview_url TEXT,
        category_id INTEGER,
        tags TEXT,
        status INTEGER DEFAULT 1,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // 图片素材表
    await db.exec(`
      CREATE TABLE IF NOT EXISTS image_materials (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        title TEXT NOT NULL,
        content TEXT NOT NULL,
        preview_url TEXT,
        width INTEGER,
        height INTEGER,
        category_id INTEGER,
        tags TEXT,
        status INTEGER DEFAULT 1,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // 背景图片表
    await db.exec(`
      CREATE TABLE IF NOT EXISTS background_images (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        preview_url TEXT,
        original_url TEXT,
        thumbnail_url TEXT,
        category_id INTEGER,
        tags TEXT,
        width INTEGER,
        height INTEGER,
        file_size INTEGER,
        color_palette TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    console.log('✅ SQLite表结构创建完成');
  } catch (error) {
    console.error('❌ 创建表结构失败:', error);
  }
};

// 执行查询
export const query = async (sql, params = []) => {
  try {
    if (!db) {
      await initDatabase();
    }
    
    if (sql.trim().toUpperCase().startsWith('SELECT')) {
      return await db.all(sql, params);
    } else {
      const result = await db.run(sql, params);
      return { insertId: result.lastID, affectedRows: result.changes };
    }
  } catch (error) {
    console.error('SQLite查询错误:', error);
    throw error;
  }
};

// 分页查询
export const paginate = async (sql, params = [], pageNum = 1, pageSize = 10) => {
  try {
    // 计算总数
    const countSql = `SELECT COUNT(*) as total FROM (${sql})`;
    const countResult = await query(countSql, params);
    const total = countResult[0]?.total || 0;

    // 分页查询
    const offset = (pageNum - 1) * pageSize;
    const paginatedSql = `${sql} LIMIT ${pageSize} OFFSET ${offset}`;
    const records = await query(paginatedSql, params);

    return {
      records,
      total,
      pageNum: parseInt(pageNum),
      pageSize: parseInt(pageSize),
      totalPages: Math.ceil(total / pageSize)
    };
  } catch (error) {
    console.error('SQLite分页查询错误:', error);
    throw error;
  }
};

// 测试连接
export const testConnection = async () => {
  try {
    if (!db) {
      await initDatabase();
    }
    
    await db.get('SELECT 1');
    console.log('✅ SQLite数据库连接测试成功');
    return true;
  } catch (error) {
    console.error('❌ SQLite数据库连接测试失败:', error.message);
    return false;
  }
};

// 关闭数据库连接
export const closeDatabase = async () => {
  if (db) {
    await db.close();
    db = null;
    console.log('✅ SQLite数据库连接已关闭');
  }
};

export default { query, paginate, testConnection, initDatabase, closeDatabase };
