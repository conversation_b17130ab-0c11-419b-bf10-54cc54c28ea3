import { query } from '../config/database.js';

// 简单的用户认证中间件
export const authenticateUser = async (req, res, next) => {
  try {
    // 从请求头中获取用户信息
    const username = req.headers['x-username'];
    
    if (!username) {
      // 如果没有用户信息，使用默认用户
      const defaultUsers = await query('SELECT id FROM system_users WHERE is_active = 1 ORDER BY id LIMIT 1');
      if (defaultUsers.length > 0) {
        req.user = { id: defaultUsers[0].id, username: 'anonymous' };
      } else {
        return res.error('系统中没有可用的用户', 401);
      }
    } else {
      // 根据用户名查找用户
      const users = await query('SELECT id, username, role FROM system_users WHERE username = ? AND is_active = 1', [username]);
      
      if (users.length === 0) {
        return res.error('用户不存在或已被禁用', 401);
      }
      
      req.user = users[0];
    }
    
    next();
  } catch (error) {
    console.error('用户认证失败:', error);
    res.error('用户认证失败', 500);
  }
};

// 可选的用户认证中间件（不强制要求登录）
export const optionalAuth = async (req, res, next) => {
  try {
    const username = req.headers['x-username'];
    
    if (username) {
      const users = await query('SELECT id, username, role FROM system_users WHERE username = ? AND is_active = 1', [username]);
      if (users.length > 0) {
        req.user = users[0];
      }
    }
    
    // 如果没有用户信息，设置为null，但不阻止请求
    if (!req.user) {
      req.user = null;
    }
    
    next();
  } catch (error) {
    console.error('可选认证失败:', error);
    req.user = null;
    next();
  }
};
