<template>
  <div class="department-list">
    <div class="page-header">
      <h1>部门管理</h1>
      <a-button type="primary" @click="showCreateModal">
        <template #icon>
          <icon-plus />
        </template>
        新增部门
      </a-button>
    </div>

    <!-- 搜索区域 -->
    <div class="search-section">
      <a-form :model="searchForm" layout="inline">
        <a-form-item label="关键词">
          <a-input 
            v-model="searchForm.keyword" 
            placeholder="搜索部门名称、代码或描述"
            style="width: 250px;"
            @press-enter="handleSearch"
          />
        </a-form-item>
        <a-form-item label="状态">
          <a-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            style="width: 120px;"
            allow-clear
            allow-search
            :filter-option="statusFilterOption"
          >
            <a-option :value="1">启用</a-option>
            <a-option :value="0">禁用</a-option>
          </a-select>
        </a-form-item>
        <a-form-item>
          <a-space>
            <a-button type="primary" @click="handleSearch">
              <template #icon>
                <icon-search />
              </template>
              搜索
            </a-button>
            <a-button @click="handleReset">重置</a-button>
            <a-button @click="handleRefresh">
              <template #icon>
                <icon-refresh />
              </template>
              刷新
            </a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </div>

    <!-- 批量操作 -->
    <div class="batch-actions" v-if="selectedRowKeys.length > 0">
      <a-alert type="info" show-icon>
        <template #message>
          已选择 {{ selectedRowKeys.length }} 项
          <a-button type="text" size="small" @click="selectedRowKeys = []">取消选择</a-button>
        </template>
        <template #action>
          <a-button size="small" status="danger" @click="handleBatchDelete">
            批量删除
          </a-button>
        </template>
      </a-alert>
    </div>

    <!-- 表格 -->
    <a-table
      :columns="columns"
      :data="departmentData"
      :loading="loading"
      :pagination="pagination"
      :row-selection="rowSelection"
      @page-change="handlePageChange"
      @page-size-change="handlePageSizeChange"
      row-key="id"
    >
      <template #status="{ record }">
        <a-tag :color="record.status === 1 ? 'green' : 'red'">
          {{ record.status === 1 ? '启用' : '禁用' }}
        </a-tag>
      </template>

      <template #manager="{ record }">
        <span v-if="record.manager_name">
          {{ record.manager_nickname || record.manager_name }}
        </span>
        <span v-else style="color: #999;">未设置</span>
      </template>

      <template #userCount="{ record }">
        <a-tag>{{ record.user_count || 0 }}人</a-tag>
      </template>

      <template #actions="{ record }">
        <a-space size="mini">
          <a-button type="text" size="small" @click="showEditModal(record)">
            编辑
          </a-button>
          <a-button type="text" size="small" status="danger" @click="handleDelete(record)">
            删除
          </a-button>
        </a-space>
      </template>
    </a-table>

    <!-- 新增/编辑部门弹窗 -->
    <a-modal
      v-model:visible="departmentModalVisible"
      :title="isEdit ? '编辑部门' : '新增部门'"
      width="600px"
      @ok="handleDepartmentSubmit"
      @cancel="handleDepartmentCancel"
    >
      <a-form :model="departmentForm" :rules="departmentRules" ref="departmentFormRef" layout="vertical">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="部门名称" field="name">
              <a-input v-model="departmentForm.name" placeholder="请输入部门名称" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="部门代码" field="code">
              <a-input v-model="departmentForm.code" placeholder="请输入部门代码" />
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="上级部门" field="parent_id">
              <a-select
                v-model="departmentForm.parent_id"
                placeholder="请选择上级部门"
                allow-clear
                allow-search
                :filter-option="departmentFilterOption"
              >
                <a-option v-for="dept in parentDepartmentOptions" :key="dept.id" :value="dept.id">
                  {{ dept.name }}
                </a-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="排序" field="sort_order">
              <a-input-number v-model="departmentForm.sort_order" placeholder="排序值" :min="0" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="状态" field="status">
              <a-select
                v-model="departmentForm.status"
                placeholder="请选择状态"
                allow-search
                :filter-option="statusFilterOption"
              >
                <a-option :value="1">启用</a-option>
                <a-option :value="0">禁用</a-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-form-item label="部门描述" field="description">
          <a-textarea 
            v-model="departmentForm.description" 
            placeholder="请输入部门描述"
            :rows="3"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { Message, Modal } from '@arco-design/web-vue'
import { 
  IconPlus, 
  IconRefresh, 
  IconSearch 
} from '@arco-design/web-vue/es/icon'
import {
  getDepartmentList,
  getAllDepartments,
  createDepartment,
  updateDepartment,
  deleteDepartment,
  batchDeleteDepartments,
  type Department
} from '@/api/dept'
import { createSelectFilter } from '@/utils/pinyin-filter'

// 响应式数据
const loading = ref(false)
const departmentData = ref<Department[]>([])
const selectedRowKeys = ref<number[]>([])
const allDepartments = ref<Department[]>([])

// 搜索表单
const searchForm = reactive({
  keyword: '',
  status: ''
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showTotal: true,
  showPageSize: true
})

// 表格列配置
const columns = [
  { title: '部门名称', dataIndex: 'name', width: 150 },
  { title: '部门代码', dataIndex: 'code', width: 120 },
  { title: '上级部门', dataIndex: 'parent_name', width: 150 },
  { title: '部门经理', slotName: 'manager', width: 120 },
  { title: '人员数量', slotName: 'userCount', width: 100, align: 'center' },
  { title: '排序', dataIndex: 'sort_order', width: 80, align: 'center' },
  { title: '状态', slotName: 'status', width: 100, align: 'center' },
  { title: '创建时间', dataIndex: 'created_at', width: 160, render: ({ record }: { record: Department }) => formatDateTime(record.created_at) },
  { title: '操作', slotName: 'actions', width: 120, align: 'center', fixed: 'right' }
]

// 行选择配置
const rowSelection = reactive({
  type: 'checkbox',
  selectedRowKeys: selectedRowKeys,
  onSelectionChange: (keys: number[]) => {
    selectedRowKeys.value = keys
  }
})

// 弹窗相关
const departmentModalVisible = ref(false)
const isEdit = ref(false)
const currentDepartmentId = ref<number | null>(null)

// 表单数据
const departmentForm = reactive({
  name: '',
  code: '',
  description: '',
  parent_id: undefined as number | undefined,
  sort_order: 0,
  status: 1
})

// 表单引用
const departmentFormRef = ref()

// 表单验证规则
const departmentRules = {
  name: [
    { required: true, message: '请输入部门名称' },
    { maxLength: 100, message: '部门名称最多100个字符' }
  ],
  code: [
    { required: true, message: '请输入部门代码' },
    { maxLength: 50, message: '部门代码最多50个字符' }
  ]
}

// 上级部门选项（排除当前编辑的部门）
const parentDepartmentOptions = computed(() => {
  return allDepartments.value.filter(dept => 
    dept.id !== currentDepartmentId.value
  )
})

// 工具函数
const formatDateTime = (dateStr: string) => {
  if (!dateStr) return '-'
  const date = new Date(dateStr)
  if (isNaN(date.getTime())) return '-'

  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')

  return `${year}-${month}-${day}`
}

// 获取所有部门（用于上级部门选择）
const fetchAllDepartments = async () => {
  try {
    const response = await getAllDepartments()
    if (response.success) {
      allDepartments.value = response.data
    }
  } catch (error) {
    console.error('获取部门列表失败:', error)
  }
}

// 获取部门列表
const fetchDepartmentList = async () => {
  loading.value = true
  try {
    const params = {
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
      keyword: searchForm.keyword,
      status: searchForm.status
    }
    
    const response = await getDepartmentList(params)
    if (response.success) {
      departmentData.value = response.data.records
      pagination.total = response.data.total
    }
  } catch (error) {
    console.error('获取部门列表失败:', error)
    Message.error('获取部门列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.current = 1
  fetchDepartmentList()
}

// 重置搜索
const handleReset = () => {
  searchForm.keyword = ''
  searchForm.status = ''
  pagination.current = 1
  fetchDepartmentList()
}

// 刷新
const handleRefresh = () => {
  fetchDepartmentList()
}

// 分页处理
const handlePageChange = (page: number) => {
  pagination.current = page
  fetchDepartmentList()
}

const handlePageSizeChange = (pageSize: number) => {
  pagination.pageSize = pageSize
  pagination.current = 1
  fetchDepartmentList()
}

// 显示新增弹窗
const showCreateModal = () => {
  isEdit.value = false
  currentDepartmentId.value = null
  resetDepartmentForm()
  departmentModalVisible.value = true
}

// 显示编辑弹窗
const showEditModal = (record: Department) => {
  isEdit.value = true
  currentDepartmentId.value = record.id
  departmentForm.name = record.name
  departmentForm.code = record.code
  departmentForm.description = record.description || ''
  departmentForm.parent_id = record.parent_id
  departmentForm.sort_order = record.sort_order
  departmentForm.status = record.status
  departmentModalVisible.value = true
}

// 重置部门表单
const resetDepartmentForm = () => {
  departmentForm.name = ''
  departmentForm.code = ''
  departmentForm.description = ''
  departmentForm.parent_id = undefined
  departmentForm.sort_order = 0
  departmentForm.status = 1
}

// 部门表单提交
const handleDepartmentSubmit = async () => {
  try {
    const valid = await departmentFormRef.value?.validate()
    if (!valid) return

    loading.value = true

    if (isEdit.value && currentDepartmentId.value) {
      // 编辑部门
      const response = await updateDepartment(currentDepartmentId.value, {
        name: departmentForm.name,
        code: departmentForm.code,
        description: departmentForm.description || undefined,
        parent_id: departmentForm.parent_id || undefined,
        sort_order: departmentForm.sort_order,
        status: departmentForm.status as 0 | 1
      })

      if (response.success) {
        Message.success('编辑部门成功')
        departmentModalVisible.value = false
        // 刷新数据
        await Promise.all([
          fetchAllDepartments(),
          fetchDepartmentList()
        ])
      } else {
        Message.error(response.msg || '编辑部门失败')
      }
    } else {
      // 新增部门
      const response = await createDepartment({
        name: departmentForm.name,
        code: departmentForm.code,
        description: departmentForm.description || undefined,
        parent_id: departmentForm.parent_id || undefined,
        sort_order: departmentForm.sort_order,
        status: departmentForm.status as 0 | 1
      })

      if (response.success) {
        Message.success('新增部门成功')
        departmentModalVisible.value = false
        // 刷新数据
        await Promise.all([
          fetchAllDepartments(),
          fetchDepartmentList()
        ])
      } else {
        Message.error(response.msg || '新增部门失败')
      }
    }
  } catch (error) {
    console.error('保存部门失败:', error)
    Message.error('保存部门失败，请重试')
  } finally {
    loading.value = false
  }
}

// 部门表单取消
const handleDepartmentCancel = () => {
  departmentModalVisible.value = false
}

// 删除部门
const handleDelete = (record: Department) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除部门"${record.name}"吗？此操作不可恢复。`,
    onOk: async () => {
      try {
        await deleteDepartment(record.id)
        Message.success('删除部门成功')
        fetchAllDepartments()
        fetchDepartmentList()
      } catch (error) {
        console.error('删除部门失败:', error)
      }
    }
  })
}

// 批量删除部门
const handleBatchDelete = () => {
  Modal.confirm({
    title: '确认批量删除',
    content: `确定要删除选中的 ${selectedRowKeys.value.length} 个部门吗？此操作不可恢复。`,
    onOk: async () => {
      try {
        await batchDeleteDepartments(selectedRowKeys.value)
        Message.success(`成功删除 ${selectedRowKeys.value.length} 个部门`)
        selectedRowKeys.value = []
        fetchAllDepartments()
        fetchDepartmentList()
      } catch (error) {
        console.error('批量删除部门失败:', error)
      }
    }
  })
}

// 拼音筛选函数
const departmentFilterOption = createSelectFilter('name')
const statusFilterOption = (inputValue: string, option: any) => {
  if (!inputValue) return true
  const label = option.label || option.children || ''
  const searchLower = inputValue.toLowerCase()

  // 支持中英文筛选
  if (label.includes('启用') && (searchLower.includes('q') || searchLower.includes('qi') || searchLower.includes('enable'))) {
    return true
  }
  if (label.includes('禁用') && (searchLower.includes('j') || searchLower.includes('jin') || searchLower.includes('disable'))) {
    return true
  }

  return label.toLowerCase().includes(searchLower)
}

// 页面加载时获取数据
onMounted(() => {
  fetchAllDepartments()
  fetchDepartmentList()
})
</script>

<style scoped>
.department-list {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
}

.search-section {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 6px;
  margin-bottom: 16px;
}

.batch-actions {
  margin-bottom: 16px;
}
</style>
