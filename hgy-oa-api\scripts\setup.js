import fs from 'fs-extra';
import path from 'path';
import { fileURLToPath } from 'url';
import { testConnection, query } from '../src/config/database.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 创建必要的目录
const createDirectories = async () => {
  console.log('创建必要的目录...');
  
  const dirs = [
    path.join(__dirname, '../uploads'),
    path.join(__dirname, '../uploads/images'),
    path.join(__dirname, '../uploads/videos'),
    path.join(__dirname, '../uploads/documents'),
    path.join(__dirname, '../uploads/others')
  ];

  for (const dir of dirs) {
    await fs.ensureDir(dir);
    console.log(`✅ 创建目录: ${dir}`);
  }
};

// 执行数据库表结构创建
const createTables = async () => {
  console.log('创建数据库表结构...');
  
  const schemaPath = path.join(__dirname, '../database/schema.sql');
  
  if (!fs.existsSync(schemaPath)) {
    console.error('❌ 数据库表结构文件不存在');
    return false;
  }

  try {
    const sqlContent = fs.readFileSync(schemaPath, 'utf8');
    const statements = sqlContent.split(';').filter(stmt => stmt.trim());

    for (const statement of statements) {
      if (statement.trim()) {
        await query(statement);
      }
    }

    console.log('✅ 数据库表结构创建成功');
    return true;
  } catch (error) {
    console.error('❌ 创建数据库表结构失败:', error.message);
    return false;
  }
};

// 检查环境配置
const checkEnvironment = () => {
  console.log('检查环境配置...');
  
  const envPath = path.join(__dirname, '../.env');
  
  if (!fs.existsSync(envPath)) {
    console.error('❌ .env 文件不存在，请复制 .env.example 并配置');
    return false;
  }

  const requiredVars = ['DB_HOST', 'DB_USER', 'DB_PASSWORD', 'DB_NAME'];
  
  for (const varName of requiredVars) {
    if (!process.env[varName]) {
      console.error(`❌ 环境变量 ${varName} 未配置`);
      return false;
    }
  }

  console.log('✅ 环境配置检查通过');
  return true;
};

// 插入默认数据
const insertDefaultData = async () => {
  console.log('插入默认数据...');
  
  try {
    // 插入默认字体
    const defaultFonts = [
      {
        name: 'Arial',
        code: 'arial',
        font_family: 'Arial, sans-serif',
        is_system: 1
      },
      {
        name: 'Times New Roman',
        code: 'times-new-roman',
        font_family: 'Times New Roman, serif',
        is_system: 1
      },
      {
        name: '微软雅黑',
        code: 'microsoft-yahei',
        font_family: 'Microsoft Yahei, sans-serif',
        is_system: 1
      }
    ];

    for (const font of defaultFonts) {
      const sql = `
        INSERT IGNORE INTO fonts (name, code, font_family, is_system, status, created_at, updated_at)
        VALUES (?, ?, ?, ?, 1, NOW(), NOW())
      `;
      await query(sql, [font.name, font.code, font.font_family, font.is_system]);
    }

    // 插入默认分类
    const defaultCategories = [
      { table: 'graph_categories', name: '基础图形', icon: null },
      { table: 'graph_categories', name: '装饰图形', icon: null },
      { table: 'element_categories', name: '图标', icon: null },
      { table: 'element_categories', name: '装饰元素', icon: null },
      { table: 'background_categories', name: '纯色背景', icon: null },
      { table: 'background_categories', name: '渐变背景', icon: null }
    ];

    for (const category of defaultCategories) {
      const sql = `
        INSERT IGNORE INTO ${category.table} (name, icon, sort_order, status, created_at, updated_at)
        VALUES (?, ?, 0, 1, NOW(), NOW())
      `;
      await query(sql, [category.name, category.icon]);
    }

    console.log('✅ 默认数据插入成功');
  } catch (error) {
    console.error('❌ 插入默认数据失败:', error.message);
  }
};

// 主设置函数
const setup = async () => {
  console.log('🚀 开始设置后端服务...\n');

  // 检查环境配置
  if (!checkEnvironment()) {
    process.exit(1);
  }

  // 测试数据库连接
  console.log('测试数据库连接...');
  const connected = await testConnection();
  if (!connected) {
    console.error('❌ 数据库连接失败，请检查配置');
    process.exit(1);
  }

  // 创建目录
  await createDirectories();

  // 创建数据库表
  const tablesCreated = await createTables();
  if (!tablesCreated) {
    console.error('❌ 数据库表创建失败');
    process.exit(1);
  }

  // 插入默认数据
  await insertDefaultData();

  console.log('\n🎉 后端服务设置完成！');
  console.log('\n下一步：');
  console.log('1. 运行 npm run dev 启动开发服务器');
  console.log('2. 运行 node scripts/migrate-data.js 迁移数据（可选）');
  console.log('3. 访问 http://localhost:3001/health 检查服务状态');
};

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  setup().catch(error => {
    console.error('设置过程中发生错误:', error);
    process.exit(1);
  });
}

export default setup;
