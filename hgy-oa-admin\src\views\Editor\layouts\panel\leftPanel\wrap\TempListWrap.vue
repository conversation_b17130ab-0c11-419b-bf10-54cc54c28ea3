<template>
    <div class="wrap">
        <search-header :cateList="cateList" v-model="keyword" @changeCate="changeCate" @search="onSearch"/>
        <div class="temp-wrap">
            <comp-list-wrap @fetchData="fetchData" :data="page.dataList" :config="config" :noMore="page.noMore" max-height="calc(100vh - 115px)">
                <template #item="{ item, url, index }">
                    <a-card hoverable @click="handleClick(item)" class="cursor-pointer drop-shadow" :body-style="{ padding: '0px' }">
                        <div class="">
                            <div class="tags">
                                <div class="tag">VIP</div>
<!--                                <div>ag</div>-->
                            </div>
                            <LazyImg :url="url" class="img" />
                        </div>
                                             <div class="p5px">
                                                 <span class="name truncated">{{ item.title }}</span>
                                             </div>
                    </a-card>
                </template>
            </comp-list-wrap>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue'
import { LazyImg, Waterfall } from '@/components/vue-waterfall-plugin-next'
import loading from '@/assets/images/loading.png'
import error from '@/assets/images/error.png'
import searchHeader from "@/components/editorModules/searchHeader.vue";
const config= {
    imgSelector:'cover',
    // 设置每行显示3个模板
    breakpoints: {
        2000: {
            rowPerView: 3
        },
        1200: {
            rowPerView: 3
        },
        800: {
            rowPerView: 3
        },
        500: {
            rowPerView: 2
        },
        300: {
            rowPerView: 1
        }
    },
    // 调整间距
    gutter: 8,
    // 调整宽度以适应3列布局
    width: 100
}

import {useEditor} from "@/views/Editor/app";
import {Group,Image} from "leafer-ui";
const {editor} = useEditor()
import {getDefaultName} from "@/views/Editor/utils/utils";
import CompListWrap from "@/views/Editor/layouts/panel/leftPanel/wrap/CompListWrap.vue";
import usePageMixin from "@/views/Editor/layouts/panel/leftPanel/wrap/mixins/pageMixin";
import {queryTemplateList} from "@/api/editor/materials";

const keyword = ref();
const cateList = reactive([
    {label:'全部',value:'-1'}
]);
const currentCategoryId = ref('-1');
const isLoading = ref(false); // 添加加载状态标志，防止重复请求
// 获取模板分类数据
const fetchCategories = async () => {
    try {
        // 这里应该调用模板分类API，但目前先使用模板列表API
        console.log('获取模板分类数据...')
        // TODO: 实现真正的分类API调用
        // const response = await getTemplateCategoryTree()
        // if (response.success) {
        //     const categories = response.data || []
        //     cateList.splice(1) // 保留"全部"选项，清空其他
        //     categories.forEach(cat => {
        //         cateList.push({
        //             label: cat.name,
        //             value: cat.id.toString()
        //         })
        //     })
        // }
    } catch (error) {
        console.error('获取模板分类失败:', error)
    }
}

const changeCate = (categoryId: any) => {
    console.log('切换分类:', categoryId)
    currentCategoryId.value = categoryId
    // 重置分页数据和加载状态
    page.dataList = []
    page.pageNum = 1
    page.noMore = false
    isLoading.value = false
    // 重新获取数据
    fetchData()
}

const onSearch = (value: any, ev: any) => {
    console.log('搜索:', value)
    keyword.value = value
    // 重置分页数据和加载状态
    page.dataList = []
    page.pageNum = 1
    page.noMore = false
    isLoading.value = false
    // 重新获取数据
    fetchData()
}

const { page } = usePageMixin()
page.pageSize = 20

const fetchData = () => {
    // 防止重复请求
    if (isLoading.value) {
        console.log('正在加载中，跳过重复请求')
        return
    }

    console.log('获取模板数据，页码:', page.pageNum, '分类:', currentCategoryId.value)

    isLoading.value = true

    const params: any = {
        pageNum: page.pageNum,
        pageSize: page.pageSize
    }

    // 如果有关键词，添加搜索参数
    if (keyword.value) {
        params.keyword = keyword.value
    }

    // 如果有分类筛选，添加分类参数
    if (currentCategoryId.value && currentCategoryId.value !== '-1') {
        params.categoryId = currentCategoryId.value
    }

    queryTemplateList(params).then(res => {
        console.log('模板数据响应:', res)
        if (res.success || res.data) {
            const newDataList = res.data?.records || []
            if (newDataList.length > 0) {
                page.dataList.push(...newDataList)
                page.pageNum += 1
            }
            const total = res.data?.total || 0
            if (page.dataList.length >= total) {
                page.noMore = true
            } else {
                page.noMore = false
            }
            console.log('当前模板列表:', page.dataList)
        } else {
            console.warn('获取模板数据失败:', res)
        }
    }).catch(error => {
        console.error('获取模板数据出错:', error)
    }).finally(() => {
        isLoading.value = false
    })
}
const handleClick = (item) => {
    console.log('点击模板:', item)
    if (item.json) {
        editor.importJsonToCurrentPage(item.json, true)
    } else {
        console.warn('模板数据为空')
    }
}

// 组件挂载时初始化
onMounted(() => {
    console.log('TempListWrap 组件挂载')
    fetchCategories()
    // 初始化时加载第一页数据
    fetchData()
})
</script>

<style lang="less" scoped>
.search__wrap {
    padding: 1.4rem 1rem 0.8rem 0rem;
}
.temp-wrap .tags{
  .tag{
    background-color: rgba(0,0,0,.6);
    //background-color:  rgb(var(--primary-6));
    border-radius: 8px;
    top: 6px;
    box-shadow: 0 1px 4px 0 rgba(0,0,0,.16);
    color: #fff;
    font-size: 12px;
    height: 16px;
    line-height: 16px;
    padding: 0 6px;
    position: absolute;
    left: 6px;
    z-index: 11;
  }
}
</style>
