# 后端项目部署文档

## 项目概述

图片制作站后端API服务，基于Node.js + Express框架开发，提供图片编辑、模板管理、用户认证等功能。

## 环境要求

- Node.js v22.12.0 (推荐使用nvm管理Node版本)
- pnpm >= 8.0.0
- MySQL >= 5.7
- 七牛云存储账号

## 环境配置

### 本地开发环境

```bash
# 后端接口域名
http://localhost:3001/

# 数据库配置
DB_HOST=*************
DB_USER=image_design
DB_PASSWORD=Mr7ybjSWaxz5aYZT
DB_NAME=image_design
DB_PORT=3306

# 七牛云存储配置
QINIU_ACCESS_KEY=XY7NulclFTE8ff15ORUTq-zRFhrfnSOxwnrGF6e7
QINIU_SECRET_KEY=3Pqaeoh1JmmSdhfpONLE3LcH6HJsl3Y2vl8lT-0D
QINIU_BUCKET=3d-app
QINIU_DOMAIN=https://3d-app-qiniu.dlmu.cc
```

### 测试环境

```bash
# 后端接口域名
https://image-api.dlmu.cc

# 数据库配置（同本地开发）
DB_HOST=*************
DB_USER=image_design
DB_PASSWORD=Mr7ybjSWaxz5aYZT
DB_NAME=image_design
DB_PORT=3306

# 七牛云存储配置（同本地开发）
QINIU_ACCESS_KEY=XY7NulclFTE8ff15ORUTq-zRFhrfnSOxwnrGF6e7
QINIU_SECRET_KEY=3Pqaeoh1JmmSdhfpONLE3LcH6HJsl3Y2vl8lT-0D
QINIU_BUCKET=3d-app
QINIU_DOMAIN=https://3d-app-qiniu.dlmu.cc
```

## 部署步骤

### 1. 克隆代码

```bash
git clone [repository-url]
cd hgy-oa-api
```

### 2. 安装Node.js和依赖

```bash
# 使用nvm安装指定版本的Node.js (项目根目录有.nvmrc文件)
nvm use

# 安装pnpm
npm install -g pnpm

# 安装项目依赖
pnpm install
```

### 3. 配置环境变量

#### 本地开发环境
```bash
# 使用 .env.local 文件（已创建）
pnpm dev:local
```

#### 测试环境
```bash
# 使用 .env.test 文件（已创建）
pnpm start:test
```

#### 生产环境
```bash
# 设置系统环境变量
export NODE_ENV=production
export PORT=3001
export DB_HOST=*************
export DB_USER=image_design
export DB_PASSWORD=Mr7ybjSWaxz5aYZT
export DB_NAME=image_design
export DB_PORT=3306
export QINIU_ACCESS_KEY=XY7NulclFTE8ff15ORUTq-zRFhrfnSOxwnrGF6e7
export QINIU_SECRET_KEY=3Pqaeoh1JmmSdhfpONLE3LcH6HJsl3Y2vl8lT-0D
export QINIU_BUCKET=3d-app
export QINIU_DOMAIN=https://3d-app-qiniu.dlmu.cc

# 启动服务
pnpm start
```

### 4. 数据库初始化

```bash
# 初始化数据库表结构和基础数据
pnpm setup
```

### 5. 启动服务

```bash
# 本地开发
pnpm dev:local

# 测试环境
pnpm start:test

# 生产环境
pnpm start
```

## 可用脚本

```bash
pnpm start          # 生产环境启动
pnpm dev            # 开发环境启动（默认）
pnpm dev:local      # 本地开发环境启动
pnpm start:test     # 测试环境启动
pnpm setup          # 数据库初始化
pnpm migrate        # 数据迁移
```

## 目录结构

```
hgy-oa-api/
├── src/
│   ├── app.js              # 应用入口
│   ├── config/             # 配置文件
│   │   ├── database.js     # 数据库配置
│   │   └── qiniu.js        # 七牛云配置
│   ├── middleware/         # 中间件
│   │   └── auth.js         # 认证中间件
│   └── routes/             # 路由文件
│       ├── template.js     # 模板相关API
│       ├── font.js         # 字体相关API
│       └── ...
├── scripts/                # 脚本文件
│   ├── setup.js           # 数据库初始化
│   └── ...
├── .env.local             # 本地环境配置
├── .env.test              # 测试环境配置
└── package.json
```

## API接口

### 基础信息
- 基础URL: `http://localhost:3001/api` (本地) / `https://image-api.dlmu.cc/api` (测试)
- 认证方式: Header中传递 `x-username`

### 主要接口
- `GET /api/template/user/list` - 获取用户模板列表
- `POST /api/template/save` - 保存模板
- `GET /api/font/list` - 获取字体列表
- `POST /api/auth/login` - 用户登录

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查数据库配置是否正确
   - 确认数据库服务是否启动
   - 检查网络连接

2. **七牛云上传失败**
   - 检查AccessKey和SecretKey是否正确
   - 确认bucket名称和域名配置
   - 检查网络连接

3. **端口占用**
   - 修改PORT环境变量
   - 或者停止占用端口的进程

### 日志查看

```bash
# 查看应用日志
pnpm start 2>&1 | tee app.log

# 查看错误日志
tail -f app.log | grep ERROR
```

## 监控和维护

### 健康检查
- 本地环境: `http://localhost:3001/health`
- 测试环境: `https://image-api.dlmu.cc/health`

### 性能监控
- 数据库连接池状态
- API响应时间
- 内存使用情况

### 备份策略
- 定期备份数据库
- 备份上传的文件资源
- 备份配置文件

## 安全注意事项

1. **环境变量安全**
   - 不要将包含敏感信息的.env文件提交到版本控制
   - 生产环境使用系统环境变量

2. **数据库安全**
   - 使用强密码
   - 限制数据库访问IP
   - 定期更新密码

3. **API安全**
   - 实施适当的认证机制
   - 添加请求频率限制
   - 输入验证和过滤
