import { request } from '@/utils/request'

// 素材相关接口类型定义
export interface Material {
  id: number
  title: string
  type: 'image' | 'text'
  file_path?: string
  thumbnail_path?: string
  content?: string
  width?: number
  height?: number
  file_size?: number
  format?: string
  category_id?: number
  categories?: Array<{
    id: number
    name: string
  }>
  description?: string
  status: number
  upload_user?: string
  upload_user_nickname?: string
  created_at: string
  updated_at: string
}

export interface MaterialListParams {
  page?: number
  pageSize?: number
  keyword?: string
  type?: 'image' | 'text'
  primaryCategory?: number
  secondaryCategory?: number
  status?: number
}

export interface MaterialListResponse {
  list: Material[]
  total: number
  page: number
  pageSize: number
}

export interface CreateMaterialParams {
  title: string
  type: 'image' | 'text'
  file_path?: string
  thumbnail_path?: string
  content?: string
  width?: number
  height?: number
  file_size?: number
  format?: string
  category_id?: number
  description?: string
}

export interface UpdateMaterialParams {
  title?: string
  category_id?: number
  description?: string
  status?: number
}

// 获取素材列表
export const getMaterialList = (params: MaterialListParams) => {
  return request.get<MaterialListResponse>('/api/material/list', { params })
}

// 创建素材
export const createMaterial = (data: CreateMaterialParams) => {
  return request.post('/api/material', data)
}

// 更新素材
export const updateMaterial = (id: number, data: UpdateMaterialParams) => {
  return request.put(`/api/material/${id}`, data)
}

// 删除素材
export const deleteMaterial = (id: number) => {
  return request.delete(`/api/material/${id}`)
}

// 切换素材状态
export const toggleMaterialStatus = (id: number, status: number) => {
  return request.put(`/api/material/${id}/status`, { status })
}

// 获取素材详情
export const getMaterialDetail = (id: number) => {
  return request.get<Material>(`/api/material/${id}`)
}

// 上传素材（支持文件上传）
export const uploadMaterial = (formData: FormData) => {
  return request.post('/api/material/upload', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 更新素材（支持文件替换）
export const updateMaterialWithFile = (formData: FormData) => {
  return request.post('/api/material/update-with-file', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// JSON上传方法（解决中文编码问题）
export const uploadMaterialJSON = (data: {
  title: string
  description?: string
  file: string // base64编码的文件
  filename: string
  category_id?: number
}) => {
  return request.post('/api/material/upload-json', data)
}
