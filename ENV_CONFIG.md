# 环境配置说明

## 概述

本项目支持多环境配置，包括本地开发环境和测试环境。通过环境变量文件来管理不同环境的配置参数。

## 环境配置文件

### 后端 (hgy-oa-api)

- `.env.local` - 本地开发环境配置
- `.env.test` - 测试环境配置

### 前端 (hgy-oa-admin)

- `.env.local` - 本地开发环境配置  
- `.env.test` - 测试环境配置

## 启动命令

### 后端启动

```bash
# 本地开发环境
npm run dev:local

# 测试环境
npm run start:test

# 默认启动（使用系统环境变量）
npm start
npm run dev
```

### 前端启动

```bash
# 本地开发环境
npm run dev:local

# 测试环境
npm run dev:test

# 构建测试环境
npm run build:test
```

## 环境参数说明

### 本地开发环境

- **后端接口**: http://localhost:3001/
- **前端管理**: http://localhost:5173/
- **数据库**: *************:3306/image_design
- **七牛云**: 3d-app bucket

### 测试环境

- **后端接口**: https://image-api.dlmu.cc
- **前端管理**: https://image-admin.dlmu.cc  
- **数据库**: *************:3306/image_design
- **七牛云**: 3d-app bucket

## 数据库配置

```javascript
const dbConfig = {
  host: '*************',
  user: 'image_design', 
  password: 'Mr7ybjSWaxz5aYZT',
  database: 'image_design',
  port: 3306,
  charset: 'utf8mb4',
  collation: 'utf8mb4_unicode_ci',
  connectionLimit: 10,
  queueLimit: 0,
  idleTimeout: 180000,
  enableKeepAlive: true,
  keepAliveInitialDelay: 0
};
```

## 七牛云存储配置

```javascript
const qiniuConfig = {
  accessKey: 'XY7NulclFTE8ff15ORUTq-zRFhrfnSOxwnrGF6e7',
  secretKey: '3Pqaeoh1JmmSdhfpONLE3LcH6HJsl3Y2vl8lT-0D',
  bucket: '3d-app',
  domain: 'https://3d-app-qiniu.dlmu.cc',
  zone: qiniu.zone.Zone_z0,
  pathPrefix: 'image-design/'
};
```

## 注意事项

1. **环境文件安全**: `.env.*` 文件包含敏感信息，请勿提交到版本控制系统
2. **环境切换**: 使用对应的npm脚本来启动不同环境
3. **配置同步**: 修改配置时请同时更新对应的环境文件
4. **部署配置**: 生产环境建议使用系统环境变量而非文件配置
