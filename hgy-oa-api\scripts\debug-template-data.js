import { query } from '../src/config/database.js';

const debugTemplateData = async () => {
  try {
    console.log('🔍 调试模板数据格式...\n');

    // 获取最新的用户模板
    const templates = await query(`
      SELECT 
        id, name, description, created_by,
        CASE WHEN canvas_data IS NULL THEN 'NULL' ELSE 'EXISTS' END as canvas_data_status,
        CASE WHEN json IS NULL THEN 'NULL' ELSE 'EXISTS' END as json_status,
        CASE WHEN cover IS NULL THEN 'NULL' ELSE 'EXISTS' END as cover_status,
        CASE WHEN thumbnail_url IS NULL THEN 'NULL' ELSE 'EXISTS' END as thumbnail_status,
        created_at
      FROM editor_templates 
      WHERE created_by = 1 
      ORDER BY created_at DESC 
      LIMIT 3
    `);

    if (templates.length === 0) {
      console.log('❌ 没有找到用户模板');
      return;
    }

    console.log(`📋 找到 ${templates.length} 个用户模板:`);
    
    for (const template of templates) {
      console.log(`\n📄 模板 ID: ${template.id}`);
      console.log(`   名称: ${template.name}`);
      console.log(`   描述: ${template.description}`);
      console.log(`   创建者: ${template.created_by}`);
      console.log(`   canvas_data: ${template.canvas_data_status}`);
      console.log(`   json: ${template.json_status}`);
      console.log(`   cover: ${template.cover_status}`);
      console.log(`   thumbnail_url: ${template.thumbnail_status}`);
      console.log(`   创建时间: ${template.created_at}`);

      // 获取详细的数据内容
      const detailData = await query(`
        SELECT 
          canvas_data, json, cover, thumbnail_url
        FROM editor_templates 
        WHERE id = ?
      `, [template.id]);

      if (detailData.length > 0) {
        const detail = detailData[0];
        
        // 检查 canvas_data
        if (detail.canvas_data) {
          try {
            const canvasData = typeof detail.canvas_data === 'string' 
              ? JSON.parse(detail.canvas_data) 
              : detail.canvas_data;
            
            console.log(`   📊 canvas_data 结构:`);
            console.log(`      类型: ${typeof canvasData}`);
            console.log(`      标签: ${canvasData.tag || 'N/A'}`);
            console.log(`      宽度: ${canvasData.width || 'N/A'}`);
            console.log(`      高度: ${canvasData.height || 'N/A'}`);
            console.log(`      子元素数量: ${canvasData.children ? canvasData.children.length : 'N/A'}`);
            
            if (canvasData.children && canvasData.children.length > 0) {
              console.log(`      子元素类型: ${canvasData.children.map(child => child.tag || child.type).join(', ')}`);
            }
          } catch (error) {
            console.log(`   ❌ canvas_data 解析失败: ${error.message}`);
          }
        }

        // 检查 json
        if (detail.json) {
          try {
            const jsonData = typeof detail.json === 'string' 
              ? JSON.parse(detail.json) 
              : detail.json;
            
            console.log(`   📊 json 结构:`);
            console.log(`      类型: ${typeof jsonData}`);
            if (jsonData.tag) {
              console.log(`      标签: ${jsonData.tag}`);
            }
            if (Array.isArray(jsonData)) {
              console.log(`      数组长度: ${jsonData.length}`);
            }
          } catch (error) {
            console.log(`   ❌ json 解析失败: ${error.message}`);
          }
        }

        // 检查图片URL
        if (detail.cover) {
          console.log(`   🖼️  cover URL: ${detail.cover.substring(0, 100)}...`);
        }
        if (detail.thumbnail_url) {
          console.log(`   🖼️  thumbnail URL: ${detail.thumbnail_url.substring(0, 100)}...`);
        }
      }
    }

    console.log('\n✅ 模板数据调试完成！');

  } catch (error) {
    console.error('❌ 调试过程中发生错误:', error);
  }
};

// 执行调试
debugTemplateData().then(() => {
  console.log('程序执行完成');
  process.exit(0);
}).catch(error => {
  console.error('程序执行失败:', error);
  process.exit(1);
});
