import express from 'express';
import { query } from '../config/database.js';

const router = express.Router();

// 用户登录
router.post('/login', async (req, res) => {
  try {
    const { username, password } = req.body;

    if (!username || !password) {
      return res.error('用户名和密码不能为空', 400);
    }

    // 查找用户
    const users = await query(
      'SELECT id, username, nickname, role, is_active FROM system_users WHERE username = ?',
      [username]
    );

    if (users.length === 0) {
      return res.error('用户名或密码错误', 401);
    }

    const user = users[0];

    // 检查用户状态
    if (!user.is_active) {
      return res.error('用户已被禁用', 401);
    }

    // 验证密码（这里使用简单的base64编码，实际项目应使用bcrypt）
    const hashedPassword = Buffer.from(password).toString('base64');
    const storedPassword = await query(
      'SELECT password_hash FROM system_users WHERE id = ?',
      [user.id]
    );

    if (storedPassword.length === 0 || storedPassword[0].password_hash !== hashedPassword) {
      return res.error('用户名或密码错误', 401);
    }

    // 更新最后登录时间
    await query(
      'UPDATE system_users SET last_login = NOW() WHERE id = ?',
      [user.id]
    );

    // 返回用户信息（不包含密码）
    const userInfo = {
      id: user.id,
      username: user.username,
      nickname: user.nickname,
      role: user.role,
      loginTime: new Date().toISOString()
    };

    res.success(userInfo, '登录成功');
  } catch (error) {
    console.error('登录失败:', error);
    res.error('登录失败', 500);
  }
});

// 获取当前用户信息
router.get('/me', async (req, res) => {
  try {
    const username = req.headers['x-username'];
    
    if (!username) {
      return res.error('未登录', 401);
    }

    const users = await query(
      `SELECT u.id, u.username, u.nickname, u.role, u.last_login, u.department_id,
              d.name as department_name, d.code as department_code
       FROM system_users u
       LEFT JOIN system_depts d ON u.department_id = d.id
       WHERE u.username = ? AND u.is_active = 1`,
      [username]
    );

    if (users.length === 0) {
      return res.error('用户不存在', 404);
    }

    res.success(users[0], '获取用户信息成功');
  } catch (error) {
    console.error('获取用户信息失败:', error);
    res.error('获取用户信息失败', 500);
  }
});

// 用户登出
router.post('/logout', (req, res) => {
  // 由于我们使用的是无状态认证，登出主要在前端处理
  res.success(null, '登出成功');
});

export default router;
