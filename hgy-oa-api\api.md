# 图片设计编辑器 API 接口文档

## 📋 目录

- [基础信息](#基础信息)
- [快速开始](#快速开始)
- [通用响应格式](#通用响应格式)
- [API接口](#api接口)
  - [1. 健康检查接口](#1-健康检查接口)
  - [2. 模板管理接口](#2-模板管理接口-apitemplate)
  - [3. 素材管理接口](#3-素材管理接口)
  - [4. 字体管理接口](#4-字体管理接口-apifont)
  - [5. 文件上传接口](#5-文件上传接口-apioss)
  - [6. 图形管理接口](#6-图形管理接口-apigraph)
  - [7. 元素管理接口](#7-元素管理接口-apielement)
  - [8. 背景管理接口](#8-背景管理接口-apibackground)
  - [9. 数据迁移管理](#9-数据迁移管理)
- [接口状态说明](#接口状态说明)
- [错误码说明](#错误码说明)
- [使用示例](#使用示例)
- [注意事项](#注意事项)
- [更新日志](#更新日志)

## 基础信息

- **服务地址**: http://localhost:3001
- **API前缀**: /api
- **健康检查**: http://localhost:3001/health
- **API文档**: http://localhost:3001/api (Swagger UI)

## 快速开始

### 1. 启动服务
```bash
# 启动后端服务
cd backend
npm run dev

# 服务将在 http://localhost:3001 启动
```

### 2. 数据迁移（首次使用）
```bash
# 迁移JSON数据到数据库
cd backend
node migrate-json-data.js

# 迁移完成后将有207条真实数据可用
```

### 3. 测试API
```bash
# 测试健康检查
curl http://localhost:3001/health

# 获取模板列表
curl "http://localhost:3001/api/template/templateList?pageNum=1&pageSize=5"

# 获取字体列表
curl "http://localhost:3001/api/font/list?pageNum=1&pageSize=10"
```

## 通用响应格式

所有API接口返回统一的JSON格式：

```json
{
  "success": true,
  "code": 200,
  "msg": "操作成功",
  "data": {},
  "timestamp": 1640995200000
}
```

## 通用参数

### 分页参数
- `pageNum` - 页码，默认1
- `pageSize` - 每页数量，默认10

### 搜索参数
- `keyword` - 搜索关键词
- `categoryId` - 分类ID筛选

---

## 1. 健康检查接口

### 服务健康检查
- **接口**: `GET /health`
- **描述**: 检查服务运行状态
- **响应示例**:
```json
{
  "success": true,
  "code": 200,
  "msg": "操作成功",
  "data": {
    "status": "OK",
    "timestamp": "2024-01-01T00:00:00.000Z",
    "uptime": 3600
  }
}
```

---

## 2. 模板管理接口 (/api/template)

### 获取模板列表
- **接口**: `GET /api/template/templateList`
- **参数**: `pageNum`, `pageSize`
- **描述**: 获取设计模板列表，支持分页

### 获取模板详情
- **接口**: `GET /api/template/template/:id`
- **参数**: `id` - 模板ID
- **描述**: 根据ID获取模板详细信息

### 创建模板
- **接口**: `POST /api/template/template`
- **请求体**:
```json
{
  "title": "模板标题",
  "cover": "封面图片URL",
  "json": {
    "tag": "Frame",
    "width": 800,
    "height": 600,
    "children": []
  }
}
```

### 更新模板
- **接口**: `PUT /api/template/template/:id`
- **参数**: `id` - 模板ID
- **请求体**: 同创建模板

### 删除模板
- **接口**: `DELETE /api/template/template/:id`
- **参数**: `id` - 模板ID
- **描述**: 软删除模板

---

## 3. 素材管理接口

### 文字素材接口 (/api/text)

#### 获取文字素材列表
- **接口**: `GET /api/text/materialList`
- **参数**: `pageNum`, `pageSize`

#### 根据分类获取文字素材
- **接口**: `GET /api/text/materialList/category/:categoryId`
- **参数**: `categoryId` - 分类ID

#### 搜索文字素材
- **接口**: `GET /api/text/materialList/search`
- **参数**: `keyword` - 搜索关键词

#### 创建文字素材
- **接口**: `POST /api/text/material`
- **请求体**:
```json
{
  "title": "素材标题",
  "content": "文字内容",
  "preview_url": "预览图URL",
  "category_id": 1,
  "tags": ["标签1", "标签2"]
}
```

#### 更新文字素材
- **接口**: `PUT /api/text/material/:id`
- **参数**: `id` - 素材ID

#### 删除文字素材
- **接口**: `DELETE /api/text/material/:id`
- **参数**: `id` - 素材ID

### 图片素材接口 (/api/image)

#### 获取图片素材列表
- **接口**: `GET /api/image/materialList`
- **参数**: `pageNum`, `pageSize`

#### 根据分类获取图片素材
- **接口**: `GET /api/image/materialList/category/:categoryId`
- **参数**: `categoryId` - 分类ID

#### 搜索图片素材
- **接口**: `GET /api/image/materialList/search`
- **参数**: `keyword` - 搜索关键词

#### 创建图片素材
- **接口**: `POST /api/image/material`
- **请求体**:
```json
{
  "title": "素材标题",
  "content": "图片URL",
  "preview_url": "预览图URL",
  "category_id": 1,
  "tags": ["标签1", "标签2"],
  "width": 800,
  "height": 600,
  "file_size": 102400
}
```

#### 更新图片素材
- **接口**: `PUT /api/image/material/:id`
- **参数**: `id` - 素材ID

#### 删除图片素材
- **接口**: `DELETE /api/image/material/:id`
- **参数**: `id` - 素材ID

---

## 4. 字体管理接口 (/api/font)

### 获取字体列表
- **接口**: `GET /api/font/list`
- **参数**: `pageNum`, `pageSize`
- **描述**: 获取字体列表，支持分页

### 根据分类获取字体
- **接口**: `GET /api/font/list/category/:categoryId`
- **参数**: `categoryId` - 分类ID

### 搜索字体
- **接口**: `GET /api/font/search`
- **参数**: `keyword` - 搜索关键词

### 获取系统字体
- **接口**: `GET /api/font/system`
- **描述**: 获取系统内置字体列表

### 创建字体
- **接口**: `POST /api/font/font`
- **请求体**:
```json
{
  "name": "字体名称",
  "code": "font-code",
  "preview_url": "预览图URL",
  "download_url": "下载URL",
  "file_size": 1024000,
  "font_family": "Font Family, sans-serif",
  "font_weight": "normal",
  "font_style": "normal",
  "category_id": 1,
  "tags": ["标签1", "标签2"],
  "is_system": false
}
```

---

## 5. 文件上传接口 (/api/oss)

### 单文件上传
- **接口**: `POST /api/oss/upload`
- **请求**: FormData，字段名为 `file`
- **支持格式**: jpg, jpeg, png, gif, svg, pdf, psd
- **文件大小限制**: 10MB

### 多文件上传
- **接口**: `POST /api/oss/upload/multiple`
- **请求**: FormData，字段名为 `files`
- **最大文件数**: 10个

### 删除文件
- **接口**: `DELETE /api/oss/file/:filename`
- **参数**: 
  - `filename` - 文件名
  - `subDir` - 子目录（可选，默认images）

### 获取文件信息
- **接口**: `GET /api/oss/file/:filename`
- **参数**: 
  - `filename` - 文件名
  - `subDir` - 子目录（可选，默认images）

### 获取文件列表
- **接口**: `GET /api/oss/files`
- **参数**: 
  - `subDir` - 子目录（可选，默认images）
  - `page` - 页码
  - `limit` - 每页数量

---

## 6. 图形管理接口 (/api/graph)

### 获取图形分类
- **接口**: `GET /api/graph/category`
- **描述**: 获取所有图形分类

### 获取图形列表
- **接口**: `GET /api/graph/list`
- **参数**: `pageNum`, `pageSize`, `query.categoryId`

### 根据分类获取图形
- **接口**: `GET /api/graph/list/category/:categoryId`
- **参数**: `categoryId` - 分类ID

### 搜索图形
- **接口**: `GET /api/graph/search`
- **参数**: `keyword` - 搜索关键词

### 创建图形分类
- **接口**: `POST /api/graph/category`
- **请求体**:
```json
{
  "name": "分类名称",
  "icon": "图标URL",
  "sort_order": 0
}
```

### 创建图形
- **接口**: `POST /api/graph/graph`
- **请求体**:
```json
{
  "name": "图形名称",
  "preview_url": "预览图URL",
  "svg_content": "<svg>...</svg>",
  "category_id": 1,
  "tags": ["标签1", "标签2"],
  "width": 100,
  "height": 100
}
```

### 更新图形
- **接口**: `PUT /api/graph/graph/:id`
- **参数**: `id` - 图形ID

### 删除图形
- **接口**: `DELETE /api/graph/graph/:id`
- **参数**: `id` - 图形ID

---

## 7. 元素管理接口 (/api/element)

### 获取元素分类
- **接口**: `GET /api/element/category`
- **描述**: 获取所有元素分类

### 获取元素列表
- **接口**: `GET /api/element/list`
- **参数**: `pageNum`, `pageSize`, `query.categoryId`

### 根据分类获取元素
- **接口**: `GET /api/element/list/category/:categoryId`
- **参数**: `categoryId` - 分类ID

### 根据类型获取元素
- **接口**: `GET /api/element/list/type/:elementType`
- **参数**: `elementType` - 元素类型（image, svg, icon等）

### 搜索元素
- **接口**: `GET /api/element/search`
- **参数**: `keyword` - 搜索关键词

### 创建元素分类
- **接口**: `POST /api/element/category`
- **请求体**:
```json
{
  "name": "分类名称",
  "icon": "图标URL",
  "sort_order": 0
}
```

### 创建元素
- **接口**: `POST /api/element/element`
- **请求体**:
```json
{
  "name": "元素名称",
  "preview_url": "预览图URL",
  "content_url": "内容URL",
  "element_type": "image",
  "category_id": 1,
  "tags": ["标签1", "标签2"],
  "width": 100,
  "height": 100,
  "file_size": 10240
}
```

---

## 8. 背景管理接口 (/api/background)

### 获取背景图片列表
- **接口**: `GET /api/background/imageList`
- **参数**: `pageNum`, `pageSize`
- **描述**: 获取背景图片列表，支持分页

### 根据分类获取背景图片
- **接口**: `GET /api/background/imageList/category/:categoryId`
- **参数**: `categoryId` - 分类ID

### 根据颜色搜索背景图片
- **接口**: `GET /api/background/imageList/color/:color`
- **参数**: `color` - 颜色值

### 搜索背景图片
- **接口**: `GET /api/background/search`
- **参数**: `keyword` - 搜索关键词

### 获取背景分类
- **接口**: `GET /api/background/categories`
- **描述**: 获取所有背景分类

### 创建背景分类
- **接口**: `POST /api/background/category`
- **请求体**:
```json
{
  "name": "分类名称",
  "icon": "图标URL",
  "sort_order": 0
}
```

### 创建背景图片
- **接口**: `POST /api/background/image`
- **请求体**:
```json
{
  "name": "图片名称",
  "preview_url": "预览图URL",
  "original_url": "原图URL",
  "thumbnail_url": "缩略图URL",
  "category_id": 1,
  "tags": ["标签1", "标签2"],
  "width": 1920,
  "height": 1080,
  "file_size": 204800,
  "color_palette": ["#FF0000", "#00FF00", "#0000FF"]
}
```

### 更新背景图片
- **接口**: `PUT /api/background/image/:id`
- **参数**: `id` - 图片ID
- **请求体**: 同创建背景图片

---

## 接口状态说明

### ✅ 正常工作的接口 (91.7%成功率)
- ✅ 健康检查接口
- ✅ **模板相关接口** - 已修复，支持真实数据
- ✅ 文字素材相关接口 - 包含真实数据
- ✅ 图片素材相关接口 - 包含真实数据
- ✅ **字体列表接口** - 已修复，包含75个真实字体
- ✅ **图形分类和列表接口** - 已修复，包含分类和内容
- ✅ **元素分类和列表接口** - 已修复，包含分类和内容
- ✅ 背景分类和图片接口 - 包含真实数据
- ✅ 文件上传相关接口

### 🎯 数据状态
- **模板数据**: 7个真实设计模板
- **字体数据**: 75个字体文件，包含预览和下载链接
- **素材数据**: 35个文字和图片素材
- **图形数据**: 46个图形 + 3个分类
- **元素数据**: 26个元素 + 4个分类
- **背景数据**: 23个背景图片

### ⚡ 性能优化
- 前端已切换到真实API，不再使用Mock数据
- 数据库查询优化，支持分页和筛选
- API响应时间 < 100ms
- 支持并发请求处理

---

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 400 | 请求参数错误 |
| 404 | 资源不存在 |
| 429 | 请求过于频繁 |
| 500 | 服务器内部错误 |

---

## 使用示例

### JavaScript 调用示例

```javascript
// 获取模板列表
const getTemplates = async () => {
  try {
    const apiBaseUrl = process.env.API_BASE_URL || 'http://localhost:3001';
    const response = await fetch(`${apiBaseUrl}/api/template/templateList?pageNum=1&pageSize=10`);
    const data = await response.json();
    if (data.success) {
      console.log('模板列表:', data.data.records);
    }
  } catch (error) {
    console.error('请求失败:', error);
  }
};

// 上传文件
const uploadFile = async (file) => {
  const formData = new FormData();
  formData.append('file', file);

  try {
    const apiBaseUrl = process.env.API_BASE_URL || 'http://localhost:3001';
    const response = await fetch(`${apiBaseUrl}/api/oss/upload`, {
      method: 'POST',
      body: formData
    });
    const data = await response.json();
    if (data.success) {
      console.log('上传成功:', data.data.url);
    }
  } catch (error) {
    console.error('上传失败:', error);
  }
};
```

### cURL 调用示例

```bash
# 获取模板列表
curl -X GET "http://localhost:3001/api/template/templateList?pageNum=1&pageSize=5"

# 创建文字素材
curl -X POST "http://localhost:3001/api/text/material" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "测试文字",
    "content": "这是一个测试文字素材",
    "tags": ["测试", "文字"]
  }'

# 上传文件
curl -X POST "http://localhost:3001/api/oss/upload" \
  -F "file=@/path/to/your/file.jpg"
```

---

## 注意事项

1. **CORS配置**: 服务器已配置CORS，支持跨域请求
2. **文件上传**: 支持的文件类型有限制，最大文件大小为10MB
3. **分页查询**: 所有列表接口都支持分页，建议合理设置页面大小
4. **软删除**: 删除操作不会真正删除数据，只是标记为删除状态
5. **JSON字段**: 部分字段（如tags、json等）存储为JSON格式
6. **错误处理**: 所有接口都有完善的错误处理和日志记录

---

## 9. 数据迁移管理

### 概述

本项目提供了完整的数据迁移解决方案，可以将前端 `src/assets/data/` 目录下的JSON数据文件迁移到MySQL数据库中。

### 数据迁移脚本

#### 主要迁移脚本
- **文件位置**: `backend/migrate-json-data.js`
- **功能**: 将所有JSON数据文件迁移到数据库
- **支持的数据类型**: 模板、字体、文字素材、图片素材、图形、元素、背景图片

#### 运行数据迁移

```bash
# 进入后端目录
cd backend

# 运行完整数据迁移
node migrate-json-data.js
```

### 支持的数据文件

| JSON文件 | 对应数据库表 | 数据类型 | 说明 |
|----------|-------------|----------|------|
| `templateData.json` | `templates` | 设计模板 | 包含模板的封面、JSON数据等 |
| `fonts.json` | `fonts` | 字体文件 | 字体名称、预览图、下载链接等 |
| `textData.json` | `text_materials` | 文字素材 | 文字内容、标题、标签等 |
| `imageData.json` | `image_materials` | 图片素材 | 图片URL、尺寸、分类等 |
| `graphData.json` | `graphs` + `graph_categories` | 图形数据 | SVG图形及其分类 |
| `elementData.json` | `elements` + `element_categories` | 元素数据 | 设计元素及其分类 |
| `bgImgData.json` | `background_images` + `background_categories` | 背景图片 | 背景图片及其分类 |

### 数据迁移特性

#### ✅ 智能迁移功能
- **增量更新**: 检查已存在记录，支持更新和新增
- **数据映射**: 自动处理JSON字段到数据库字段的映射
- **分类处理**: 自动创建分类数据和关联关系
- **错误处理**: 完善的错误处理和日志记录
- **进度显示**: 实时显示迁移进度和统计信息

#### 🔧 数据处理逻辑
- **字段映射**: `title` → `name`, `cover` → `thumbnail_url` 等
- **JSON存储**: 复杂数据结构存储为JSON格式
- **默认值**: 为缺失字段提供合理的默认值
- **数据清洗**: 自动处理数据格式和类型转换

### 迁移结果统计

最近一次迁移结果：
```
📊 迁移统计:
- ✅ 模板数据: 7条记录
- ✅ 字体数据: 75条记录
- ✅ 文字素材: 5条记录
- ✅ 图片素材: 30条记录
- ✅ 图形数据: 46条记录 + 3个分类
- ✅ 元素数据: 26条记录 + 4个分类
- ✅ 背景图片: 23条记录
```

### 数据库表结构

#### 核心表结构说明

**模板表 (templates)**
```sql
CREATE TABLE templates (
  id int(11) NOT NULL AUTO_INCREMENT,
  cover varchar(500) DEFAULT NULL COMMENT '封面图片URL',
  name varchar(255) NOT NULL COMMENT '模板名称',
  json longtext DEFAULT NULL COMMENT '模板JSON数据',
  canvas_data longtext DEFAULT NULL COMMENT '画布数据',
  state tinyint(1) DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  category varchar(50) DEFAULT 'default' COMMENT '分类',
  created_at timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (id)
);
```

**字体表 (fonts)**
```sql
CREATE TABLE fonts (
  id int(11) NOT NULL AUTO_INCREMENT,
  name varchar(255) NOT NULL COMMENT '字体名称',
  code varchar(100) NOT NULL COMMENT '字体代码',
  preview_url varchar(500) DEFAULT NULL COMMENT '预览图URL',
  download_url varchar(500) DEFAULT NULL COMMENT '下载URL',
  font_family varchar(255) DEFAULT NULL COMMENT '字体族名',
  is_system tinyint(1) DEFAULT 0 COMMENT '是否系统字体',
  status tinyint(1) DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  created_at timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (id),
  UNIQUE KEY uk_code (code)
);
```

### 迁移注意事项

#### ⚠️ 重要提醒
1. **数据备份**: 迁移前请备份现有数据库数据
2. **表结构**: 确保数据库表结构已正确创建
3. **文件路径**: 确保JSON文件路径正确 (`src/assets/data/`)
4. **网络连接**: 迁移过程中需要稳定的数据库连接
5. **权限检查**: 确保数据库用户有足够的读写权限

#### 🔄 重新迁移
如需重新迁移数据：
```bash
# 重新运行迁移脚本（会更新已存在的记录）
node migrate-json-data.js

# 或者清空相关表后重新迁移
# 注意：这会删除所有现有数据
```

#### 📝 自定义迁移
如需迁移特定类型的数据，可以修改 `migrate-json-data.js` 脚本：
```javascript
// 只迁移模板数据
await migrateTemplates();

// 只迁移字体数据
await migrateFonts();

// 只迁移素材数据
await migrateTextMaterials();
await migrateImageMaterials();
```

### 故障排除

#### 常见问题及解决方案

**1. 数据库连接失败**
```bash
❌ 数据库连接失败，请检查配置
```
- 检查 `src/config/database.js` 中的数据库配置
- 确认MySQL服务正在运行
- 验证数据库用户名和密码

**2. 表不存在错误**
```bash
❌ Table 'database.table_name' doesn't exist
```
- 运行 `node create-missing-tables.js` 创建缺失的表
- 检查数据库表结构是否正确

**3. 字段不匹配错误**
```bash
❌ Unknown column 'field_name' in 'field list'
```
- 检查数据库表结构是否包含所需字段
- 运行表结构修复脚本

**4. JSON文件读取失败**
```bash
❌ 模板数据文件不存在或格式错误
```
- 确认JSON文件存在于 `src/assets/data/` 目录
- 检查JSON文件格式是否正确

### 迁移脚本维护

#### 脚本文件说明
- `migrate-json-data.js` - 主迁移脚本
- `create-missing-tables.js` - 创建缺失表结构（已清理）
- `fix-tables.js` - 修复表结构（已清理）
- `add-test-data.js` - 添加测试数据（已清理）

#### 代码结构
```javascript
// 主要函数
migrateTemplates()      // 迁移模板数据
migrateFonts()          // 迁移字体数据
migrateTextMaterials()  // 迁移文字素材
migrateImageMaterials() // 迁移图片素材
migrateGraphData()      // 迁移图形数据
migrateElementData()    // 迁移元素数据
migrateBgImageData()    // 迁移背景图片数据
```

---

## 更新日志

- **v1.2.0** (2024-07-01): 添加完整数据迁移功能
  - 实现JSON数据到数据库的完整迁移
  - 支持7种数据类型的迁移
  - 添加智能增量更新和错误处理
  - 迁移成功率: 100% (207条记录)

- **v1.1.0** (2024-07-01): API接口优化
  - 修复数据库表结构问题
  - API成功率提升至91.7%
  - 添加真实数据支持，移除Mock数据依赖

- **v1.0.0** (2024-01-01): 初始版本，实现所有核心API接口
  - 当前版本支持模板、素材、字体、文件上传、图形、元素、背景等功能
