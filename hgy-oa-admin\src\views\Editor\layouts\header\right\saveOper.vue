<template>
    <a-space>
        <!-- <a-button href="https://gitee.com/sourcenet/gzm-design"
                  target="_blank"
                  type="text"
                  class="!underline underline-offset-5 p-l-5px p-r-5px">
            <ali-icon type="icon-gitee" class="mr4px text-size-18px"/>Gitee1
        </a-button>
        <a-button href="https://github.com/LvHuaiSheng/gzm-design"
                  target="_blank"
                  type="text"
                  class="!underline underline-offset-5 p-l-5px p-r-5px">
            <ali-icon type="icon-github" class="mr3px text-size-18px"/>GitHub
        </a-button>

        <a-divider direction="vertical" /> -->

        <a-button @click="preview()">
            <template #icon>
                <icon-eye />
            </template>
            预览
        </a-button>
        <a-button type="primary" @click="save()">
            <template #icon>
                <icon-save />
            </template>
            保存
        </a-button>
        <a-dropdown-button type="primary" @select="handleSelect" @click="handleDownload()">
            <icon-download class="m-r-8px"/>下载作品
            <template #icon>
                <icon-down/>
            </template>
            <template #content>
                <a-doption value="json">另存为JSON</a-doption>
            </template>
        </a-dropdown-button>
    </a-space>
    <a-image-preview
            :src="previewUrl"
            v-model:visible="visiblePreview"
    />
    <a-modal v-model:visible="exportVisible" title="下载作品" @ok="handleExport()" width="600px" :top="50" :align-center="false">
        <a-form ref="formRef" :model="exportForm" :rules="rules">
            <a-form-item field="fileType" label="导出文件类型">
                <a-radio-group v-model="exportForm.fileType" type="button" :options="exportFileTypes"></a-radio-group>
            </a-form-item>
            <a-form-item field="quality" label="图片质量"  v-if="['jpg','webp'].includes(exportForm.fileType)">
                <a-space>
                    <a-radio-group v-model="exportForm.quality" type="button" :options="scQtaRate"></a-radio-group>
                    <a-input-number v-model="exportForm.quality" mode="button" style="width: 120px" :max="1" :step="0.1" :min="0.1" placeholder="1"></a-input-number>
                </a-space>
            </a-form-item>
            <a-form-item field="scale" label="缩放比例" extra="可用于生成小尺寸的缩略图">
                <a-space>
                    <a-radio-group v-model="exportForm.scale" type="button" :options="scQtaRate"></a-radio-group>
                    <a-input-number v-model="exportForm.scale" mode="button" style="width: 120px" :max="1" :step="0.1" :min="0.1" placeholder="1"></a-input-number>
                </a-space>
            </a-form-item>
            <a-form-item field="pixelRatio" label="像素比" extra="可导出适配高清屏的2倍图、3倍图">
                <a-input-number v-model="exportForm.pixelRatio" allow-clear hide-button style="width: 200px" placeholder="默认为1倍图">
                    <template #suffix>
                        倍
                    </template>
                </a-input-number>
            </a-form-item>
            <a-form-item field="trim" label="裁剪透明像素">
                <a-switch type="round" v-model="exportForm.trim">
                    <template #checked>
                        是
                    </template>
                    <template #unchecked>
                        否
                    </template>
                </a-switch>
            </a-form-item>
        </a-form>
    </a-modal>
</template>

<script setup lang="ts">
import {useEditor} from "@/views/Editor/app";
import {downFile} from "@/utils/designUtil.js";
import {v4 as uuidv4} from "uuid";
import {Notification, Message, Modal} from "@arco-design/web-vue";
import { saveTemplate } from '@/api/template';

const {editor,keybinding} = useEditor()

const visiblePreview = ref(false)
const previewUrl = ref()
const exportFileTypes = reactive([
    {value: 'jpg', label: 'JPG'},
    {value: 'png', label: 'PNG'},
    {value: 'webp', label: 'WEBP'},
])
const scQtaRate = reactive([
    {value: 1, label: '正常'},
    {value: 0.7, label: '0.7倍'},
    {value: 0.5, label: '0.5倍'},
    {value: 0.3, label: '0.3倍'},
    {value: 0.1, label: '0.1倍'},
])
const exportVisible = ref(false)
const exportForm = ref({
    fileType:'jpg',
    quality: 1,
    scale: 1,
    pixelRatio: 1,
    trim: false,
});
const rules = {

}
const resetForm = () => {
    exportForm.value = {
        fileType:'jpg',
        quality: 1,
        scale: 1,
        pixelRatio: 1,
        trim: false,
    }
}
const preview = async () => {
    const result = await editor.contentFrame.export('png', {blob: true})
    const url = URL.createObjectURL(result.data);
    previewUrl.value = url
    visiblePreview.value = true
}
const save = () => {
    // 调用保存模板到数据库的功能
    saveTemplateToDatabase()
}

const handleDownload = () => {
    resetForm()
    exportVisible.value = true
}

const handleExport = () => {
    let fileName = uuidv4()
    editor.contentFrame.export(`${fileName}.${exportForm.value.fileType}`,exportForm.value)
}

const handleSelect = (v) => {
    let fileName = uuidv4()
    switch (v) {
        case 'png':
            editor.contentFrame.export(fileName + '.png')
            break
        case 'jpg':
            editor.contentFrame.export(fileName + '.jpg')
            break
        case 'webp':
            editor.contentFrame.export(fileName + '.webp')
            break
        case 'json':
            saveJson()
            break
        default:
            editor.contentFrame.export(fileName + '.jpg')
            break
    }
};
function saveJson() {
    const dataUrl = editor.contentFrame.toJSON();
    const fileStr = `data:text/json;charset=utf-8,${encodeURIComponent(
        JSON.stringify(dataUrl, null, '\t')
    )}`;
    downFile(fileStr, `${uuidv4()}.json`);
}

// 保存模板到数据库
const saveTemplateToDatabase = async () => {
    try {
        // 获取画布数据
        const canvasData = editor.contentFrame.toJSON()

        // 查找所有blob URL
        const blobUrls = findBlobUrls(canvasData)
        console.log('找到的blob URLs:', blobUrls)

        if (blobUrls.length === 0) {
            // 没有blob图片，直接保存
            await saveTemplateData(canvasData, [])
            return
        }

        // 有blob图片，需要先转换为文件
        const files = []
        const blobUrlMap = []

        for (let i = 0; i < blobUrls.length; i++) {
            const blobUrl = blobUrls[i]
            try {
                const response = await fetch(blobUrl)
                const blob = await response.blob()
                const file = new File([blob], `image_${i}.png`, { type: blob.type || 'image/png' })
                files.push(file)
                blobUrlMap.push(blobUrl)
            } catch (error) {
                console.error('转换blob失败:', error)
            }
        }

        // 保存模板数据
        await saveTemplateData(canvasData, files, blobUrlMap)

    } catch (error) {
        console.error('保存模板失败:', error)
        Message.error('保存模板失败: ' + error.message)
    }
}

// 查找JSON中的blob URL
const findBlobUrls = (obj: any): string[] => {
    const blobUrls: string[] = []

    const traverse = (item: any) => {
        if (typeof item === 'string' && item.startsWith('blob:')) {
            if (!blobUrls.includes(item)) {
                blobUrls.push(item)
            }
        } else if (typeof item === 'object' && item !== null) {
            if (Array.isArray(item)) {
                item.forEach(traverse)
            } else {
                Object.values(item).forEach(traverse)
            }
        }
    }

    traverse(obj)
    return blobUrls
}

// 生成cover图片
const generateCoverImage = async (): Promise<string | null> => {
    try {
        console.log('开始生成cover图片...')

        // 导出画布为PNG格式的blob
        const result = await editor.contentFrame.export('png', {
            blob: true,
            scale: 0.3, // 缩放到30%作为封面
            quality: 0.8
        })

        if (result && result.data) {
            // 将blob转换为base64
            return new Promise((resolve, reject) => {
                const reader = new FileReader()
                reader.onload = () => {
                    const base64 = reader.result as string
                    console.log('cover图片生成成功，大小:', Math.round(base64.length / 1024), 'KB')
                    resolve(base64)
                }
                reader.onerror = reject
                reader.readAsDataURL(result.data)
            })
        } else {
            console.warn('导出画布失败')
            return null
        }
    } catch (error) {
        console.error('生成cover图片失败:', error)
        return null
    }
}

// 保存模板数据
const saveTemplateData = async (canvasData: any, files: File[], blobUrls: string[] = []) => {
    const templateName = prompt('请输入模板名称:') || `模板_${new Date().getTime()}`

    try {
        const formData = new FormData()

        // 基本信息
        formData.append('name', templateName)
        formData.append('description', '用户创建的模板')
        formData.append('category', 'custom')
        formData.append('canvas_data', JSON.stringify(canvasData))
        formData.append('width', editor.contentFrame.width.toString())
        formData.append('height', editor.contentFrame.height.toString())
        formData.append('is_public', '1')

        // 生成并添加cover图片
        const coverBase64 = await generateCoverImage()
        if (coverBase64) {
            formData.append('cover', coverBase64)
            console.log('cover图片已添加到表单数据')
        }

        // 添加图片文件和对应的blob URL
        files.forEach((file, index) => {
            formData.append('images', file)
            if (blobUrls[index]) {
                formData.append(`blobUrl_${index}`, blobUrls[index])
            }
        })

        console.log('开始保存模板...')
        const response = await saveTemplate(formData)

        console.log('保存模板响应:', response)

        if (response.success) {
            Message.success('模板保存成功!')
            console.log('模板保存成功:', response.data)

            // 触发"我的"模板列表刷新
            window.dispatchEvent(new CustomEvent('template-saved', {
                detail: { templateId: response.data.id }
            }))
        } else {
            Message.error('保存失败: ' + (response.msg || '未知错误'))
        }

    } catch (error) {
        console.error('保存模板失败:', error)
        Message.error('保存模板失败: ' + error.message)
    }
}
</script>

<style scoped>

</style>
