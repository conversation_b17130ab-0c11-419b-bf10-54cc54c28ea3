import express from 'express';
import { query } from '../config/database.js';
import { authenticateUser } from '../middleware/auth.js';

const router = express.Router();

// 获取套件列表
router.get('/', authenticateUser, async (req, res) => {
  try {
    const {
      pageNum = 1,
      pageSize = 10,
      keyword = '',
      isVisible = '',
      categoryId = '',
      parentCategoryId = ''
    } = req.query;

    // 基础查询
    let sql = `
      SELECT
        s.id, s.name, s.description, s.is_visible, s.sort_order, s.category_id,
        s.created_at, s.updated_at,
        u.nickname as creator_name, u.username as creator_username,
        gsc.name as category_name,
        parent_gsc.name as parent_category_name
      FROM gallery_suites s
      LEFT JOIN users u ON s.created_by = u.id
      LEFT JOIN display_suits gsc ON s.category_id = gsc.id
      LEFT JOIN display_suits parent_gsc ON gsc.parent_id = parent_gsc.id
      WHERE s.deleted_at IS NULL
    `;

    const params = [];

    // 关键词搜索
    if (keyword) {
      sql += ` AND s.name LIKE ?`;
      params.push(`%${keyword}%`);
    }

    // 显示状态筛选
    if (isVisible !== '') {
      sql += ` AND s.is_visible = ?`;
      params.push(isVisible);
    }

    // 分类筛选
    if (categoryId) {
      sql += ` AND s.category_id = ?`;
      params.push(categoryId);
    } else if (parentCategoryId) {
      // 筛选指定一级分类下的所有套件（包括子分类）
      sql += ` AND (gsc.id = ? OR gsc.parent_id = ?)`;
      params.push(parentCategoryId, parentCategoryId);
    }

    // 排序
    sql += ` ORDER BY s.sort_order ASC, s.created_at DESC`;

    // 分页
    const limit = parseInt(pageSize);
    const offset = (parseInt(pageNum) - 1) * limit;
    sql += ` LIMIT ${limit} OFFSET ${offset}`;

    const suites = await query(sql, params);

    // 获取总数
    let countSql = `
      SELECT COUNT(*) as total
      FROM gallery_suites s
      WHERE s.deleted_at IS NULL
    `;

    const countParams = [];
    if (keyword) {
      countSql += ` AND s.name LIKE ?`;
      countParams.push(`%${keyword}%`);
    }
    if (isVisible !== '') {
      countSql += ` AND s.is_visible = ?`;
      countParams.push(isVisible);
    }

    const totalResult = await query(countSql, countParams);
    const total = totalResult[0].total;

    // 为每个套件获取关联的图片数量
    for (let suite of suites) {
      const imageCountResult = await query(`
        SELECT COUNT(*) as image_count
        FROM gallery_suite_images gsi
        JOIN display_images gi ON gsi.image_id = gi.id
        WHERE gsi.suite_id = ? AND gi.deleted_at IS NULL
      `, [suite.id]);
      
      suite.image_count = imageCountResult[0].image_count;
    }

    res.success({
      records: suites,
      total,
      pageNum: parseInt(pageNum),
      pageSize: parseInt(pageSize)
    }, '获取套件列表成功');

  } catch (error) {
    console.error('获取套件列表失败:', error);
    res.error('获取套件列表失败', 500);
  }
});

// 创建套件
router.post('/', authenticateUser, async (req, res) => {
  try {
    const { name, description, is_visible = 1, sort_order = 0 } = req.body;

    if (!name) {
      return res.error('套件名称不能为空', 400);
    }

    // 检查名称是否重复
    const existingSuite = await query(`
      SELECT id FROM gallery_suites 
      WHERE name = ? AND deleted_at IS NULL
    `, [name]);

    if (existingSuite.length > 0) {
      return res.error('套件名称已存在', 400);
    }

    // 创建套件
    const result = await query(`
      INSERT INTO gallery_suites (name, description, is_visible, sort_order, created_by)
      VALUES (?, ?, ?, ?, ?)
    `, [name, description, is_visible, sort_order, req.user.id]);

    const suiteId = result.insertId;

    // 获取创建的套件信息
    const newSuite = await query(`
      SELECT 
        s.id, s.name, s.description, s.is_visible, s.sort_order,
        s.created_at, s.updated_at,
        u.nickname as creator_name, u.username as creator_username
      FROM gallery_suites s
      LEFT JOIN users u ON s.created_by = u.id
      WHERE s.id = ?
    `, [suiteId]);

    res.success(newSuite[0], '创建套件成功');

  } catch (error) {
    console.error('创建套件失败:', error);
    res.error('创建套件失败', 500);
  }
});

// 更新套件
router.put('/:id', authenticateUser, async (req, res) => {
  try {
    const { id } = req.params;
    const { name, description, is_visible, sort_order } = req.body;

    if (!name) {
      return res.error('套件名称不能为空', 400);
    }

    // 检查套件是否存在
    const existingSuite = await query(`
      SELECT id FROM gallery_suites 
      WHERE id = ? AND deleted_at IS NULL
    `, [id]);

    if (existingSuite.length === 0) {
      return res.error('套件不存在', 404);
    }

    // 检查名称是否重复（排除当前套件）
    const duplicateSuite = await query(`
      SELECT id FROM gallery_suites 
      WHERE name = ? AND id != ? AND deleted_at IS NULL
    `, [name, id]);

    if (duplicateSuite.length > 0) {
      return res.error('套件名称已存在', 400);
    }

    // 更新套件
    await query(`
      UPDATE gallery_suites 
      SET name = ?, description = ?, is_visible = ?, sort_order = ?
      WHERE id = ?
    `, [name, description, is_visible, sort_order, id]);

    // 获取更新后的套件信息
    const updatedSuite = await query(`
      SELECT 
        s.id, s.name, s.description, s.is_visible, s.sort_order,
        s.created_at, s.updated_at,
        u.nickname as creator_name, u.username as creator_username
      FROM gallery_suites s
      LEFT JOIN users u ON s.created_by = u.id
      WHERE s.id = ?
    `, [id]);

    res.success(updatedSuite[0], '更新套件成功');

  } catch (error) {
    console.error('更新套件失败:', error);
    res.error('更新套件失败', 500);
  }
});

// 删除套件
router.delete('/:id', authenticateUser, async (req, res) => {
  try {
    const { id } = req.params;

    // 检查套件是否存在
    const existingSuite = await query(`
      SELECT id FROM gallery_suites 
      WHERE id = ? AND deleted_at IS NULL
    `, [id]);

    if (existingSuite.length === 0) {
      return res.error('套件不存在', 404);
    }

    // 软删除套件
    await query(`
      UPDATE gallery_suites 
      SET deleted_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `, [id]);

    res.success(null, '删除套件成功');

  } catch (error) {
    console.error('删除套件失败:', error);
    res.error('删除套件失败', 500);
  }
});

// 批量删除套件
router.delete('/', authenticateUser, async (req, res) => {
  try {
    const { ids } = req.body;

    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return res.error('请选择要删除的套件', 400);
    }

    // 批量软删除
    const placeholders = ids.map(() => '?').join(',');
    await query(`
      UPDATE gallery_suites 
      SET deleted_at = CURRENT_TIMESTAMP
      WHERE id IN (${placeholders})
    `, ids);

    res.success(null, `成功删除 ${ids.length} 个套件`);

  } catch (error) {
    console.error('批量删除套件失败:', error);
    res.error('批量删除套件失败', 500);
  }
});

// 切换套件显示状态
router.patch('/:id/toggle-visibility', authenticateUser, async (req, res) => {
  try {
    const { id } = req.params;

    // 检查套件是否存在
    const existingSuite = await query(`
      SELECT id, is_visible FROM gallery_suites 
      WHERE id = ? AND deleted_at IS NULL
    `, [id]);

    if (existingSuite.length === 0) {
      return res.error('套件不存在', 404);
    }

    const newVisibility = existingSuite[0].is_visible ? 0 : 1;

    // 切换显示状态
    await query(`
      UPDATE gallery_suites 
      SET is_visible = ?
      WHERE id = ?
    `, [newVisibility, id]);

    res.success({ is_visible: newVisibility }, '切换显示状态成功');

  } catch (error) {
    console.error('切换显示状态失败:', error);
    res.error('切换显示状态失败', 500);
  }
});

export default router;
