#!/usr/bin/env node

/**
 * 查找项目中硬编码的API地址
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 要搜索的目录
const searchDir = path.join(__dirname, '../src');

// 要搜索的文件扩展名
const extensions = ['.vue', '.ts', '.js', '.tsx', '.jsx'];

// 要搜索的模式
const patterns = [
  /localhost:3001/g,
  /127\.0\.0\.1:3001/g,
  /http:\/\/localhost:3001/g,
  /https:\/\/localhost:3001/g,
  /'http:\/\/localhost:3001'/g,
  /"http:\/\/localhost:3001"/g,
  /`http:\/\/localhost:3001`/g
];

// 排除的目录
const excludeDirs = ['node_modules', '.git', 'dist', 'build'];

// 结果存储
const results = [];

// 递归搜索文件
function searchFiles(dir) {
  const files = fs.readdirSync(dir);
  
  for (const file of files) {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      // 跳过排除的目录
      if (!excludeDirs.includes(file)) {
        searchFiles(filePath);
      }
    } else if (stat.isFile()) {
      // 检查文件扩展名
      const ext = path.extname(file);
      if (extensions.includes(ext)) {
        searchInFile(filePath);
      }
    }
  }
}

// 在文件中搜索模式
function searchInFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const lines = content.split('\n');
    
    lines.forEach((line, lineNumber) => {
      patterns.forEach((pattern, patternIndex) => {
        const matches = line.match(pattern);
        if (matches) {
          matches.forEach(match => {
            results.push({
              file: path.relative(searchDir, filePath),
              line: lineNumber + 1,
              match: match,
              context: line.trim(),
              pattern: pattern.toString()
            });
          });
        }
      });
    });
  } catch (error) {
    console.error(`读取文件失败: ${filePath}`, error.message);
  }
}

// 执行搜索
console.log('🔍 开始搜索硬编码的API地址...\n');
console.log(`搜索目录: ${searchDir}`);
console.log(`文件类型: ${extensions.join(', ')}`);
console.log(`搜索模式: ${patterns.map(p => p.toString()).join(', ')}\n`);

searchFiles(searchDir);

// 输出结果
if (results.length === 0) {
  console.log('✅ 没有找到硬编码的API地址！');
} else {
  console.log(`❌ 找到 ${results.length} 个硬编码的API地址:\n`);
  
  // 按文件分组
  const groupedResults = {};
  results.forEach(result => {
    if (!groupedResults[result.file]) {
      groupedResults[result.file] = [];
    }
    groupedResults[result.file].push(result);
  });
  
  // 输出结果
  Object.keys(groupedResults).forEach(file => {
    console.log(`📁 ${file}:`);
    groupedResults[file].forEach(result => {
      console.log(`   第${result.line}行: ${result.match}`);
      console.log(`   上下文: ${result.context}`);
      console.log('');
    });
  });
  
  // 生成修复建议
  console.log('\n🔧 修复建议:');
  console.log('1. 将硬编码的API地址替换为统一的API配置');
  console.log('2. 使用 import { getApiBaseUrl, getEndpointUrl } from "@/config/api"');
  console.log('3. 示例修复:');
  console.log('   修复前: fetch("http://localhost:3001/api/users")');
  console.log('   修复后: const { getEndpointUrl } = await import("@/config/api"); fetch(getEndpointUrl("USER_LIST"))');
}

console.log('\n搜索完成！');
