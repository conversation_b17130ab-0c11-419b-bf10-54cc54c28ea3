import express from 'express';
import multer from 'multer';
import path from 'path';
import fs from 'fs-extra';
import { v4 as uuidv4 } from 'uuid';
import { fileURLToPath } from 'url';

const router = express.Router();

// 获取当前文件的目录路径
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 确保上传目录存在
const uploadDir = path.join(__dirname, '../../uploads');
fs.ensureDirSync(uploadDir);

// 配置multer存储
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    // 根据文件类型创建不同的子目录
    let subDir = 'others';
    if (file.mimetype.startsWith('image/')) {
      subDir = 'images';
    } else if (file.mimetype.startsWith('video/')) {
      subDir = 'videos';
    } else if (file.mimetype === 'application/pdf') {
      subDir = 'documents';
    }
    
    const targetDir = path.join(uploadDir, subDir);
    fs.ensureDirSync(targetDir);
    cb(null, targetDir);
  },
  filename: (req, file, cb) => {
    // 生成唯一文件名
    const ext = path.extname(file.originalname);
    const filename = `${uuidv4()}${ext}`;
    cb(null, filename);
  }
});

// 文件过滤器
const fileFilter = (req, file, cb) => {
  const allowedTypes = process.env.ALLOWED_FILE_TYPES?.split(',') || [
    'jpg', 'jpeg', 'png', 'gif', 'svg', 'pdf', 'psd'
  ];
  
  const ext = path.extname(file.originalname).toLowerCase().slice(1);
  
  if (allowedTypes.includes(ext)) {
    cb(null, true);
  } else {
    cb(new Error(`不支持的文件类型: ${ext}`), false);
  }
};

// 配置multer
const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: parseInt(process.env.MAX_FILE_SIZE) || 10 * 1024 * 1024, // 默认10MB
    files: 10 // 最多10个文件
  }
});

// 单文件上传
router.post('/upload', upload.single('file'), async (req, res) => {
  try {
    if (!req.file) {
      return res.error('没有上传文件', 400);
    }
    
    const file = req.file;
    const baseUrl = `${req.protocol}://${req.get('host')}`;
    const relativePath = path.relative(path.join(__dirname, '../../'), file.path);
    const url = `${baseUrl}/${relativePath.replace(/\\/g, '/')}`;
    
    const fileInfo = {
      url,
      filename: file.filename,
      originalname: file.originalname,
      mimetype: file.mimetype,
      size: file.size,
      path: relativePath
    };
    
    res.success(fileInfo, '文件上传成功');
  } catch (error) {
    console.error('文件上传失败:', error);
    res.error('文件上传失败');
  }
});

// 多文件上传
router.post('/upload/multiple', upload.array('files', 10), async (req, res) => {
  try {
    if (!req.files || req.files.length === 0) {
      return res.error('没有上传文件', 400);
    }
    
    const baseUrl = `${req.protocol}://${req.get('host')}`;
    const files = req.files.map(file => {
      const relativePath = path.relative(path.join(__dirname, '../../'), file.path);
      const url = `${baseUrl}/${relativePath.replace(/\\/g, '/')}`;
      
      return {
        url,
        filename: file.filename,
        originalname: file.originalname,
        mimetype: file.mimetype,
        size: file.size,
        path: relativePath
      };
    });
    
    res.success({ files, count: files.length }, '文件上传成功');
  } catch (error) {
    console.error('多文件上传失败:', error);
    res.error('多文件上传失败');
  }
});

// 删除文件
router.delete('/file/:filename', async (req, res) => {
  try {
    const { filename } = req.params;
    const { subDir = 'images' } = req.query;
    
    const filePath = path.join(uploadDir, subDir, filename);
    
    if (!fs.existsSync(filePath)) {
      return res.error('文件不存在', 404);
    }
    
    await fs.remove(filePath);
    res.success(null, '文件删除成功');
  } catch (error) {
    console.error('删除文件失败:', error);
    res.error('删除文件失败');
  }
});

// 获取文件信息
router.get('/file/:filename', async (req, res) => {
  try {
    const { filename } = req.params;
    const { subDir = 'images' } = req.query;
    
    const filePath = path.join(uploadDir, subDir, filename);
    
    if (!fs.existsSync(filePath)) {
      return res.error('文件不存在', 404);
    }
    
    const stats = await fs.stat(filePath);
    const baseUrl = `${req.protocol}://${req.get('host')}`;
    const relativePath = path.relative(path.join(__dirname, '../../'), filePath);
    const url = `${baseUrl}/${relativePath.replace(/\\/g, '/')}`;
    
    const fileInfo = {
      filename,
      url,
      size: stats.size,
      created: stats.birthtime,
      modified: stats.mtime
    };
    
    res.success(fileInfo);
  } catch (error) {
    console.error('获取文件信息失败:', error);
    res.error('获取文件信息失败');
  }
});

// 获取上传目录文件列表
router.get('/files', async (req, res) => {
  try {
    const { subDir = 'images', page = 1, limit = 20 } = req.query;
    const targetDir = path.join(uploadDir, subDir);
    
    if (!fs.existsSync(targetDir)) {
      return res.success({ files: [], total: 0, page: parseInt(page), limit: parseInt(limit) });
    }
    
    const files = await fs.readdir(targetDir);
    const fileInfos = [];
    
    for (const filename of files) {
      const filePath = path.join(targetDir, filename);
      const stats = await fs.stat(filePath);
      
      if (stats.isFile()) {
        const baseUrl = `${req.protocol}://${req.get('host')}`;
        const relativePath = path.relative(path.join(__dirname, '../../'), filePath);
        const url = `${baseUrl}/${relativePath.replace(/\\/g, '/')}`;
        
        fileInfos.push({
          filename,
          url,
          size: stats.size,
          created: stats.birthtime,
          modified: stats.mtime
        });
      }
    }
    
    // 分页
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + parseInt(limit);
    const paginatedFiles = fileInfos.slice(startIndex, endIndex);
    
    res.success({
      files: paginatedFiles,
      total: fileInfos.length,
      page: parseInt(page),
      limit: parseInt(limit),
      totalPages: Math.ceil(fileInfos.length / limit)
    });
  } catch (error) {
    console.error('获取文件列表失败:', error);
    res.error('获取文件列表失败');
  }
});

export default router;
