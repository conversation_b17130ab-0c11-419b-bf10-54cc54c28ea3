import express from 'express';
import { query } from '../config/database.js';
import { authenticateUser } from '../middleware/auth.js';

const router = express.Router();

// 获取套件分类树形结构
router.get('/tree', async (req, res) => {
  try {
    // 获取所有分类
    const categories = await query(`
      SELECT 
        id, name, description, parent_id, sort_order, is_visible
      FROM display_suits
      WHERE deleted_at IS NULL AND is_visible = 1
      ORDER BY sort_order ASC, created_at ASC
    `);

    // 构建树形结构
    const categoryTree = buildCategoryTree(categories);
    
    res.success(categoryTree, '获取套件分类树成功');

  } catch (error) {
    console.error('获取套件分类树失败:', error);
    res.error('获取套件分类树失败', 500);
  }
});

// 获取套件分类列表（管理用）
router.get('/', authenticateUser, async (req, res) => {
  try {
    const {
      pageNum = 1,
      pageSize = 20,
      keyword = '',
      parentId = ''
    } = req.query;

    // 基础查询
    let sql = `
      SELECT 
        c.id, c.name, c.description, c.parent_id, c.sort_order, c.is_visible,
        c.created_at, c.updated_at,
        p.name as parent_name,
        u.nickname as creator_name
      FROM display_suits c
      LEFT JOIN display_suits p ON c.parent_id = p.id
      LEFT JOIN system_users u ON c.created_by = u.id
      WHERE c.deleted_at IS NULL
    `;

    const params = [];

    // 关键词搜索
    if (keyword) {
      sql += ` AND c.name LIKE ?`;
      params.push(`%${keyword}%`);
    }

    // 父分类筛选
    if (parentId !== '') {
      if (parentId === '0') {
        sql += ` AND c.parent_id IS NULL`;
      } else {
        sql += ` AND c.parent_id = ?`;
        params.push(parentId);
      }
    }

    // 排序
    sql += ` ORDER BY COALESCE(c.parent_id, c.id), c.sort_order ASC, c.created_at ASC`;

    // 分页
    const limit = parseInt(pageSize);
    const offset = (parseInt(pageNum) - 1) * limit;
    sql += ` LIMIT ${limit} OFFSET ${offset}`;

    const categories = await query(sql, params);

    // 获取总数
    let countSql = `
      SELECT COUNT(*) as total
      FROM display_suits c
      WHERE c.deleted_at IS NULL
    `;

    const countParams = [];
    if (keyword) {
      countSql += ` AND c.name LIKE ?`;
      countParams.push(`%${keyword}%`);
    }
    if (parentId !== '') {
      if (parentId === '0') {
        countSql += ` AND c.parent_id IS NULL`;
      } else {
        countSql += ` AND c.parent_id = ?`;
        countParams.push(parentId);
      }
    }

    const totalResult = await query(countSql, countParams);
    const total = totalResult[0].total;

    res.success({
      records: categories,
      total,
      pageNum: parseInt(pageNum),
      pageSize: parseInt(pageSize)
    }, '获取套件分类列表成功');

  } catch (error) {
    console.error('获取套件分类列表失败:', error);
    res.error('获取套件分类列表失败', 500);
  }
});

// 创建套件分类
router.post('/', authenticateUser, async (req, res) => {
  try {
    const { name, description, parent_id, sort_order = 0, is_visible = 1 } = req.body;

    if (!name) {
      return res.error('分类名称不能为空', 400);
    }

    // 检查同级分类名称是否重复
    const existingCategory = await query(`
      SELECT id FROM display_suits 
      WHERE name = ? AND parent_id ${parent_id ? '= ?' : 'IS NULL'} AND deleted_at IS NULL
    `, parent_id ? [name, parent_id] : [name]);

    if (existingCategory.length > 0) {
      return res.error('同级分类中已存在相同名称', 400);
    }

    // 如果有父分类，验证父分类是否存在
    if (parent_id) {
      const parentCategory = await query(
        'SELECT id FROM display_suits WHERE id = ? AND deleted_at IS NULL',
        [parent_id]
      );
      if (parentCategory.length === 0) {
        return res.error('父分类不存在', 400);
      }
    }

    const result = await query(`
      INSERT INTO display_suits (name, description, parent_id, sort_order, is_visible, created_by)
      VALUES (?, ?, ?, ?, ?, ?)
    `, [name, description, parent_id || null, sort_order, is_visible, req.user.id]);

    res.success({ id: result.insertId }, '创建套件分类成功');

  } catch (error) {
    console.error('创建套件分类失败:', error);
    res.error('创建套件分类失败', 500);
  }
});

// 更新套件分类
router.put('/:id', authenticateUser, async (req, res) => {
  try {
    const { id } = req.params;
    const { name, description, parent_id, sort_order, is_visible } = req.body;

    // 检查分类是否存在
    const existingCategory = await query(
      'SELECT id FROM display_suits WHERE id = ? AND deleted_at IS NULL',
      [id]
    );

    if (existingCategory.length === 0) {
      return res.error('套件分类不存在', 404);
    }

    // 检查同级分类名称是否重复（排除自己）
    if (name) {
      const duplicateCategory = await query(`
        SELECT id FROM display_suits 
        WHERE name = ? AND parent_id ${parent_id ? '= ?' : 'IS NULL'} AND id != ? AND deleted_at IS NULL
      `, parent_id ? [name, parent_id, id] : [name, id]);

      if (duplicateCategory.length > 0) {
        return res.error('同级分类中已存在相同名称', 400);
      }
    }

    // 构建更新字段
    const updateFields = [];
    const updateValues = [];

    if (name !== undefined) {
      updateFields.push('name = ?');
      updateValues.push(name);
    }
    if (description !== undefined) {
      updateFields.push('description = ?');
      updateValues.push(description);
    }
    if (parent_id !== undefined) {
      updateFields.push('parent_id = ?');
      updateValues.push(parent_id || null);
    }
    if (sort_order !== undefined) {
      updateFields.push('sort_order = ?');
      updateValues.push(sort_order);
    }
    if (is_visible !== undefined) {
      updateFields.push('is_visible = ?');
      updateValues.push(is_visible);
    }

    if (updateFields.length === 0) {
      return res.error('没有要更新的字段', 400);
    }

    updateFields.push('updated_at = NOW()');
    updateValues.push(id);

    await query(
      `UPDATE display_suits SET ${updateFields.join(', ')} WHERE id = ?`,
      updateValues
    );

    res.success(null, '更新套件分类成功');

  } catch (error) {
    console.error('更新套件分类失败:', error);
    res.error('更新套件分类失败', 500);
  }
});

// 删除套件分类
router.delete('/:id', authenticateUser, async (req, res) => {
  try {
    const { id } = req.params;

    // 检查分类是否存在
    const existingCategory = await query(
      'SELECT id FROM display_suits WHERE id = ? AND deleted_at IS NULL',
      [id]
    );

    if (existingCategory.length === 0) {
      return res.error('套件分类不存在', 404);
    }

    // 检查是否有子分类
    const childCategories = await query(
      'SELECT id FROM display_suits WHERE parent_id = ? AND deleted_at IS NULL',
      [id]
    );

    if (childCategories.length > 0) {
      return res.error('该分类下还有子分类，无法删除', 400);
    }

    // 检查是否有关联的套件
    const relatedSuites = await query(
      'SELECT id FROM gallery_suites WHERE category_id = ? AND deleted_at IS NULL',
      [id]
    );

    if (relatedSuites.length > 0) {
      return res.error('该分类下还有套件，无法删除', 400);
    }

    // 软删除
    await query(
      'UPDATE display_suits SET deleted_at = NOW() WHERE id = ?',
      [id]
    );

    res.success(null, '删除套件分类成功');

  } catch (error) {
    console.error('删除套件分类失败:', error);
    res.error('删除套件分类失败', 500);
  }
});

// 构建分类树形结构的辅助函数
function buildCategoryTree(categories) {
  const categoryMap = new Map();
  const rootCategories = [];

  // 创建分类映射
  categories.forEach(category => {
    categoryMap.set(category.id, { ...category, children: [] });
  });

  // 构建树形结构
  categories.forEach(category => {
    if (category.parent_id) {
      const parent = categoryMap.get(category.parent_id);
      if (parent) {
        parent.children.push(categoryMap.get(category.id));
      }
    } else {
      rootCategories.push(categoryMap.get(category.id));
    }
  });

  return rootCategories;
}

export default router;
