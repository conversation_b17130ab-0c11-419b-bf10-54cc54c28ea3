/** 常量 */

// 最近使用颜色最大个数
export const TD_COLOR_USED_COLORS_MAX_SIZE = 100; // 每行10个

// 颜色模式options配置
export const COLOR_MODES = {
  monochrome: '单色',
  'linear-gradient': '线性渐变',
  'radial-gradient': '径向渐变',
};

// 默认颜色
export const DEFAULT_COLOR = '#001F97';

// 默认渐变色
export const DEFAULT_LINEAR_GRADIENT = 'linear-gradient(90deg, rgba(241,29,0,1) 0%, rgba(73,106,220,1) 100%);';
export const DEFAULT_RADIAL_GRADIENT = 'radial-gradient(90deg, rgba(241,29,0,1) 0%, rgba(73,106,220,1) 100%);';

// 默认系统色彩
export const DEFAULT_SYSTEM_SWATCH_COLORS = [
  '#ECF2FE',
  '#D4E3FC',
  '#BBD3FB',
  '#96BBF8',
  '#699EF5',
  '#4787F0',
  '#266FE8',
  '#0052D9',
  '#0034B5',
  '#001F97',
  '#FDECEE',
  '#F9D7D9',
  '#F8B9BE',
  '#F78D94',
  '#F36D78',
  '#E34D59',
  '#C9353F',
  '#B11F26',
  '#951114',
  '#680506',
  '#FEF3E6',
  '#F9E0C7',
  '#F7C797',
  '#F2995F',
  '#ED7B2F',
  '#D35A21',
  '#BA431B',
  '#9E3610',
  '#842B0B',
  '#5A1907',
  '#E8F8F2',
  '#BCEBDC',
  '#85DBBE',
  '#48C79C',
  '#00A870',
  '#078D5C',
  '#067945',
  '#056334',
  '#044F2A',
  '#033017',
];

// saturation-panel default rect
export const SATURATION_PANEL_DEFAULT_WIDTH = 230;
export const SATURATION_PANEL_DEFAULT_HEIGHT = 168;
export const SLIDER_DEFAULT_WIDTH = 186;
export const GRADIENT_SLIDER_DEFAULT_WIDTH = 176;
