<template>
  <div class="admin-layout">
    <!-- 侧边栏 -->
    <a-layout-sider
      :width="240"
      :collapsed="collapsed"
      :trigger="null"
      collapsible
      class="admin-sidebar"
    >
      <!-- <div class="sidebar-header">
        <h2 v-if="!collapsed">管理中心</h2>
        <h2 v-else>管理</h2>
      </div> -->

      <a-menu
        v-model:selectedKeys="selectedKeys"
        v-model:openKeys="openKeys"
        mode="inline"
        :inline-collapsed="collapsed"
        theme="light"
        class="admin-menu"
      >
        <!-- 系统管理 -->
        <a-sub-menu key="system" title="系统管理">
          <template #icon>
            <icon-settings />
          </template>
          <a-menu-item key="dashboard" @click="handleMenuClick('dashboard')">
            <template #icon>
              <icon-dashboard />
            </template>
            数据统计
          </a-menu-item>
          <a-menu-item key="user-list" @click="handleMenuClick('user-list')">
            <template #icon>
              <icon-user-group />
            </template>
            用户管理
          </a-menu-item>
          <a-menu-item key="department-list" @click="handleMenuClick('department-list')">
            <template #icon>
              <icon-apps />
            </template>
            部门管理
          </a-menu-item>
          <a-menu-item key="role-manage" @click="handleMenuClick('role-manage')">
            <template #icon>
              <icon-safe />
            </template>
            角色管理
          </a-menu-item>
          <a-menu-item key="logs" @click="handleMenuClick('logs')">
            <template #icon>
              <icon-file />
            </template>
            日志管理
          </a-menu-item>
        </a-sub-menu>

        <!-- 图片管理 -->
        <a-sub-menu key="gallery" title="图片演示站">
          <template #icon>
            <icon-image />
          </template>
          <a-menu-item key="gallery-category" @click="handleMenuClick('gallery-category')">
            <template #icon>
              <icon-folder />
            </template>
            图片分类
          </a-menu-item>
          <a-menu-item key="gallery-list" @click="handleMenuClick('gallery-list')">
            <template #icon>
              <icon-image />
            </template>
            图片列表
          </a-menu-item>
          <a-menu-item key="document-list" @click="handleMenuClick('document-list')">
            <template #icon>
              <icon-file />
            </template>
            文案管理
          </a-menu-item>
          <a-menu-item key="suite-management" @click="handleMenuClick('suite-management')">
            <template #icon>
              <icon-apps />
            </template>
            套件管理
          </a-menu-item>
        </a-sub-menu>

        <!-- 素材管理 -->
        <a-sub-menu key="material" title="图片制作站">
          <template #icon>
            <icon-folder />
          </template>
          <a-menu-item key="material-category" @click="handleMenuClick('material-category')">
            <template #icon>
              <icon-folder />
            </template>
            素材分类
          </a-menu-item>
          <a-menu-item key="material-list" @click="handleMenuClick('material-list')">
            <template #icon>
              <icon-image />
            </template>
            素材列表
          </a-menu-item>
          <a-menu-item key="template-category" @click="handleMenuClick('template-category')">
            <template #icon>
              <icon-folder />
            </template>
            模板分类
          </a-menu-item>
          <a-menu-item key="template-list" @click="handleMenuClick('template-list')">
            <template #icon>
              <icon-file />
            </template>
            模板列表
          </a-menu-item>
        </a-sub-menu>

        <!-- 设计任务 -->
        <a-sub-menu key="design" title="设计工作表">
          <template #icon>
            <icon-desktop />
          </template>
          <a-menu-item key="design-statistics" @click="handleMenuClick('design-statistics')">
            <template #icon>
              <icon-bar-chart />
            </template>
            工作统计
          </a-menu-item>
        </a-sub-menu>
      </a-menu>
    </a-layout-sider>

    <!-- 主内容区域 -->
    <div class="admin-main">
      <!-- 顶部导航栏 -->
      <div class="admin-header">
        <a-button
          type="text"
          @click="collapsed = !collapsed"
          class="collapse-btn"
        >
          <icon-menu-fold v-if="!collapsed" />
          <icon-menu-unfold v-else />
        </a-button>

        <div class="header-right">
          <a-breadcrumb class="breadcrumb">
            <a-breadcrumb-item>{{ currentBreadcrumb.parent }}</a-breadcrumb-item>
            <a-breadcrumb-item>{{ currentBreadcrumb.current }}</a-breadcrumb-item>
          </a-breadcrumb>

          <a-button type="text" @click="goHome">
            <template #icon>
              <icon-home />
            </template>
            返回首页
          </a-button>
        </div>
      </div>

      <!-- 内容区域 -->
      <div class="admin-content">
        <router-view />
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import {
  IconSettings,
  IconDashboard,
  IconFile,
  IconUserGroup,
  IconApps,
  IconSafe,
  IconImage,
  IconFolder,
  IconDesktop,
  IconBarChart,
  IconMenuFold,
  IconMenuUnfold,
  IconHome
} from '@arco-design/web-vue/es/icon'

const router = useRouter()
const route = useRoute()

// 控制侧边栏折叠
const collapsed = ref(false)

// 菜单状态
const selectedKeys = ref(['dashboard'])
const openKeys = ref(['system'])

// 面包屑映射
const breadcrumbMap: Record<string, { parent: string; current: string }> = {
  'dashboard': { parent: '系统管理', current: '数据统计' },
  'user-list': { parent: '系统管理', current: '用户管理' },
  'department-list': { parent: '系统管理', current: '部门管理' },
  'role-manage': { parent: '系统管理', current: '角色管理' },
  'logs': { parent: '系统管理', current: '日志管理' },
  'gallery-category': { parent: '图片管理', current: '图片分类' },
  'gallery-list': { parent: '图片管理', current: '图片列表' },
  'material-category': { parent: '素材管理', current: '素材分类' },
  'material-list': { parent: '素材管理', current: '素材列表' },
  'design-statistics': { parent: '设计任务', current: '工作统计' }
}

const currentBreadcrumb = computed(() => {
  const key = selectedKeys.value[0]
  return breadcrumbMap[key] || { parent: '管理系统', current: '首页' }
})

// 路由映射
const routeMap: Record<string, string> = {
  'dashboard': '/admin/dashboard',
  'logs': '/admin/logs',
  'user-list': '/admin/user-list',
  'department-list': '/admin/department-list',
  'role-manage': '/admin/role-manage',
  'gallery-category': '/admin/gallery-category',
  'gallery-list': '/admin/gallery-list',
  'document-list': '/admin/document-list',
  'suite-management': '/admin/suite-management',
  'material-category': '/admin/material-category',
  'material-list': '/admin/material-list',
  'template-category': '/admin/template-category',
  'template-list': '/admin/template-list',
  'design-statistics': '/admin/design-statistics'
}

// 菜单点击处理
const handleMenuClick = (key: string) => {
  const path = routeMap[key]
  if (path) {
    router.push(path)
  }
}

// 返回首页
const goHome = () => {
  router.push('/')
}

// 监听路由变化更新菜单状态
watch(() => route.path, (newPath) => {
  const pathMap: Record<string, string> = {
    '/admin/dashboard': 'dashboard',
    '/admin/logs': 'logs',
    '/admin/user-list': 'user-list',
    '/admin/department-list': 'department-list',
    '/admin/role-manage': 'role-manage',
    '/admin/gallery-category': 'gallery-category',
    '/admin/gallery-list': 'gallery-list',
    '/admin/document-list': 'document-list',
    '/admin/suite-management': 'suite-management',
    '/admin/material-category': 'material-category',
    '/admin/material-list': 'material-list',
    '/admin/design-statistics': 'design-statistics'
  }

  const key = pathMap[newPath]
  if (key) {
    selectedKeys.value = [key]

    // 自动展开对应的父菜单
    if (key.startsWith('dashboard') || key.startsWith('logs') || key.startsWith('user') || key.startsWith('department') || key.startsWith('role')) {
      openKeys.value = ['system']
    } else if (key.startsWith('gallery') || key.startsWith('document') || key.startsWith('suite')) {
      openKeys.value = ['gallery']
    } else if (key.startsWith('material')) {
      openKeys.value = ['material']
    } else if (key.startsWith('design')) {
      openKeys.value = ['design']
    }
  }
}, { immediate: true })
</script>
<style scoped>
.admin-layout {
  height: calc(100vh - 66px);
  background: #f0f2f5;
  display: flex;
  overflow: hidden;
}

.admin-sidebar {
  background: #fff;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
  height: calc(100vh - 66px);
}



.admin-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: calc(100vh - 66px);
  overflow: hidden;
}

.sidebar-header {
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
  text-align: center;
}

.sidebar-header h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1d2129;
}

.admin-menu {
  border-right: none;
}

.admin-header {
  background: #fff;
  padding: 0 16px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  height: 64px;
  flex-shrink: 0;
}

.collapse-btn {
  font-size: 16px;
  width: 48px;
  height: 48px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.breadcrumb {
  margin: 0;
}

.admin-content {
  margin: 16px;
  padding: 24px;
  background: #fff;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
  flex: 1;
}

/* 自定义菜单样式 */
:deep(.arco-menu-item) {
  margin: 2px 8px;
  border-radius: 6px;
}

:deep(.arco-menu-item:hover) {
  background-color: #f2f3f5;
}

:deep(.arco-menu-item-selected) {
  background-color: #e8f4ff !important;
  color: #165dff !important;
}

:deep(.arco-menu-item-selected .arco-icon) {
  color: #165dff !important;
}

:deep(.arco-menu-sub-menu-title) {
  margin: 2px 8px;
  border-radius: 6px;
}

:deep(.arco-menu-sub-menu-title:hover) {
  background-color: #f2f3f5;
}

:deep(.arco-menu-sub-menu-selected > .arco-menu-sub-menu-title) {
  background-color: #e8f4ff;
  color: #165dff;
}

:deep(.arco-menu-sub-menu-selected > .arco-menu-sub-menu-title .arco-icon) {
  color: #165dff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .admin-content {
    margin: 8px;
    padding: 16px;
  }

  .header-right {
    gap: 8px;
  }

  .breadcrumb {
    display: none;
  }
}

@media (max-width: 480px) {
  .admin-sidebar {
    width: 60px;
  }

  .menu-group-title span,
  .menu-item span {
    display: none;
  }

  .sidebar-header h2 {
    display: none;
  }

  .menu-items {
    padding-left: 0;
  }

  .menu-item {
    justify-content: center;
    padding: 12px 8px;
  }
}
</style>