/**
 * 拼音筛选工具函数
 * 支持输入英文筛选中文，如输入 l 可以筛选出 李
 * 使用 pinyin-pro 库进行拼音转换
 */

import { pinyin } from 'pinyin-pro'

/**
 * 获取文本的拼音首字母组合
 * @param text 中文文本
 * @returns 拼音首字母组合
 */
function getPinyinInitials(text: string): string {
  try {
    return pinyin(text, {
      pattern: 'first',
      toneType: 'none',
      type: 'string'
    }).toLowerCase()
  } catch (error) {
    // 如果转换失败，返回原文本的小写形式
    return text.toLowerCase()
  }
}

/**
 * 获取文本的完整拼音组合
 * @param text 中文文本
 * @returns 完整拼音组合
 */
function getFullPinyinText(text: string): string {
  try {
    return pinyin(text, {
      pattern: 'pinyin',
      toneType: 'none',
      type: 'string'
    }).toLowerCase()
  } catch (error) {
    // 如果转换失败，返回原文本的小写形式
    return text.toLowerCase()
  }
}

/**
 * 拼音筛选函数
 * @param options 选项数组
 * @param searchText 搜索文本
 * @param labelKey 显示文本的键名，默认为 'label'
 * @returns 筛选后的选项数组
 */
export function filterOptionsByPinyin<T extends Record<string, any>>(
  options: T[],
  searchText: string,
  labelKey: string = 'label'
): T[] {
  if (!searchText) return options

  const searchLower = searchText.toLowerCase()

  return options.filter(option => {
    const label = option[labelKey] || ''
    const labelStr = String(label)

    // 直接匹配
    if (labelStr.toLowerCase().includes(searchLower)) {
      return true
    }

    // 拼音首字母匹配
    const initials = getPinyinInitials(labelStr)
    if (initials.includes(searchLower)) {
      return true
    }

    // 完整拼音匹配
    const fullPinyin = getFullPinyinText(labelStr)
    if (fullPinyin.includes(searchLower)) {
      return true
    }

    return false
  })
}

/**
 * 为 Arco Design Select 组件创建筛选函数
 * @param labelKey 显示文本的键名
 * @returns 筛选函数
 */
export function createSelectFilter(labelKey: string = 'label') {
  return (inputValue: string, option: any) => {
    if (!inputValue) return true

    const label = option[labelKey] || option.label || ''
    const labelStr = String(label)
    const searchLower = inputValue.toLowerCase()

    // 直接匹配
    if (labelStr.toLowerCase().includes(searchLower)) {
      return true
    }

    // 拼音首字母匹配
    const initials = getPinyinInitials(labelStr)
    if (initials.includes(searchLower)) {
      return true
    }

    // 完整拼音匹配
    const fullPinyin = getFullPinyinText(labelStr)
    if (fullPinyin.includes(searchLower)) {
      return true
    }

    return false
  }
}

export default {
  filterOptionsByPinyin,
  createSelectFilter,
  getPinyinInitials,
  getFullPinyinText
}
