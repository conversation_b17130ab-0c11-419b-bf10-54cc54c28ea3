<template>
  <div class="logs">
    <h1>日志管理</h1>
    
    <a-card>
      <a-table
        :columns="columns"
        :data="logData"
        :pagination="pagination"
        :loading="loading"
      >
        <template #level="{ record }">
          <a-tag :color="getLevelColor(record.level)">
            {{ record.level }}
          </a-tag>
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive } from 'vue'

const loading = ref(false)
const logData = ref([
  {
    id: 1,
    level: 'INFO',
    message: '用户登录成功',
    user: 'admin',
    ip: '*************',
    time: '2025-07-01 14:30:25'
  },
  {
    id: 2,
    level: 'ERROR',
    message: '数据库连接失败',
    user: 'system',
    ip: '127.0.0.1',
    time: '2025-07-01 14:25:10'
  },
  {
    id: 3,
    level: 'WARN',
    message: '文件上传大小超限',
    user: 'user001',
    ip: '*************',
    time: '2025-07-01 14:20:15'
  }
])

const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 3
})

const columns = [
  { title: '级别', dataIndex: 'level', slotName: 'level', width: 100 },
  { title: '消息', dataIndex: 'message', width: 300 },
  { title: '用户', dataIndex: 'user', width: 120 },
  { title: 'IP地址', dataIndex: 'ip', width: 150 },
  { title: '时间', dataIndex: 'time', width: 180 }
]

const getLevelColor = (level) => {
  const colorMap = {
    'INFO': 'blue',
    'WARN': 'orange',
    'ERROR': 'red'
  }
  return colorMap[level] || 'gray'
}

onMounted(() => {
  // 获取日志数据
})
</script>

<style scoped>
.logs h1 {
  margin-bottom: 24px;
  color: #262626;
}
</style>
