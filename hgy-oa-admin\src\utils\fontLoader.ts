import { Message, Notification } from '@arco-design/web-vue'
import FontFaceObserver from 'fontfaceobserver'
import { h } from 'vue'
import FontLoadingProgress from '@/components/fontLoader/FontLoadingProgress.vue'

interface FontLoadOptions {
  timeout?: number
  showProgress?: boolean
  showIndividualMessages?: boolean
}

interface FontLoadResult {
  success: boolean
  loaded: number
  failed: number
  failedFonts: string[]
  totalTime: number
}

class FontLoader {
  private loadingFonts = new Set<string>()
  private loadedFonts = new Set<string>()
  private failedFonts = new Set<string>()

  /**
   * 加载单个字体
   */
  async loadSingleFont(
    fontName: string, 
    options: FontLoadOptions = {}
  ): Promise<boolean> {
    const { 
      timeout = 15000, 
      showProgress = true,
      showIndividualMessages = true 
    } = options

    // 如果已经加载过，直接返回
    if (this.loadedFonts.has(fontName)) {
      if (showIndividualMessages) {
        Message.info(`字体「${fontName}」已加载`)
      }
      return true
    }

    // 如果正在加载，等待加载完成
    if (this.loadingFonts.has(fontName)) {
      if (showIndividualMessages) {
        Message.info(`字体「${fontName}」正在加载中...`)
      }
      return new Promise((resolve) => {
        const checkInterval = setInterval(() => {
          if (!this.loadingFonts.has(fontName)) {
            clearInterval(checkInterval)
            resolve(this.loadedFonts.has(fontName))
          }
        }, 100)
      })
    }

    this.loadingFonts.add(fontName)
    const startTime = Date.now()
    
    let loading: any = null
    if (showProgress) {
      loading = Message.loading({
        content: `🔄 正在加载字体「${fontName}」...`,
        duration: 0
      })
    }

    try {
      const font = new FontFaceObserver(fontName)
      await font.load(null, timeout)
      
      const loadTime = ((Date.now() - startTime) / 1000).toFixed(1)
      this.loadedFonts.add(fontName)
      
      if (loading) loading.close()
      
      if (showIndividualMessages) {
        Message.success({
          content: `✅ 字体「${fontName}」加载成功 (${loadTime}s)`,
          duration: 2,
        })
      }
      
      return true
    } catch (error) {
      this.failedFonts.add(fontName)
      
      if (loading) loading.close()
      
      if (showIndividualMessages) {
        Message.error({
          content: `❌ 字体「${fontName}」加载失败`,
          duration: 3,
        })
      }
      
      console.error(`字体加载失败: ${fontName}`, error)
      return false
    } finally {
      this.loadingFonts.delete(fontName)
    }
  }

  /**
   * 批量加载字体
   */
  async loadFonts(
    fonts: string[],
    options: FontLoadOptions & { maxConcurrent?: number } = {}
  ): Promise<FontLoadResult> {
    const {
      timeout = 15000,
      maxConcurrent = 3,
      showProgress = true
    } = options

    // 过滤掉已加载的字体
    const fontsToLoad = fonts.filter(fontName => !this.loadedFonts.has(fontName))

    if (fontsToLoad.length === 0) {
      // 如果没有需要加载的字体，不显示任何提示
      return {
        success: true,
        loaded: fonts.length, // 返回原始字体数量，因为它们都已加载
        failed: 0,
        failedFonts: [],
        totalTime: 0
      }
    }

    // 使用过滤后的字体列表
    const actualFonts = fontsToLoad

    const startTime = Date.now()
    let loaded = 0
    let failed = 0
    const failedFonts: string[] = []
    let currentFont = ''

    // 创建进度通知
    let progressNotificationKey: string | null = null
    if (showProgress) {
      progressNotificationKey = `font-progress-${Date.now()}`

      const updateProgress = (current: string = '') => {
        currentFont = current
        Notification.info({
          id: progressNotificationKey!,
          title: '',
          content: h(FontLoadingProgress, {
            loaded,
            total: actualFonts.length,
            failed,
            currentFont
          }),
          duration: 0,
          closable: false,
        })
      }

      updateProgress()
    }

    // 并发加载函数
    const loadFont = async (fontName: string) => {
      try {
        if (showProgress && progressNotificationKey) {
          // 更新当前加载的字体
          Notification.info({
            id: progressNotificationKey,
            title: '',
            content: h(FontLoadingProgress, {
              loaded,
              total: actualFonts.length,
              failed,
              currentFont: fontName
            }),
            duration: 0,
            closable: false,
          })
        }

        const success = await this.loadSingleFont(fontName, {
          timeout,
          showProgress: false,
          showIndividualMessages: false
        })

        if (success) {
          loaded++
        } else {
          failed++
          failedFonts.push(fontName)
        }
      } finally {
        // 更新进度
        if (showProgress && progressNotificationKey) {
          Notification.info({
            id: progressNotificationKey,
            title: '',
            content: h(FontLoadingProgress, {
              loaded,
              total: actualFonts.length,
              failed,
              currentFont: ''
            }),
            duration: 0,
            closable: false,
          })
        }
      }
    }

    // 分批并发控制
    const batchPromises = []
    for (let i = 0; i < actualFonts.length; i += maxConcurrent) {
      const batch = actualFonts.slice(i, i + maxConcurrent)
      batchPromises.push(Promise.all(batch.map(font => loadFont(font))))
    }

    await Promise.all(batchPromises)

    const totalTime = (Date.now() - startTime) / 1000

    // 延迟一下让用户看到100%
    if (showProgress && progressNotificationKey) {
      setTimeout(() => {
        Notification.remove(progressNotificationKey!)
        
        // 最终结果反馈
        if (failed === 0) {
          Message.success({
            content: `🎉 所有字体加载完成！共 ${loaded} 个字体 (${totalTime.toFixed(1)}s)`,
            duration: 3,
          })
        } else if (loaded > 0) {
          Message.warning({
            content: `⚠️ 字体加载完成，${loaded} 个成功，${failed} 个失败 (${totalTime.toFixed(1)}s)`,
            duration: 4,
          })
        } else {
          Message.error({
            content: `❌ 所有字体加载失败，请检查网络连接`,
            duration: 4,
          })
        }
      }, 500)
    }

    return {
      success: failed === 0,
      loaded,
      failed,
      failedFonts,
      totalTime
    }
  }

  /**
   * 获取加载状态
   */
  getLoadStatus() {
    return {
      loading: Array.from(this.loadingFonts),
      loaded: Array.from(this.loadedFonts),
      failed: Array.from(this.failedFonts)
    }
  }

  /**
   * 清除缓存
   */
  clearCache() {
    this.loadedFonts.clear()
    this.failedFonts.clear()
  }
}

// 导出单例
export const fontLoader = new FontLoader()
export default fontLoader
