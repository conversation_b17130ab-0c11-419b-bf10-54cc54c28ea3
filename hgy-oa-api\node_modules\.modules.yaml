hoistPattern:
  - '*'
hoistedDependencies:
  accepts@1.3.8:
    accepts: private
  adler-32@1.3.1:
    adler-32: private
  agentkeepalive@4.6.0:
    agentkeepalive: private
  any-promise@1.3.0:
    any-promise: private
  anymatch@3.1.3:
    anymatch: private
  append-field@1.0.0:
    append-field: private
  array-flatten@1.1.1:
    array-flatten: private
  asynckit@0.4.0:
    asynckit: private
  aws-ssl-profiles@1.1.2:
    aws-ssl-profiles: private
  balanced-match@1.0.2:
    balanced-match: private
  before@0.0.1:
    before: private
  binary-extensions@2.3.0:
    binary-extensions: private
  block-stream2@2.1.0:
    block-stream2: private
  body-parser@1.20.3:
    body-parser: private
  brace-expansion@1.1.12:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  buffer-from@1.1.2:
    buffer-from: private
  busboy@1.6.0:
    busboy: private
  bytes@3.1.2:
    bytes: private
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: private
  call-bound@1.0.4:
    call-bound: private
  cfb@1.2.2:
    cfb: private
  chokidar@3.6.0:
    chokidar: private
  codepage@1.15.0:
    codepage: private
  combined-stream@1.0.8:
    combined-stream: private
  compressible@2.0.18:
    compressible: private
  concat-map@0.0.1:
    concat-map: private
  concat-stream@2.0.0:
    concat-stream: private
  content-disposition@0.5.4:
    content-disposition: private
  content-type@1.0.5:
    content-type: private
  cookie-signature@1.0.6:
    cookie-signature: private
  cookie@0.7.1:
    cookie: private
  copy-to@2.0.1:
    copy-to: private
  crc-32@1.2.2:
    crc-32: private
  crc32@0.2.2:
    crc32: private
  data-uri-to-buffer@4.0.1:
    data-uri-to-buffer: private
  debug@2.6.9:
    debug: private
  default-user-agent@1.0.0:
    default-user-agent: private
  delayed-stream@1.0.0:
    delayed-stream: private
  denque@2.1.0:
    denque: private
  depd@2.0.0:
    depd: private
  destroy@1.2.0:
    destroy: private
  digest-header@1.1.0:
    digest-header: private
  dunder-proto@1.0.1:
    dunder-proto: private
  ee-first@1.1.1:
    ee-first: private
  encodeurl@2.0.0:
    encodeurl: private
  end-of-stream@1.4.5:
    end-of-stream: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-object-atoms@1.1.1:
    es-object-atoms: private
  es-set-tostringtag@2.1.0:
    es-set-tostringtag: private
  escape-html@1.0.3:
    escape-html: private
  etag@1.8.1:
    etag: private
  extend-shallow@2.0.1:
    extend-shallow: private
  fetch-blob@3.2.0:
    fetch-blob: private
  fill-range@7.1.1:
    fill-range: private
  finalhandler@1.3.1:
    finalhandler: private
  formdata-polyfill@4.0.10:
    formdata-polyfill: private
  formstream@1.5.1:
    formstream: private
  forwarded@0.2.0:
    forwarded: private
  frac@1.1.2:
    frac: private
  fresh@0.5.2:
    fresh: private
  fsevents@2.3.3:
    fsevents: private
  function-bind@1.1.2:
    function-bind: private
  generate-function@2.3.1:
    generate-function: private
  get-intrinsic@1.3.0:
    get-intrinsic: private
  get-proto@1.0.1:
    get-proto: private
  glob-parent@5.1.2:
    glob-parent: private
  gopd@1.2.0:
    gopd: private
  graceful-fs@4.2.11:
    graceful-fs: private
  has-flag@3.0.0:
    has-flag: private
  has-symbols@1.1.0:
    has-symbols: private
  has-tostringtag@1.0.2:
    has-tostringtag: private
  hasown@2.0.2:
    hasown: private
  http-errors@2.0.0:
    http-errors: private
  humanize-ms@1.2.1:
    humanize-ms: private
  iconv-lite@0.6.3:
    iconv-lite: private
  ignore-by-default@1.0.1:
    ignore-by-default: private
  inherits@2.0.4:
    inherits: private
  ipaddr.js@1.9.1:
    ipaddr.js: private
  is-binary-path@2.1.0:
    is-binary-path: private
  is-extendable@0.1.1:
    is-extendable: private
  is-extglob@2.1.1:
    is-extglob: private
  is-glob@4.0.3:
    is-glob: private
  is-number@7.0.0:
    is-number: private
  is-property@1.0.2:
    is-property: private
  jsonfile@6.1.0:
    jsonfile: private
  long@5.3.2:
    long: private
  lru-cache@7.18.3:
    lru-cache: private
  lru.min@1.1.2:
    lru.min: private
  math-intrinsics@1.1.0:
    math-intrinsics: private
  media-typer@0.3.0:
    media-typer: private
  merge-descriptors@1.0.3:
    merge-descriptors: private
  methods@1.1.2:
    methods: private
  mime-db@1.54.0:
    mime-db: private
  mime-types@2.1.35:
    mime-types: private
  mime@2.6.0:
    mime: private
  minimatch@3.1.2:
    minimatch: private
  minimist@1.2.8:
    minimist: private
  mkdirp@0.5.6:
    mkdirp: private
  mockdate@3.0.5:
    mockdate: private
  ms@2.0.0:
    ms: private
  mz@2.7.0:
    mz: private
  named-placeholders@1.1.3:
    named-placeholders: private
  negotiator@0.6.4:
    negotiator: private
  node-domexception@1.0.0:
    node-domexception: private
  node-hex@1.0.1:
    node-hex: private
  normalize-path@3.0.0:
    normalize-path: private
  object-assign@4.1.1:
    object-assign: private
  object-inspect@1.13.4:
    object-inspect: private
  on-finished@2.4.1:
    on-finished: private
  on-headers@1.0.2:
    on-headers: private
  once@1.4.0:
    once: private
  os-name@1.0.3:
    os-name: private
  osx-release@1.1.0:
    osx-release: private
  parseurl@1.3.3:
    parseurl: private
  path-to-regexp@0.1.12:
    path-to-regexp: private
  pause-stream@0.0.11:
    pause-stream: private
  picomatch@2.3.1:
    picomatch: private
  process@0.11.10:
    process: private
  proxy-addr@2.0.7:
    proxy-addr: private
  pstree.remy@1.1.8:
    pstree.remy: private
  pump@3.0.3:
    pump: private
  qs@6.13.0:
    qs: private
  range-parser@1.2.1:
    range-parser: private
  raw-body@2.5.2:
    raw-body: private
  readable-stream@3.6.2:
    readable-stream: private
  readdirp@3.6.0:
    readdirp: private
  safe-buffer@5.2.1:
    safe-buffer: private
  safer-buffer@2.1.2:
    safer-buffer: private
  semver@7.7.2:
    semver: private
  send@0.19.0:
    send: private
  seq-queue@0.0.5:
    seq-queue: private
  serve-static@1.16.2:
    serve-static: private
  setprototypeof@1.2.0:
    setprototypeof: private
  side-channel-list@1.0.0:
    side-channel-list: private
  side-channel-map@1.0.1:
    side-channel-map: private
  side-channel-weakmap@1.0.2:
    side-channel-weakmap: private
  side-channel@1.1.0:
    side-channel: private
  simple-update-notifier@2.0.0:
    simple-update-notifier: private
  sqlstring@2.3.3:
    sqlstring: private
  ssf@0.11.2:
    ssf: private
  statuses@2.0.1:
    statuses: private
  streamsearch@1.1.0:
    streamsearch: private
  string_decoder@1.3.0:
    string_decoder: private
  supports-color@5.5.0:
    supports-color: private
  thenify-all@1.6.0:
    thenify-all: private
  thenify@3.3.1:
    thenify: private
  through@2.3.8:
    through: private
  to-regex-range@5.0.1:
    to-regex-range: private
  toidentifier@1.0.1:
    toidentifier: private
  touch@3.1.1:
    touch: private
  tunnel-agent@0.6.0:
    tunnel-agent: private
  type-is@1.6.18:
    type-is: private
  typedarray@0.0.6:
    typedarray: private
  typescript@4.9.5:
    typescript: private
  undefsafe@2.0.5:
    undefsafe: private
  unescape@1.0.1:
    unescape: private
  universalify@2.0.1:
    universalify: private
  unpipe@1.0.0:
    unpipe: private
  urllib@2.44.0:
    urllib: private
  util-deprecate@1.0.2:
    util-deprecate: private
  util@0.10.4:
    util: private
  utility@1.18.0:
    utility: private
  utils-merge@1.0.1:
    utils-merge: private
  vary@1.1.2:
    vary: private
  web-streams-polyfill@3.3.3:
    web-streams-polyfill: private
  win-release@1.1.1:
    win-release: private
  wmf@1.0.2:
    wmf: private
  word@0.3.0:
    word: private
  wrappy@1.0.2:
    wrappy: private
  xtend@4.0.2:
    xtend: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@9.15.0
pendingBuilds: []
prunedAt: Wed, 09 Jul 2025 09:13:36 GMT
publicHoistPattern:
  - '*eslint*'
  - '*prettier*'
registries:
  default: https://registry.npmjs.org/
skipped:
  - fsevents@2.3.3
storeDir: D:\.pnpm-store\v3
virtualStoreDir: D:\code\gitlab\oa\hgy-oa-api\node_modules\.pnpm
virtualStoreDirMaxLength: 120
