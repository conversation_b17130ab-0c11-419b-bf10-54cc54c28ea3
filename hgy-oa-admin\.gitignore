# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Dependencies
node_modules
.npm
.pnpm-debug.log*

# Build outputs
dist
dist-ssr
build/
out/
.output/
.vercel/
.netlify/
*.local

# Cache directories
.cache/
.parcel-cache/
.vite/
.rollup.cache/
.turbo/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# TypeScript
*.tsbuildinfo

# Auto-generated files
auto-imports.d.ts
components.d.ts

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Test coverage
coverage/
.nyc_output/

# Optional caches
.eslintcache
.stylelintcache

# Temporary folders
tmp/
temp/

# Lock files (uncomment if you want to ignore them)
# package-lock.json
# yarn.lock
# pnpm-lock.yaml
