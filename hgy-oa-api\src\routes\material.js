import express from 'express';
import multer from 'multer';
import path from 'path';
import { query, paginate } from '../config/database.js';
import { uploadToQiniu, generateFileKey, getPrivateFileUrl } from '../config/qiniu.js';

const router = express.Router();

// 配置multer用于文件上传
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB
  },
  fileFilter: (req, file, cb) => {
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('只允许上传图片文件'));
    }
  }
});

// 中间件：修复FormData中文编码问题
const fixFormDataEncoding = (req, res, next) => {
  if (req.body) {
    Object.keys(req.body).forEach(key => {
      if (typeof req.body[key] === 'string') {
        const value = req.body[key];
        // 检查是否包含乱码字符
        if (value.includes('�')) {
          try {
            // 方法1: 尝试从latin1转换为utf8
            let fixed = Buffer.from(value, 'latin1').toString('utf8');

            // 如果还是有乱码，尝试其他方法
            if (fixed.includes('�')) {
              // 方法2: 尝试从binary转换
              fixed = Buffer.from(value, 'binary').toString('utf8');
            }

            // 如果还是有乱码，尝试URL解码
            if (fixed.includes('�')) {
              try {
                fixed = decodeURIComponent(escape(value));
              } catch (e) {
                console.log('URL解码失败:', e.message);
              }
            }

            req.body[key] = fixed;
            console.log(`修复字段 ${key}: ${value} -> ${req.body[key]}`);
          } catch (error) {
            console.error(`修复字段 ${key} 失败:`, error);
          }
        }
      }
    });
  }
  next();
};

// 上传素材到七牛云并保存到数据库
router.post('/upload', upload.single('file'), fixFormDataEncoding, async (req, res) => {
  try {
    if (!req.file) {
      return res.error('请选择要上传的素材文件', 400);
    }

    let {
      title,
      description = '',
      category_id
    } = req.body;

    console.log('上传API接收到的原始数据:');
    console.log('标题:', title);
    console.log('描述:', description);
    console.log('分类ID:', category_id);

    // 尝试修复编码问题
    try {
      if (title && typeof title === 'string') {
        // 检查是否是乱码，如果是则尝试重新编码
        if (title.includes('�')) {
          console.log('检测到标题乱码，尝试修复...');
          // 尝试从latin1转换为utf8
          const titleBuffer = Buffer.from(title, 'latin1');
          title = titleBuffer.toString('utf8');
          console.log('修复后的标题:', title);
        }
      }

      if (description && typeof description === 'string') {
        if (description.includes('�')) {
          console.log('检测到描述乱码，尝试修复...');
          const descBuffer = Buffer.from(description, 'latin1');
          description = descBuffer.toString('utf8');
          console.log('修复后的描述:', description);
        }
      }
    } catch (error) {
      console.error('编码修复失败:', error);
    }

    console.log('最终使用的数据:');
    console.log('标题:', title);
    console.log('描述:', description);

    // 使用登录用户的ID，如果没有登录用户则查找默认用户
    let finalUploadUserId;
    if (req.user && req.user.id) {
      finalUploadUserId = req.user.id;
      console.log('使用登录用户ID:', finalUploadUserId, '用户名:', req.user.username);
    } else {
      // 如果没有登录用户，查找一个存在的用户ID作为默认值
      try {
        const users = await query('SELECT id FROM system_users WHERE is_active = 1 ORDER BY id LIMIT 1');
        if (users.length > 0) {
          finalUploadUserId = users[0].id;
          console.log('使用默认用户ID:', finalUploadUserId);
        } else {
          return res.error('系统中没有可用的用户，请先创建用户', 400);
        }
      } catch (error) {
        console.error('查询用户失败:', error);
        return res.error('查询用户失败', 500);
      }
    }

    if (!title) {
      return res.error('素材标题不能为空', 400);
    }

    // 生成七牛云文件key
    const fileKey = generateFileKey(req.file.originalname, 'materials');
    console.log('生成的文件key:', fileKey);

    // 上传到七牛云
    console.log('开始上传到七牛云...');
    const qiniuResult = await uploadToQiniu(req.file.buffer, fileKey, req.file.originalname);
    console.log('七牛云上传结果:', qiniuResult);

    // 为私有Bucket生成带签名的下载URL
    let fileUrl;
    try {
      fileUrl = getPrivateFileUrl(qiniuResult.key, 86400 * 365); // 1年有效期
      console.log('生成的私有URL:', fileUrl);
    } catch (error) {
      console.error('生成私有URL失败，使用公开URL:', error);
      fileUrl = qiniuResult.url; // 降级使用公开URL
    }

    const format = req.file.mimetype.split('/')[1].toUpperCase();

    // 插入到image_materials表
    const insertSql = `
      INSERT INTO editor_materials (
        title, file_path, thumbnail_path, width, height, file_size, format,
        category_id, description, status, upload_user, qiniu_key, qiniu_hash,
        created_at, updated_at
      )
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 1, ?, ?, ?, NOW(), NOW())
    `;

    const result = await query(insertSql, [
      title,
      fileUrl,
      fileUrl, // 使用同一个URL作为缩略图
      qiniuResult.width || 0,
      qiniuResult.height || 0,
      qiniuResult.size,
      format,
      category_id || null,
      description,
      finalUploadUserId,
      qiniuResult.key,
      qiniuResult.hash
    ]);

    const materialInfo = {
      id: result.insertId,
      title,
      file_path: fileUrl,
      thumbnail_path: fileUrl,
      file_size: qiniuResult.size,
      width: qiniuResult.width,
      height: qiniuResult.height,
      format,
      description,
      category_id,
      qiniu_key: qiniuResult.key,
      qiniu_hash: qiniuResult.hash
    };

    res.success(materialInfo, '素材上传成功');
  } catch (error) {
    console.error('上传素材失败:', error);
    res.error('上传素材失败: ' + (error.message || '未知错误'));
  }
});

// 更新素材（支持文件替换）
router.post('/update-with-file', upload.single('file'), async (req, res) => {
  try {
    const { id, title, description, category_id } = req.body;

    if (!id) {
      return res.error('素材ID不能为空', 400);
    }

    if (!title) {
      return res.error('标题不能为空', 400);
    }

    const upload_user = req.user?.id || 1; // 从登录用户获取，暂时默认为1

    let updateData = {
      title,
      description: description || '',
      category_id: category_id || null,
      updated_at: 'NOW()'
    };

    // 如果有新文件，处理文件上传
    if (req.file) {
      // 生成七牛云文件key
      const fileKey = generateFileKey(req.file.originalname, 'materials');
      console.log('生成的文件key:', fileKey);

      // 上传到七牛云
      console.log('开始上传到七牛云...');
      const qiniuResult = await uploadToQiniu(req.file.buffer, fileKey, req.file.originalname);
      console.log('七牛云上传结果:', qiniuResult);

      // 为私有Bucket生成带签名的下载URL
      let fileUrl;
      try {
        fileUrl = getPrivateFileUrl(qiniuResult.key, 86400 * 365); // 1年有效期
        console.log('生成的私有URL:', fileUrl);
      } catch (error) {
        console.error('生成私有URL失败，使用公开URL:', error);
        fileUrl = qiniuResult.url; // 降级使用公开URL
      }

      const format = req.file.mimetype.split('/')[1].toUpperCase();

      // 更新文件相关字段
      updateData = {
        ...updateData,
        file_path: fileUrl,
        thumbnail_path: fileUrl,
        width: qiniuResult.width || 0,
        height: qiniuResult.height || 0,
        file_size: qiniuResult.size,
        format: format,
        qiniu_key: qiniuResult.key,
        qiniu_hash: qiniuResult.hash
      };
    }

    // 构建更新SQL
    const setClause = Object.keys(updateData).map(key =>
      key === 'updated_at' ? `${key} = NOW()` : `${key} = ?`
    ).join(', ');

    const values = Object.entries(updateData)
      .filter(([key]) => key !== 'updated_at')
      .map(([, value]) => value);

    const sql = `UPDATE editor_materials SET ${setClause} WHERE id = ?`;
    const result = await query(sql, [...values, id]);

    if (result.affectedRows === 0) {
      return res.error('素材不存在或更新失败', 404);
    }

    res.success({ id: parseInt(id) }, '素材更新成功');
  } catch (error) {
    console.error('更新素材失败:', error);
    res.error('更新素材失败: ' + (error.message || '未知错误'));
  }
});

// ==================== 素材分类管理 ====================

// 获取素材分类树形结构
router.get('/categories', async (req, res) => {
  try {
    console.log('获取素材分类列表（一级分类）...');

    const sql = `
      SELECT
        id,
        name,
        icon,
        sort_order,
        description,
        status,
        created_at,
        updated_at
      FROM editor_material_categories
      WHERE status = 1
      ORDER BY sort_order ASC, id ASC
    `;

    const categories = await query(sql);

    // 获取每个分类的素材数量统计
    for (const category of categories) {
      try {
        const countSql = 'SELECT COUNT(*) as count FROM editor_materials WHERE category_id = ? AND status = 1';
        const result = await query(countSql, [category.id]);
        category.count = result[0]?.count || 0;
        console.log(`分类 ${category.name} 的素材数量: ${category.count}`);
      } catch (error) {
        console.error(`获取分类 ${category.name} 素材数量失败:`, error);
        category.count = 0;
      }
    }

    console.log('分类列表查询成功，共', categories.length, '个分类');
    res.success(categories);
  } catch (error) {
    console.error('获取素材分类失败:', error);
    res.error('获取素材分类失败');
  }
});

// 创建素材分类
router.post('/categories', async (req, res) => {
  try {
    const { name, parent_id, icon, sort_order = 0, description } = req.body;

    if (!name) {
      return res.error('分类名称不能为空', 400);
    }

    // 处理可能为undefined的值
    const parentIdValue = parent_id || null;
    const iconValue = icon || null;
    const sortOrderValue = sort_order || 0;
    const descriptionValue = description || null;

    // 检查同级分类名称是否重复
    const existingCategories = await query(
      'SELECT id FROM editor_material_categories WHERE name = ? AND parent_id = ? AND status = 1',
      [name, parentIdValue]
    );

    if (existingCategories.length > 0) {
      return res.error('同级分类名称已存在', 400);
    }

    const sql = `
      INSERT INTO editor_material_categories (name, parent_id, icon, sort_order, description, status, created_at, updated_at)
      VALUES (?, ?, ?, ?, ?, 1, NOW(), NOW())
    `;

    const result = await query(sql, [name, parentIdValue, iconValue, sortOrderValue, descriptionValue]);
    res.success({ id: result.insertId }, '素材分类创建成功');
  } catch (error) {
    console.error('创建素材分类失败:', error);
    res.error('创建素材分类失败');
  }
});

// 更新素材分类
router.put('/categories/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { name, parent_id, icon, sort_order, description } = req.body;

    if (!name) {
      return res.error('分类名称不能为空', 400);
    }

    // 处理可能为undefined的值
    const parentIdValue = parent_id || null;
    const iconValue = icon || null;
    const sortOrderValue = sort_order || 0;
    const descriptionValue = description || null;

    // 检查分类是否存在
    const existingCategory = await query(
      'SELECT id FROM editor_material_categories WHERE id = ? AND status = 1',
      [id]
    );

    if (existingCategory.length === 0) {
      return res.error('分类不存在', 404);
    }

    // 检查同级分类名称是否重复（排除自己）
    const duplicateCategories = await query(
      'SELECT id FROM editor_material_categories WHERE name = ? AND parent_id = ? AND id != ? AND status = 1',
      [name, parentIdValue, id]
    );

    if (duplicateCategories.length > 0) {
      return res.error('同级分类名称已存在', 400);
    }

    const sql = `
      UPDATE editor_material_categories
      SET name = ?, parent_id = ?, icon = ?, sort_order = ?, description = ?, updated_at = NOW()
      WHERE id = ? AND status = 1
    `;

    const result = await query(sql, [name, parentIdValue, iconValue, sortOrderValue, descriptionValue, id]);

    if (result.affectedRows === 0) {
      return res.error('更新失败', 500);
    }

    res.success(null, '素材分类更新成功');
  } catch (error) {
    console.error('更新素材分类失败:', error);
    res.error('更新素材分类失败');
  }
});

// 删除素材分类
router.delete('/categories/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // 检查是否有子分类
    const childCategories = await query(
      'SELECT id FROM editor_material_categories WHERE parent_id = ? AND status = 1',
      [id]
    );

    if (childCategories.length > 0) {
      return res.error('该分类下还有子分类，无法删除', 400);
    }

    // 软删除分类
    const sql = `
      UPDATE editor_material_categories
      SET status = 0, updated_at = NOW()
      WHERE id = ? AND status = 1
    `;

    const result = await query(sql, [id]);

    if (result.affectedRows === 0) {
      return res.error('分类不存在或删除失败', 404);
    }

    res.success(null, '素材分类删除成功');
  } catch (error) {
    console.error('删除素材分类失败:', error);
    res.error('删除素材分类失败');
  }
});

// ==================== 素材管理 ====================

// 获取素材列表
router.get('/list', async (req, res) => {
  try {
    console.log('开始获取素材列表...', req.query);

    const {
      page = 1,
      pageSize = 20,
      keyword,
      primaryCategory,
      secondaryCategory,
      categoryId
    } = req.query;

    // 构建基础SQL
    let sql = `
      SELECT
        im.id,
        im.title,
        'image' as type,
        im.file_path,
        im.thumbnail_path,
        im.width,
        im.height,
        im.file_size,
        im.format,
        im.category_id,
        im.description,
        im.status,
        im.upload_user,
        u.nickname as upload_user_nickname,
        DATE_FORMAT(im.created_at, '%Y-%m-%d') as created_at,
        DATE_FORMAT(im.updated_at, '%Y-%m-%d') as updated_at
      FROM editor_materials im
      LEFT JOIN system_users u ON im.upload_user = u.id
      WHERE im.status = 1
    `;

    let countSql = `
      SELECT COUNT(*) as total
      FROM editor_materials im
      WHERE im.status = 1
    `;

    let params = [];

    // 关键词搜索
    if (keyword) {
      sql += ' AND (im.title LIKE ? OR im.description LIKE ?)';
      countSql += ' AND (im.title LIKE ? OR im.description LIKE ?)';
      params.push(`%${keyword}%`, `%${keyword}%`);
    }

    // 分类筛选 - 优先使用 categoryId 参数
    if (categoryId) {
      sql += ' AND im.category_id = ?';
      countSql += ' AND im.category_id = ?';
      params.push(parseInt(categoryId));
    } else if (primaryCategory) {
      // 查找该一级分类下的所有子分类ID
      const subCategories = await query(`
        SELECT id FROM editor_material_categories
        WHERE (id = ? OR parent_id = ?) AND status = 1
      `, [parseInt(primaryCategory), parseInt(primaryCategory)]);

      if (subCategories.length > 0) {
        const categoryIds = subCategories.map(cat => cat.id);
        if (categoryIds.length === 1) {
          sql += ' AND im.category_id = ?';
          countSql += ' AND im.category_id = ?';
          params.push(categoryIds[0]);
        } else {
          const placeholders = categoryIds.map(() => '?').join(',');
          sql += ` AND im.category_id IN (${placeholders})`;
          countSql += ` AND im.category_id IN (${placeholders})`;
          params.push(...categoryIds);
        }
      }
    } else if (secondaryCategory) {
      // 二级分类筛选
      sql += ' AND im.category_id = ?';
      countSql += ' AND im.category_id = ?';
      params.push(parseInt(secondaryCategory));
    }

    // 分页
    const offset = (parseInt(page) - 1) * parseInt(pageSize);
    const limit = parseInt(pageSize);
    sql += ' ORDER BY im.created_at DESC LIMIT ? OFFSET ?';



    // 执行查询
    const imageMaterials = await query(sql, [...params, limit, offset]);

    console.log('查询到的素材数量:', imageMaterials.length);

    // 为每个素材添加分类信息
    for (const material of imageMaterials) {
      if (material.category_id) {
        const categorySql = `
          SELECT c1.id, c1.name, c2.id as parent_id, c2.name as parent_name
          FROM editor_material_categories c1
          LEFT JOIN editor_material_categories c2 ON c1.parent_id = c2.id
          WHERE c1.id = ? AND c1.status = 1
        `;
        const categoryResult = await query(categorySql, [material.category_id]);
        if (categoryResult.length > 0) {
          const category = categoryResult[0];
          material.categories = [];

          // 如果有父分类，先添加父分类
          if (category.parent_id) {
            material.categories.push({
              id: category.parent_id,
              name: category.parent_name
            });
          }

          // 添加当前分类
          material.categories.push({
            id: category.id,
            name: category.name
          });

          // 设置分类显示文本
          if (category.parent_id) {
            material.category_display = `${category.parent_name} / ${category.name}`;
          } else {
            material.category_display = category.name;
          }
        } else {
          material.categories = [];
          material.category_display = '';
        }
      } else {
        material.categories = [];
        material.category_display = '';
      }
    }

    // 获取总数
    const countResult = await query(countSql, params);
    const total = countResult[0]?.total || 0;

    res.success({
      list: imageMaterials,
      total: total,
      page: parseInt(req.query.page || 1),
      pageSize: parseInt(req.query.pageSize || 20)
    });
  } catch (error) {
    console.error('获取素材列表失败:', error);
    res.error('获取素材列表失败');
  }
});

// 创建素材
router.post('/', async (req, res) => {
  try {
    const {
      title,
      type,
      file_path,
      thumbnail_path,
      content,
      width,
      height,
      file_size,
      format,
      category_id,
      description
    } = req.body;

    if (!title || !type) {
      return res.error('标题和类型不能为空', 400);
    }

    const upload_user = req.user?.id || 1; // 从登录用户获取，暂时默认为1

    let result;
    if (type === 'text') {
      if (!content) {
        return res.error('文字内容不能为空', 400);
      }

      const sql = `
        INSERT INTO text_materials (title, content, category_id, description, status, upload_user, created_at, updated_at)
        VALUES (?, ?, ?, ?, 1, ?, NOW(), NOW())
      `;
      result = await query(sql, [title, content, category_id, description, upload_user]);
    } else if (type === 'image') {
      if (!file_path) {
        return res.error('图片文件路径不能为空', 400);
      }

      const sql = `
        INSERT INTO editor_materials (title, file_path, thumbnail_path, width, height, file_size, format, category_id, description, status, upload_user, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 1, ?, NOW(), NOW())
      `;
      result = await query(sql, [title, file_path, thumbnail_path, width, height, file_size, format, category_id, description, upload_user]);
    } else {
      return res.error('不支持的素材类型', 400);
    }

    res.success({ id: result.insertId }, '素材创建成功');
  } catch (error) {
    console.error('创建素材失败:', error);
    res.error('创建素材失败');
  }
});

// 更新素材
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { title, category_id, description, file_path, thumbnail_path, width, height, format } = req.body;

    if (!title) {
      return res.error('标题不能为空', 400);
    }

    // 检查素材是否存在（现在所有素材都在 editor_materials 表中）
    const checkSql = `
      SELECT id FROM editor_materials WHERE id = ? AND status != 0
    `;
    const checkResult = await query(checkSql, [id]);

    if (checkResult.length === 0) {
      return res.error('素材不存在', 404);
    }

    // 构建更新SQL
    let updateFields = ['title = ?', 'updated_at = NOW()'];
    let updateValues = [title];

    if (category_id !== undefined) {
      updateFields.push('category_id = ?');
      updateValues.push(category_id);
    }

    if (description !== undefined) {
      updateFields.push('description = ?');
      updateValues.push(description);
    }

    if (file_path !== undefined) {
      updateFields.push('file_path = ?');
      updateValues.push(file_path);
    }

    if (thumbnail_path !== undefined) {
      updateFields.push('thumbnail_path = ?');
      updateValues.push(thumbnail_path);
    }

    if (width !== undefined) {
      updateFields.push('width = ?');
      updateValues.push(width);
    }

    if (height !== undefined) {
      updateFields.push('height = ?');
      updateValues.push(height);
    }

    if (format !== undefined) {
      updateFields.push('format = ?');
      updateValues.push(format);
    }

    const sql = `
      UPDATE editor_materials
      SET ${updateFields.join(', ')}
      WHERE id = ?
    `;

    updateValues.push(id);
    const result = await query(sql, updateValues);

    if (result.affectedRows === 0) {
      return res.error('更新失败', 500);
    }

    res.success(null, '素材更新成功');
  } catch (error) {
    console.error('更新素材失败:', error);
    res.error('更新素材失败');
  }
});

// 删除素材
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // 检查素材是否存在（现在所有素材都在 editor_materials 表中）
    const checkSql = `
      SELECT id FROM editor_materials WHERE id = ? AND status != 0
    `;
    const checkResult = await query(checkSql, [id]);

    if (checkResult.length === 0) {
      return res.error('素材不存在', 404);
    }

    // 软删除（现在所有素材都在 editor_materials 表中）
    const sql = `UPDATE editor_materials SET status = 0, updated_at = NOW() WHERE id = ?`;
    const result = await query(sql, [id]);

    if (result.affectedRows === 0) {
      return res.error('删除失败', 500);
    }

    res.success(null, '素材删除成功');
  } catch (error) {
    console.error('删除素材失败:', error);
    res.error('删除素材失败');
  }
});

// 切换素材状态
router.put('/:id/status', async (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body;

    if (status === undefined) {
      return res.error('状态值不能为空', 400);
    }

    // 检查素材是否存在（现在所有素材都在 editor_materials 表中）
    const checkSql = `
      SELECT id FROM editor_materials WHERE id = ? AND status != 0
    `;
    const checkResult = await query(checkSql, [id]);

    if (checkResult.length === 0) {
      return res.error('素材不存在', 404);
    }

    const sql = `UPDATE editor_materials SET status = ?, updated_at = NOW() WHERE id = ?`;
    const result = await query(sql, [status, id]);

    if (result.affectedRows === 0) {
      return res.error('状态更新失败', 500);
    }

    res.success(null, '状态更新成功');
  } catch (error) {
    console.error('状态更新失败:', error);
    res.error('状态更新失败');
  }
});

// 获取素材列表
router.get('/materialList', async (req, res) => {
  try {
    const { pageNum = 1, pageSize = 10 } = req.query;

    // 根据路由路径确定表名
    const routePath = req.baseUrl;
    let tableName = 'text_materials';
    if (routePath.includes('/image')) {
      tableName = 'editor_materials';
    }
    
    let sql;
    if (tableName === 'editor_materials') {
      sql = `
        SELECT
          id,
          title,
          file_path,
          thumbnail_path,
          category_id,
          created_at,
          updated_at
        FROM ${tableName}
        WHERE status = 1
        ORDER BY created_at DESC
      `;
    } else {
      sql = `
        SELECT
          id,
          title,
          content,
          preview_url,
          category_id,
          tags,
          created_at,
          updated_at
        FROM ${tableName}
        WHERE status = 1
        ORDER BY created_at DESC
      `;
    }
    
    const result = await paginate(sql, [], pageNum, pageSize);
    
    // 解析tags字段
    result.records = result.records.map(item => ({
      ...item,
      tags: item.tags ? (typeof item.tags === 'string' ? JSON.parse(item.tags) : item.tags) : []
    }));
    
    res.success(result);
  } catch (error) {
    console.error('获取素材列表失败:', error);
    res.error('获取素材列表失败');
  }
});

// 根据分类获取素材
router.get('/materialList/category/:categoryId', async (req, res) => {
  try {
    const { categoryId } = req.params;
    const { pageNum = 1, pageSize = 10 } = req.query;

    // 根据路由路径确定表名
    const routePath = req.baseUrl;
    let tableName = 'text_materials';
    if (routePath.includes('/image')) {
      tableName = 'editor_materials';
    }
    
    const sql = `
      SELECT 
        id,
        title,
        content,
        preview_url,
        category_id,
        tags,
        created_at,
        updated_at
      FROM ${tableName}
      WHERE status = 1 AND category_id = ?
      ORDER BY created_at DESC
    `;
    
    const result = await paginate(sql, [categoryId], pageNum, pageSize);
    
    // 解析tags字段
    result.records = result.records.map(item => ({
      ...item,
      tags: item.tags ? (typeof item.tags === 'string' ? JSON.parse(item.tags) : item.tags) : []
    }));
    
    res.success(result);
  } catch (error) {
    console.error('获取分类素材失败:', error);
    res.error('获取分类素材失败');
  }
});

// 搜索素材
router.get('/materialList/search', async (req, res) => {
  try {
    const { keyword, pageNum = 1, pageSize = 10 } = req.query;

    if (!keyword) {
      return res.error('搜索关键词不能为空', 400);
    }

    // 根据路由路径确定表名
    const routePath = req.baseUrl;
    let tableName = 'text_materials';
    if (routePath.includes('/image')) {
      tableName = 'editor_materials';
    }
    
    const sql = `
      SELECT 
        id,
        title,
        content,
        preview_url,
        category_id,
        tags,
        created_at,
        updated_at
      FROM ${tableName}
      WHERE status = 1 AND (title LIKE ? OR content LIKE ? OR tags LIKE ?)
      ORDER BY created_at DESC
    `;
    
    const searchTerm = `%${keyword}%`;
    const result = await paginate(sql, [searchTerm, searchTerm, searchTerm], pageNum, pageSize);
    
    // 解析tags字段
    result.records = result.records.map(item => ({
      ...item,
      tags: item.tags ? (typeof item.tags === 'string' ? JSON.parse(item.tags) : item.tags) : []
    }));
    
    res.success(result);
  } catch (error) {
    console.error('搜索素材失败:', error);
    res.error('搜索素材失败');
  }
});

// 创建素材
router.post('/material', async (req, res) => {
  try {
    const { title, content, preview_url, category_id, tags } = req.body;

    if (!title || !content) {
      return res.error('标题和内容不能为空', 400);
    }

    // 根据路由路径确定表名
    const routePath = req.baseUrl;
    let tableName = 'text_materials';
    if (routePath.includes('/image')) {
      tableName = 'editor_materials';
    }
    
    const sql = `
      INSERT INTO ${tableName} (title, content, preview_url, category_id, tags, status, created_at, updated_at)
      VALUES (?, ?, ?, ?, ?, 1, NOW(), NOW())
    `;
    
    const tagsStr = tags ? (typeof tags === 'object' ? JSON.stringify(tags) : tags) : null;
    const result = await query(sql, [title, content, preview_url, category_id, tagsStr]);
    
    res.success({ id: result.insertId }, '素材创建成功');
  } catch (error) {
    console.error('创建素材失败:', error);
    res.error('创建素材失败');
  }
});

// 更新素材
router.put('/material/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { title, content, preview_url, category_id, tags } = req.body;

    // 根据路由路径确定表名
    const routePath = req.baseUrl;
    let tableName = 'text_materials';
    if (routePath.includes('/image')) {
      tableName = 'editor_materials';
    }
    
    const sql = `
      UPDATE ${tableName}
      SET title = ?, content = ?, preview_url = ?, category_id = ?, tags = ?, updated_at = NOW()
      WHERE id = ? AND status = 1
    `;
    
    const tagsStr = tags ? (typeof tags === 'object' ? JSON.stringify(tags) : tags) : null;
    const result = await query(sql, [title, content, preview_url, category_id, tagsStr, id]);
    
    if (result.affectedRows === 0) {
      return res.error('素材不存在或更新失败', 404);
    }
    
    res.success(null, '素材更新成功');
  } catch (error) {
    console.error('更新素材失败:', error);
    res.error('更新素材失败');
  }
});

// 删除素材（软删除）
router.delete('/material/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // 根据路由路径确定表名
    const routePath = req.baseUrl;
    let tableName = 'text_materials';
    if (routePath.includes('/image')) {
      tableName = 'editor_materials';
    }
    
    const sql = `
      UPDATE ${tableName}
      SET status = 0, updated_at = NOW()
      WHERE id = ?
    `;
    
    const result = await query(sql, [id]);
    
    if (result.affectedRows === 0) {
      return res.error('素材不存在', 404);
    }
    
    res.success(null, '素材删除成功');
  } catch (error) {
    console.error('删除素材失败:', error);
    res.error('删除素材失败');
  }
});

// 测试数据库字符集
router.get('/test-charset', async (req, res) => {
  try {
    // 检查数据库字符集
    const charsetResult = await query('SHOW VARIABLES LIKE "character_set%"');
    const collationResult = await query('SHOW VARIABLES LIKE "collation%"');

    // 检查表字符集
    const tableCharsetResult = await query(`
      SELECT TABLE_SCHEMA, TABLE_NAME, TABLE_COLLATION
      FROM information_schema.TABLES
      WHERE TABLE_NAME = 'editor_materials'
    `);

    // 检查列字符集
    const columnCharsetResult = await query(`
      SELECT COLUMN_NAME, CHARACTER_SET_NAME, COLLATION_NAME
      FROM information_schema.COLUMNS
      WHERE TABLE_NAME = 'editor_materials'
      AND TABLE_SCHEMA = DATABASE()
      AND CHARACTER_SET_NAME IS NOT NULL
    `);

    res.success({
      charset: charsetResult,
      collation: collationResult,
      tableCharset: tableCharsetResult,
      columnCharset: columnCharsetResult
    }, '字符集检查完成');
  } catch (error) {
    console.error('检查字符集失败:', error);
    res.error('检查字符集失败');
  }
});

// 测试中文插入
router.post('/test-chinese', async (req, res) => {
  try {
    const testTitle = '测试中文标题';
    const testDescription = '这是一个测试中文描述';

    console.log('准备插入的数据:');
    console.log('标题:', testTitle);
    console.log('描述:', testDescription);
    console.log('标题编码:', Buffer.from(testTitle, 'utf8'));
    console.log('描述编码:', Buffer.from(testDescription, 'utf8'));

    const insertSql = `
      INSERT INTO editor_materials (
        title, description, file_path, thumbnail_path,
        category_id, status, upload_user, created_at, updated_at
      )
      VALUES (?, ?, ?, ?, ?, 1, ?, NOW(), NOW())
    `;

    const result = await query(insertSql, [
      testTitle,
      testDescription,
      'https://example.com/test.jpg',
      'https://example.com/test_thumb.jpg',
      1, // 背景分类
      '1' // 管理员用户
    ]);

    // 立即查询插入的数据
    const selectSql = 'SELECT id, title, description FROM editor_materials WHERE id = ?';
    const selectResult = await query(selectSql, [result.insertId]);

    console.log('插入后查询的数据:', selectResult[0]);

    res.success({
      insertId: result.insertId,
      originalData: { title: testTitle, description: testDescription },
      savedData: selectResult[0]
    }, '中文测试完成');
  } catch (error) {
    console.error('中文测试失败:', error);
    res.error('中文测试失败: ' + error.message);
  }
});

// 测试FormData中文上传
router.post('/test-formdata-chinese', upload.single('file'), fixFormDataEncoding, async (req, res) => {
  try {
    const { title, description } = req.body;

    console.log('FormData测试接收到的数据:');
    console.log('标题:', title);
    console.log('描述:', description);
    console.log('标题类型:', typeof title);
    console.log('描述类型:', typeof description);
    console.log('标题编码:', Buffer.from(title || '', 'utf8'));
    console.log('描述编码:', Buffer.from(description || '', 'utf8'));

    // 测试插入数据库
    const insertSql = `
      INSERT INTO editor_materials (
        title, description, file_path, thumbnail_path,
        category_id, status, upload_user, created_at, updated_at
      )
      VALUES (?, ?, ?, ?, ?, 1, ?, NOW(), NOW())
    `;

    const result = await query(insertSql, [
      title,
      description,
      'https://example.com/formdata-test.jpg',
      'https://example.com/formdata-test_thumb.jpg',
      1, // 背景分类
      '1' // 管理员用户
    ]);

    // 立即查询插入的数据
    const selectSql = 'SELECT id, title, description FROM editor_materials WHERE id = ?';
    const selectResult = await query(selectSql, [result.insertId]);

    console.log('FormData插入后查询的数据:', selectResult[0]);

    res.success({
      insertId: result.insertId,
      originalData: { title, description },
      savedData: selectResult[0]
    }, 'FormData中文测试完成');
  } catch (error) {
    console.error('FormData中文测试失败:', error);
    res.error('FormData中文测试失败: ' + error.message);
  }
});

// JSON上传接口（解决中文编码问题）
router.post('/upload-json', async (req, res) => {
  try {
    const { title, description, file, filename, category_id } = req.body;

    console.log('JSON上传接收到的数据:');
    console.log('标题:', title);
    console.log('描述:', description);
    console.log('文件名:', filename);

    if (!title) {
      return res.error('标题不能为空', 400);
    }

    if (!file) {
      return res.error('文件不能为空', 400);
    }

    // 从base64转换为Buffer
    const fileBuffer = Buffer.from(file, 'base64');

    // 生成唯一文件名
    const timestamp = Date.now();
    const randomString = Math.random().toString(36).substring(2, 15);
    const fileExtension = filename ? filename.split('.').pop() : 'jpg';
    const key = `image-design/materials/${timestamp}_${randomString}.${fileExtension}`;

    console.log('生成的文件key:', key);

    // 上传到七牛云
    const uploadResult = await uploadToQiniu(fileBuffer, key);
    console.log('七牛云上传结果:', uploadResult);

    if (!uploadResult.url) {
      return res.error('文件上传失败', 500);
    }

    // 生成私有URL（使用key而不是url）
    const privateUrl = getPrivateFileUrl(key, 86400 * 365); // 1年有效期
    console.log('生成的私有URL:', privateUrl);

    // 保存到数据库
    const insertSql = `
      INSERT INTO editor_materials (
        title, description, file_path, thumbnail_path,
        category_id, width, height, format, file_size,
        status, upload_user, created_at, updated_at
      )
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 1, ?, NOW(), NOW())
    `;

    const result = await query(insertSql, [
      title,
      description || '',
      privateUrl,
      privateUrl, // 使用同一个URL作为缩略图
      category_id || 1,
      uploadResult.width || 0,
      uploadResult.height || 0,
      fileExtension.toUpperCase(),
      uploadResult.size || fileBuffer.length,
      req.user?.id || 1
    ]);

    res.success({
      id: result.insertId,
      url: privateUrl,
      title,
      description
    }, '素材上传成功');
  } catch (error) {
    console.error('JSON上传失败:', error);
    res.error('上传失败: ' + error.message);
  }
});

// 重置素材分类为一级分类
router.post('/update-categories', async (req, res) => {
  try {
    console.log('开始重置素材分类...');

    // 1. 清空现有分类数据
    await query('DELETE FROM editor_material_categories');
    console.log('已清空现有分类数据');

    // 2. 重置自增ID
    await query('ALTER TABLE editor_material_categories AUTO_INCREMENT = 1');
    console.log('已重置自增ID');

    // 3. 插入新的一级分类
    const categories = [
      { id: 1, name: '文字', icon: 'icon-text', sort_order: 1, description: '文字素材' },
      { id: 2, name: '特效', icon: 'icon-effect', sort_order: 2, description: '特效素材' },
      { id: 3, name: '插画', icon: 'icon-illustration', sort_order: 3, description: '插画素材' },
      { id: 4, name: '背景', icon: 'icon-background', sort_order: 4, description: '背景素材' },
      { id: 5, name: '老师', icon: 'icon-teacher', sort_order: 5, description: '老师素材' },
      { id: 6, name: '二维码', icon: 'icon-qrcode', sort_order: 6, description: '二维码素材' },
      { id: 7, name: '书籍', icon: 'icon-book', sort_order: 7, description: '书籍素材' },
      { id: 8, name: '其它', icon: 'icon-other', sort_order: 8, description: '其它素材' }
    ];

    for (const category of categories) {
      await query(`
        INSERT INTO editor_material_categories
        (id, name, parent_id, icon, sort_order, description, status, created_at, updated_at)
        VALUES (?, ?, NULL, ?, ?, ?, 1, NOW(), NOW())
      `, [category.id, category.name, category.icon, category.sort_order, category.description]);
    }

    console.log('已插入新的分类数据');

    // 4. 查询插入结果
    const result = await query('SELECT * FROM editor_material_categories ORDER BY sort_order');

    res.success({
      message: '分类重置成功',
      categories: result
    }, '素材分类已重置为一级分类');

  } catch (error) {
    console.error('重置分类失败:', error);
    res.error('重置分类失败: ' + error.message);
  }
});

// 创建分类
router.post('/categories', async (req, res) => {
  try {
    const { name, icon, sort_order = 0, description } = req.body;

    if (!name) {
      return res.error('分类名称不能为空', 400);
    }

    const insertSql = `
      INSERT INTO editor_material_categories
      (name, icon, sort_order, description, status, created_at, updated_at)
      VALUES (?, ?, ?, ?, 1, NOW(), NOW())
    `;

    const result = await query(insertSql, [name, icon || '', sort_order, description || '']);

    res.success({ id: result.insertId }, '分类创建成功');
  } catch (error) {
    console.error('创建分类失败:', error);
    res.error('创建分类失败: ' + error.message);
  }
});

// 更新分类
router.put('/categories/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { name, icon, sort_order, description } = req.body;

    if (!name) {
      return res.error('分类名称不能为空', 400);
    }

    const updateSql = `
      UPDATE editor_material_categories
      SET name = ?, icon = ?, sort_order = ?, description = ?, updated_at = NOW()
      WHERE id = ?
    `;

    await query(updateSql, [name, icon || '', sort_order || 0, description || '', id]);

    res.success(null, '分类更新成功');
  } catch (error) {
    console.error('更新分类失败:', error);
    res.error('更新分类失败: ' + error.message);
  }
});

// 删除分类
router.delete('/categories/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // 检查是否有素材使用此分类
    const materialCount = await query(
      'SELECT COUNT(*) as count FROM editor_materials WHERE category_id = ? AND status = 1',
      [id]
    );

    if (materialCount[0].count > 0) {
      return res.error('该分类下还有素材，无法删除', 400);
    }

    const deleteSql = 'DELETE FROM editor_material_categories WHERE id = ?';
    await query(deleteSql, [id]);

    res.success(null, '分类删除成功');
  } catch (error) {
    console.error('删除分类失败:', error);
    res.error('删除分类失败: ' + error.message);
  }
});

// 清空所有素材数据
router.post('/clear-all', async (req, res) => {
  try {
    console.log('开始清空所有素材数据...');

    // 清空素材表
    await query('DELETE FROM editor_materials');
    console.log('已清空 editor_materials 表');

    // 重置自增ID
    await query('ALTER TABLE editor_materials AUTO_INCREMENT = 1');
    console.log('已重置 editor_materials 自增ID');

    // 查询剩余数据数量
    const countResult = await query('SELECT COUNT(*) as count FROM editor_materials');
    const remainingCount = countResult[0]?.count || 0;

    res.success({
      message: '素材数据清空成功',
      remainingCount: remainingCount
    }, '所有素材数据已清空');

  } catch (error) {
    console.error('清空素材数据失败:', error);
    res.error('清空素材数据失败: ' + error.message);
  }
});

export default router;
