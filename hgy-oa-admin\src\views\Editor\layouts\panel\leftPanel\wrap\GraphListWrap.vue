<template>
    <div class="wrap">
<!--        <search-header :cateList="cateList" v-model="keyword" @changeCate="changeCate" @search="onSearch"/>-->
        <comp-cate-list-wrap :data="page.dataList" :cate-list="cateList" :current-cate="currentCate" :no-more="page.noMore"
                             @fetch-data="loadList"
                             @back-cate="backCate"
                             @item-click="handleClick"
                             @select-cate="selectCate"
        ></comp-cate-list-wrap>
    </div>
</template>

<script lang="ts" setup>

import {useEditor} from "@/views/Editor/app";
import {Image} from "leafer-ui";
import {getDefaultName} from "@/views/Editor/utils/utils";
import CompCateListWrap from "@/views/Editor/layouts/panel/leftPanel/wrap/CompCateListWrap.vue";
import usePageMixin from "@/views/Editor/layouts/panel/leftPanel/wrap/mixins/pageMixin";
import {queryGraphCategory, queryGraphList} from "@/api/editor/materials";
import SearchHeader from "@/components/editorModules/searchHeader.vue";
const {editor} = useEditor()

const keyword = ref();
const currentCate = ref(null);
const cateList = ref([])

const onSearch = (value,ev) => {
    console.log('value=',value)
    console.log('keyword=',keyword.value)
    console.log('ev=',ev)
}
const { page } = usePageMixin()
page.pageSize = 30
const fetchData = () => {
    queryGraphCategory().then(res =>{
        if (res.success) {
            const list = res.data.records

            // 为主分类加载素材预览
            queryGraphList({pageNum: 1, pageSize: 6, query: {categoryId: 13}}).then(graphRes => {
                if (graphRes.success) {
                    const mainCategory = {
                        id: 13,
                        name: '全部插画',
                        description: '显示所有插画素材',
                        sort_order: 0,
                        list: graphRes.data.records.map(item => ({
                            ...item,
                            url: item.preview_url || item.file_path
                        }))
                    }

                    // 为子分类初始化空的list
                    const subCategories = list.map(cat => ({
                        ...cat,
                        list: [] // 初始化为空，用户点击时再加载
                    }))

                    cateList.value = [mainCategory, ...subCategories]
                }
            })
        }
    })
}
const handleClick = (item) => {
    const image = new Image({
        name:getDefaultName(editor.contentFrame),
        editable: true,
        x:0,
        y:0,
        ...item,
    })
    editor.add(image)
}
const backCate = () => {
    currentCate.value = null
    page.dataList = []
}
const selectCate = (cate) => {
    currentCate.value = cate
    page.query.categoryId = cate.id
    page.pageNum = 1
    page.noMore = false
    page.dataList = [] // 清空之前的数据
    loadList()
}
const loadList = () => {
    page.query.categoryId = currentCate.value.id
    queryGraphList(page).then(res =>{
        if (res.success) {
            const newDataList = res.data.records
            if (newDataList.length > 0) {
                page.dataList.push(...newDataList)
                page.pageNum += 1
            }
            if (page.dataList.length >= res.data.total) {
                page.noMore = true
            } else {
                page.noMore = false
            }
        }
    })
}
// 接收active属性
const props = defineProps<{
  active: boolean
}>()

// 组件挂载时初始化数据
import { onMounted } from 'vue'
onMounted(() => {
  console.log('GraphListWrap 组件挂载，开始初始化...');
  fetchData()
})
</script>
