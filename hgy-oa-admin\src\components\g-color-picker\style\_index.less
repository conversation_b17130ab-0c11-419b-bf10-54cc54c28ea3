// 组件允许单个组件打包，因此默认引入公共基础样式
//
//@import "../../base.less";
//
@import "./_var.less";
//
//@import "./_mixin.less";
//
@import "./_reset.less";

// 公共前缀
@prefix: t;

.@{prefix}-color-picker__panel {
  padding: 0;
  width: var(--color-picker-panel-width);
  background: @bg-color-container;
  box-shadow: @color-picker-panel-shadow;
  border-radius: 6px;
  user-select: none;
}

.@{prefix}-popup__content {
  .@{prefix}-color-picker__panel {
    background: transparent;
    box-shadow: unset;
  }
}

.@{prefix}-color-picker {
  .reset;

  &.t-popup__content {
    box-shadow: @color-picker-panel-shadow;
  }

  &__icon {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: @color-picker-icon-font-size;
    background: transparent;
    transition: .2s linear;
    color: rgba(0, 0, 0, 60%);
    border-radius: @color-picker-icon-radius;
    cursor: pointer;

    &:hover {
      background: #f3f3f3;
      transition: .2s linear;
    }
    &.@{prefix}-is-disabled {
      color: rgba(0, 0, 0, 26%);
      pointer-events: none;
    }
  }
}

.@{prefix}-color-picker__head {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: @color-picker-panel-padding;
  position: relative;
  z-index: 2;

  .@{prefix}-color-picker__close {
    flex-shrink: 0;
  }

  & + .@{prefix}-color-picker__body {
    padding-top: 0;
  }
}

.@{prefix}-color-picker__body {
  padding: @color-picker-panel-padding;
  padding-bottom: calc(@color-picker-panel-padding - 4px);
}

.@{prefix}-color-picker__thumb {
  position: absolute;
  z-index: 1;
  outline: none;
  border-color: currentcolor;
  width: @color-picker-slider-thumb-size;
  height: @color-picker-slider-thumb-size;
  border-radius: 50%;
  box-shadow: @color-picker-thumbs-shadow;
  background: #fff;
  color: #0052d9;
  padding: @color-picker-slider-thumb-padding;
  box-sizing: border-box;

  &::before {
    content: "";
    width: 100%;
    height: 100%;
    background: currentcolor;
    border-radius: 50%;
    display: block;
  }
}

.@{prefix}-color-picker__saturation {
  height: @color-picker-saturation-height;
  border-radius: @color-picker-saturation-radius;
  position: relative;
  overflow: hidden;
  background: transparent;

  &::before,
  &::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }

  &::before {
    /* stylelint-disable-next-line color-no-hex */
    background: linear-gradient(90deg, #fff, transparent);
  }

  &::after {
    /* stylelint-disable-next-line color-no-hex */
    background: linear-gradient(0deg, #000, transparent);
  }

  .@{prefix}-color-picker__thumb {
    width: @color-picker-saturation-thumb-size;
    height: @color-picker-saturation-thumb-size;
    border-radius: 50%;
    transform: translate(-50%, -50%);
  }
}

.transparentBgImage () {
  /* stylelint-disable-next-line color-no-hex */
  background-image: linear-gradient(45deg, #c5c5c5 25%, transparent 0, transparent 75%, #c5c5c5 0, #c5c5c5), linear-gradient(45deg, #c5c5c5 25%, transparent 0, transparent 75%, #c5c5c5 0, #c5c5c5);
  background-size: 6px 6px;
  background-position: 0 0, 3px 3px;
}

.@{prefix}-color-picker__slider-wrapper {
  border-radius: @color-picker-slider-wrapper-radius;
  padding: @color-picker-slider-wrapper-padding;
  position: relative;

  &--hue-type {
    /* stylelint-disable-next-line color-named */
    background: red;
  }

  &--alpha-type {
    background: #fff;
    .transparentBgImage();
  }
}

.@{prefix}-color-picker__slider-padding {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: @color-picker-slider-height;
  border-radius: @color-picker-slider-wrapper-radius;
}

.@{prefix}-color-picker__slider {
  height: @color-picker-slider-height;
  position: relative;
  border-radius: @color-picker-slider-wrapper-radius;
  cursor: pointer;
  color: transparent;
  outline: none;
  z-index: 1;

  .@{prefix}-color-picker__thumb {
    transform: translate(@color-picker-slider-thumb-transform-x, -50%);
    top: 50%;
  }

  .@{prefix}-color-picker__rail {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    border-radius: inherit;
  }
}

.@{prefix}-color-picker__alpha,
.@{prefix}-color-picker__format {
  margin: @color-picker-margin 0 0 0;
}

.@{prefix}-color-picker__hue {
  /* stylelint-disable color-no-hex, color-named */
  background: linear-gradient(
    90deg,
    red,
    #ff0 17%,
    #0f0 33%,
    #0ff 50%,
    #00f 67%,
    #f0f 83%,
    red
  );
}

.@{prefix}-color-picker__alpha {
  .@{prefix}-color-picker__rail {
    background: linear-gradient(to right, transparent, currentcolor);
  }
}

.@{prefix}-color-picker__sliders-wrapper {
  display: flex;
  align-items: center;
  margin: @color-picker-margin 0;
}

.@{prefix}-color-picker__sliders {
  width: 100%;
}

.@{prefix}-color-picker__sliders-preview {
  flex-shrink: 0;
  margin-left: @color-picker-margin;
  width: @color-picker-gradient-preview-width;
  height: @color-picker-gradient-preview-height;
  border-radius: @color-picker-gradient-preview-radius;
  overflow: hidden;
  background: #fff;
  .transparentBgImage();

  &-inner {
    display: block;
    width: 100%;
    height: 100%;
  }
}

.@{prefix}-color-picker__gradient {
  padding: 0;
  display: flex;
  align-items: center;
  margin-bottom: @color-picker-margin;

  &-slider {
    flex: 1;
  }

  &-degree {
    position: relative;
    flex-shrink: 0;
    margin-left: @color-picker-margin;
    width: @color-picker-degree-width;
    .@{prefix}-input {
      margin: 0;
      font: @color-picker-font;
    }
    .@{prefix}-input-number {
      width: 100%;
      padding: 0;
    }
  }

  .gradient-thumbs {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    margin: 0;
    padding: 0;
    list-style: none;
    border-radius: inherit;
    cursor: pointer;

    &__item {
      top: 50%;
      left: 0;
      transform: translate(-50%, -50%);
      z-index: 0;
      cursor: pointer;

      &::before {
        display: none;
      }
      &.@{prefix}-is-active {
        z-index: 1;
        outline: 2px solid #ddd;
      }

      &-inner {
        display: block;
        width: 100%;
        height: 100%;
        border-radius: 50%;
        overflow: hidden;

        &::before {
          content: "";
          width: 100%;
          height: 100%;
          display: block;
          background-color: currentcolor;
        }
      }
    }
  }
}

.@{prefix}-color-picker__format {
  display: flex;
  align-items: center;
  justify-content: space-between;

  &--item {
    &:first-child {
      flex-shrink: 0;
    }

    &:last-child {
      flex: 1;
    }
    .@{prefix}-size-m, .@{prefix}-input.@{prefix}-size-m {
      font: @color-picker-font;
    }
    .@{prefix}-input-number .@{prefix}-input {
      margin: 0;
    }

    .input-group {
      display: flex;
      align-items: center;
      justify-content: space-around;
      margin-left: @color-picker-input-format-margin-left;
      .arco-input-wrapper,.arco-input{
        padding: 0;
        text-align: center;
      }
      &__item {
        flex: 1;
        width: 0;
        margin-left: -1px;
        .@{prefix}-input {
          padding: 0 1px;

          &:focus,
          &:focus-within,
          &:hover {
            z-index: 1;
          }
        }

        &:not(:first-child):not(:last-child) {
          .@{prefix}-input {
            border-radius: 0;
          }
        }

        &:first-child:not(:last-child) {
          .@{prefix}-input {
            border-top-right-radius: 0;
            border-bottom-right-radius: 0;
          }
        }

        &:last-child:not(:first-child) {
          .@{prefix}-input {
            border-top-left-radius: 0;
            border-bottom-left-radius: 0;
          }
        }
      }
      .@{prefix}-input-number {
        width: 100%;
        padding: 0;
      }
    }
  }

  &-mode-select {
    width: @color-picker-select-format-width;
    display: flex;

    .t-select,
    .t-input {
      padding-right: 0;
    }

    .t-input--prefix > .t-input__suffix {
      padding-left: 0;
    }

    .t-select__right-icon {
      margin-right: @color-picker-select-format-margin-right;
    }
  }
}

.@{prefix}-color-picker__swatches-wrap {
  margin-top: @color-picker-margin;
  position: relative;

  .@{prefix}-color-picker__swatches {

    + .@{prefix}-color-picker__swatches {
      margin-top: @color-picker-margin;
    }

    &--title {
      font: @color-picker-font;
      padding: 0;
      color: rgba(0, 0, 0, 90%);
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    &--actions {
      margin-left: auto;
      display: flex;
      align-items: center;
      font-size: 0;
      .@{prefix}-color-picker__icon {
        width: @color-picker-swatch-icon-size;
        height: @color-picker-swatch-icon-size;
        margin-left: @color-picker-swatch-action-margin-left;
      }
    }

    &--items {
      width: 100%;
      list-style: none;
      display: grid;
      grid-template-columns: repeat(@color-picker-swatch-columns, @color-picker-swatch-width);
      gap: @color-picker-swatch-row-gap @color-picker-swatch-column-gap;
      max-height: @color-picker-swatch-rows-max-height;
      overflow-x: hidden;
      overflow-y: auto;
      padding: @color-picker-swatch-item-padding;
      position: relative;
      left: @color-picker-swatch-item-left-negative-padding;
    }

    &--item {
      display: flex;
      width: @color-picker-swatch-width;
      height: @color-picker-swatch-height;
      border-radius: @color-picker-border-radius-default;
      padding: @color-picker-swatch-padding;
      overflow: hidden;
      cursor: pointer;
      align-items: center;
      justify-content: center;
      position: relative;
      transform-origin: center;
      transition: all .2s cubic-bezier(.38, 0, .24, 1);
      box-sizing: border-box;

      &:hover {
        transform: scale(1.25);
        transform-origin: center;
      }

      // 暂时先不删，待react同步后再删除
      &__color {
        width: 100%;
        height: 100%;
        position: relative;
        overflow: hidden;
        border: 1px solid #ddd;
        border-radius: @color-picker-border-radius-default;
        flex-shrink: 0;
      }

      // 暂时先不删，待react同步后再删除
      &__inner {
        width: 100%;
        height: 100%;
        display: block;
      }

      &:not(.@{prefix}-is-active):hover {
        padding: 0;
      }

      // 暂时先不删，待react同步后再删除
      &.@{prefix}-is-active .@{prefix}-color-picker__swatches--item__color,
      &.@{prefix}-is-active .@{prefix}-color-picker__swatches--color {
        border-color: #0052d9;
      }
    }

    &--color {
      width: 100%;
      height: 100%;
      position: relative;
      overflow: hidden;
      border-radius: @color-picker-border-radius-default;
      box-sizing: border-box;
    }

    &--inner {
      width: 100%;
      height: 100%;
      display: block;
      border-radius: @color-picker-border-radius-default;
      box-shadow: @colot-picker-swatch-item-color-inset-shadow;
    }
  }
}

.@{prefix}-color-picker__trigger {
  &--default {
    display: inline-flex;
    align-items: center;
    > .@{prefix}-input {
      width: fit-content;
    }
    .@{prefix}-input {
      padding: @color-picker-trigger-input-padding;
    }

    &__color {
      width: 100%;
      height: 100%;
      box-sizing: border-box;
      display: flex;
      align-items: center;

      .color-inner {
        border: 1px solid rgba(0, 0, 0, 10%);
        display: block;
        width: @color-picker-trigger-input-color-inner-size;
        height: @color-picker-trigger-input-color-inner-size;
        color: transparent;
        position: relative;
        border-radius: 2px;

        &.@{prefix}-size-s {
          width: @color-picker-trigger-input-color-inner-size-s;
          height: @color-picker-trigger-input-color-inner-size-s;
        }

        &.@{prefix}-size-l {
          width: @color-picker-trigger-input-color-inner-size-l;
          height: @color-picker-trigger-input-color-inner-size-l;
        }
      }
    }
  }
}

.@{prefix}-color-picker__gradient-slider {
  padding: @color-picker-slider-wrapper-padding;
  border-radius: @color-picker-slider-wrapper-radius;

  .@{prefix}-color-picker--bg-alpha {
    background: #fff;
    .transparentBgImage();
  }
}

.@{prefix}-color-picker__panel.@{prefix}-is-disabled {
  .@{prefix}-color-picker__saturation,
  .@{prefix}-color-picker__slider,
  .@{prefix}-color-picker__swatches--item {
    opacity: .8;
    cursor: not-allowed;
  }
  .@{prefix}-color-picker__gradient-slider {
    .gradient-thumbs,
    .gradient-thumbs__item {
      cursor: not-allowed;
    }
  }
  .@{prefix}-color-picker__swatches--item:hover {
    padding: @color-picker-swatch-padding;
  }
}

// 覆盖select-option 默认样式，使其更紧凑点
.@{prefix}-color-picker__select-options {
  > .@{prefix}-popup__content {
    box-shadow: @color-picker-panel-shadow;
  }
}
