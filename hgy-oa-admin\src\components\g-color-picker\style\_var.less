// 组件变量
// 名称可按如下规则定义：
// <component>-[type]-[attrtype]-<attr>-[status]

// component:组件名，如button，
// type: 组件类型，如 button 的次要按钮（line）
// attrtype: 属性的具体应用场景。如颜色，用于背景（bg）、文本（text）、边框（border）等
// attr: 属性名称，如color、height、radius等
// status: 表示组件状态或尺寸，如 hover、disabled、s、l 等

// 如：@button-line-bg-color-hover
// 如：@button-line-height-s

@color-picker-panel-width: 266px;
:root{
  --color-picker-panel-width: @color-picker-panel-width;
}
@color-picker-margin-xxxs: 16px;
@color-picker-margin-s: 16px;
@color-picker-size-1: 2px;
@color-picker-border-radius-default: 3px;
@color-picker-panel-padding: 12px;
@color-picker-panel-radius: 6px;
@color-picker-icon-radius: @color-picker-border-radius-default;
@color-picker-saturation-height: 160px;
@color-picker-saturation-radius: @color-picker-border-radius-default;
@color-picker-saturation-thumb-size: calc(@color-picker-margin-xxxs + @color-picker-size-1);
@color-picker-margin: 12px;
@color-picker-slider-height: 8px;
@color-picker-slider-wrapper-radius: calc(@color-picker-slider-height / 2);
@color-picker-slider-wrapper-padding: 0 calc(@color-picker-saturation-thumb-size / 2);
@color-picker-slider-thumb-size: calc(@color-picker-margin-xxxs + @color-picker-size-1);
@color-picker-slider-thumb-transform-x: calc(@color-picker-slider-thumb-size * -.5);
@color-picker-slider-thumb-padding: 2px;
@color-picker-input-format-margin-left: 8px;
@color-picker-select-format-margin-right: 4px;
@color-picker-select-format-width: 72px;
@color-picker-font: 12px;
@color-picker-icon-radius: @color-picker-border-radius-default;
@color-picker-icon-font-size: @color-picker-swatch-icon-size;
@color-picker-degree-width: 66px;
@color-picker-gradient-preview-width: 28px;
@color-picker-gradient-preview-height: 28px;
@color-picker-gradient-preview-radius: @color-picker-border-radius-default;
@color-picker-swatch-icon-size: @color-picker-margin-xxxs;
@color-picker-swatch-action-margin-left: 8px;
@color-picker-swatch-width: @color-picker-margin-xxxs;
@color-picker-swatch-height: @color-picker-margin-xxxs;
@color-picker-swatch-radius: 50%;
@color-picker-swatch-padding: 0;
@color-picker-swatch-border-size: 1px;
@color-picker-swatch-columns: 10;
@color-picker-swatch-item-padding: calc(@color-picker-panel-padding - 8px); //make sure swatchItem scale
@color-picker-swatch-item-left-negative-padding: calc(0px - @color-picker-swatch-item-padding);
// columnGap = (panelWidth - (panelPadding * 2) + (swatchColor * columnsItem)) / 9
@color-picker-swatch-row-gap: calc(calc(var(--color-picker-panel-width) - calc(calc(@color-picker-panel-padding * 2) + calc(@color-picker-swatch-height * @color-picker-swatch-columns))) / 9);
@color-picker-swatch-column-gap: calc(calc(var(--color-picker-panel-width) - calc(calc(@color-picker-swatch-width * 2) + calc(@color-picker-swatch-width * @color-picker-swatch-columns))) / 9);
@color-picker-swatch-wrap-margin-left: (@color-picker-swatch-padding + @color-picker-swatch-border-size) * -1;
// 右边往外伸出一点，让滚动条居外侧
@color-picker-swatch-wrap-margin-right: @color-picker-swatch-wrap-margin-left -
  10px;
@color-picker-swatch-wrap-margin: 0 @color-picker-swatch-wrap-margin-right 0
  @color-picker-swatch-wrap-margin-left;
@color-picker-swatch-max-rows: 4; // 最大显示行数

// shadow
@color-picker-panel-shadow: 0 3px 14px 2px rgba(0, 0, 0, 5%), 0 8px 10px 1px rgba(0, 0, 0, 6%), 0 5px 5px -3px rgba(0, 0, 0, 10%),inset 0 .5px 0 #dcdcdc,inset .5px 0 0 #dcdcdc,inset 0 -.5px 0 #dcdcdc,inset -.5px 0 0 #dcdcdc;
@color-picker-thumbs-shadow: 0 1px 10px rgba(0, 0, 0, 5%), 0 4px 5px rgba(0, 0, 0, 8%), 0 2px 4px -1px rgba(0, 0, 0, 12%);
@colot-picker-swatch-item-color-inset-shadow: inset 0 .5px 0 #dcdcdc,inset .5px 0 0 #dcdcdc,inset 0 -.5px 0 #dcdcdc,inset -.5px 0 0 #dcdcdc;;

// 最大高度 = 行数 * (itemHeight + itemMarginTop)m;
@color-picker-swatch-rows-max-height: calc(@color-picker-swatch-max-rows *
calc(@color-picker-swatch-height + @color-picker-swatch-row-gap));

// trigger-input
@color-picker-trigger-input-padding: 0 8px 0 4px;
@color-picker-trigger-input-color-inner-size: 22px;
@color-picker-trigger-input-color-inner-size-s: 18px;
@color-picker-trigger-input-color-inner-size-l: 26px;
@bg-color-container: #fff;
