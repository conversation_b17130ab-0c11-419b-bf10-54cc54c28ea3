# API配置使用说明

## 概述

为了避免硬编码API地址，项目提供了统一的API配置管理。所有API调用都应该使用这个配置文件。

## 使用方法

### 1. 基础用法

```typescript
import { getApiBaseUrl, getApiUrl, getEndpointUrl } from './api'

// 获取API基础地址
const baseUrl = getApiBaseUrl() // http://localhost:3001 或 https://image-api.dlmu.cc

// 获取完整API地址
const loginUrl = getApiUrl('/api/auth/login')

// 使用预定义的端点
const loginUrl2 = getEndpointUrl('LOGIN')
```

### 2. 在组件中使用

```vue
<script setup lang="ts">
const handleLogin = async () => {
  try {
    // 动态导入API配置
    const { getEndpointUrl } = await import('../config/api')
    
    const response = await fetch(getEndpointUrl('LOGIN'), {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(loginData)
    })
    
    const result = await response.json()
    // 处理响应...
  } catch (error) {
    console.error('登录失败:', error)
  }
}
</script>
```

### 3. 在API服务中使用

```typescript
import { getEndpointUrl } from '@/config/api'
import request from '@/utils/request'

export const loginApi = (data: LoginData) => {
  return request.post(getEndpointUrl('LOGIN'), data)
}

export const getUserList = (params: any) => {
  return request.get(getEndpointUrl('USER_LIST'), { params })
}
```

## 预定义的API端点

| 端点名称 | 路径 | 说明 |
|---------|------|------|
| LOGIN | /api/auth/login | 用户登录 |
| LOGOUT | /api/auth/logout | 用户登出 |
| USER_LIST | /api/user/list | 用户列表 |
| USER_INFO | /api/user/info | 用户信息 |
| TEMPLATE_LIST | /api/template/templateList | 模板列表 |
| TEMPLATE_SAVE | /api/template/save | 保存模板 |
| TEMPLATE_USER_LIST | /api/template/user/list | 用户模板列表 |
| FONT_LIST | /api/font/list | 字体列表 |
| MATERIAL_CATEGORIES | /api/material/categories | 素材分类 |
| MATERIAL_LIST | /api/material/list | 素材列表 |
| DEPARTMENT_LIST | /api/department/list | 部门列表 |
| UPLOAD | /api/oss/upload | 文件上传 |

## 环境配置

### 前端环境变量

```bash
# .env.local
VITE_API_BASE_URL=http://localhost:3001

# .env.test  
VITE_API_BASE_URL=https://image-api.dlmu.cc
```

### 后端环境变量

```bash
# .env.local
API_BASE_URL=http://localhost:3001

# .env.test
API_BASE_URL=https://image-api.dlmu.cc
```

## 注意事项

1. **不要硬编码API地址**: 始终使用配置文件中的方法获取API地址
2. **动态导入**: 在Vue组件中使用动态导入避免TypeScript类型问题
3. **环境变量**: 确保在不同环境中设置正确的VITE_API_BASE_URL
4. **新增端点**: 添加新的API端点时，请更新API_ENDPOINTS对象

## 迁移指南

如果你的代码中有硬编码的API地址，请按以下方式修改：

### 修改前
```javascript
const response = await fetch('http://localhost:3001/api/auth/login', {
  method: 'POST',
  // ...
})
```

### 修改后
```javascript
const { getEndpointUrl } = await import('../config/api')
const response = await fetch(getEndpointUrl('LOGIN'), {
  method: 'POST',
  // ...
})
```
