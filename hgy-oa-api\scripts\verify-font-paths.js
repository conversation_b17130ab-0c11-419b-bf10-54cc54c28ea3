import { query } from '../src/config/database.js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const verifyFontPaths = async () => {
  try {
    console.log('🔍 开始验证字体文件路径...\n');

    // 获取实际的字体文件列表
    const fontsDir = path.join(__dirname, '../../hgy-oa-admin/public/fonts');
    const actualFiles = fs.readdirSync(fontsDir).filter(file => file.endsWith('.ttf'));
    
    console.log(`📁 实际字体文件 (${actualFiles.length} 个):`);
    actualFiles.forEach(file => console.log(`   ${file}`));

    // 获取数据库中的字体记录
    const dbFonts = await query('SELECT id, name, code, download_url FROM fonts WHERE is_system = 0 ORDER BY name');
    
    console.log(`\n💾 数据库字体记录 (${dbFonts.length} 个):`);
    
    let matchCount = 0;
    let mismatchCount = 0;
    const mismatches = [];

    for (const font of dbFonts) {
      const fileName = font.download_url ? font.download_url.replace('/fonts/', '') : '';
      const exists = actualFiles.includes(fileName);
      
      if (exists) {
        console.log(`   ✅ ${font.name} -> ${fileName}`);
        matchCount++;
      } else {
        console.log(`   ❌ ${font.name} -> ${fileName} (文件不存在)`);
        mismatchCount++;
        mismatches.push({ font, fileName, actualFiles });
      }
    }

    console.log(`\n📊 验证结果:`);
    console.log(`   匹配: ${matchCount} 个`);
    console.log(`   不匹配: ${mismatchCount} 个`);

    if (mismatchCount > 0) {
      console.log(`\n🔧 尝试修复不匹配的路径:`);
      
      for (const { font } of mismatches) {
        // 尝试根据字体名称或代码找到对应的文件
        let matchedFile = null;
        
        // 精确匹配策略
        const searchTerms = [
          font.code,
          font.name,
          font.name.replace(/\s+/g, ''),
          font.name.replace(/黑体|宋体|楷体|仿宋|书宋/g, ''),
        ];

        for (const term of searchTerms) {
          const found = actualFiles.find(file => 
            file.toLowerCase().includes(term.toLowerCase()) ||
            term.toLowerCase().includes(file.toLowerCase().replace('.ttf', ''))
          );
          if (found) {
            matchedFile = found;
            break;
          }
        }

        // 特殊匹配规则
        if (!matchedFile) {
          const specialMatches = {
            'fangzhengshusong': 'fangzhengshusong.ttf',
            'fangzhengfangsong': 'fangzhengfangsong.ttf', 
            'fangzhengheiti': 'fangzhengheiti.ttf',
            'aa_houdihei': 'Aa厚底黑.ttf',
            'alimama_dongfangdakai': 'Alimama_DongFangDaKai_Regular.ttf',
            'dingtalk_jinbuti': 'DingTalk JinBuTi.ttf',
            'douyin_sans_bold': 'DouyinSansBold.ttf',
            'youshe_biaotiyuan': '优设标题圆.ttf',
            'youshe_biaotihei': '优设标题黑.ttf',
            'swisski': 'swisski.ttf',
            'swis721_black': 'Swis721 Blk BT Black.ttf'
          };
          
          matchedFile = specialMatches[font.code];
        }

        if (matchedFile && actualFiles.includes(matchedFile)) {
          const newPath = `/fonts/${matchedFile}`;
          await query('UPDATE fonts SET download_url = ? WHERE id = ?', [newPath, font.id]);
          console.log(`   🔧 修复: ${font.name} -> ${matchedFile}`);
        } else {
          console.log(`   ⚠️  无法找到匹配文件: ${font.name} (${font.code})`);
          console.log(`      可能的候选文件:`);
          actualFiles.slice(0, 5).forEach(file => console.log(`        - ${file}`));
        }
      }
    }

    console.log('\n✅ 字体路径验证完成！');

  } catch (error) {
    console.error('❌ 验证过程中发生错误:', error);
  }
};

// 执行验证
verifyFontPaths().then(() => {
  console.log('程序执行完成');
  process.exit(0);
}).catch(error => {
  console.error('程序执行失败:', error);
  process.exit(1);
});
