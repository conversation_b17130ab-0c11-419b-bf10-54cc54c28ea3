<template>
  <div class="role-manage">
    <div class="page-header">
      <h1>角色管理</h1>
      <div class="header-actions">
        <a-button type="outline" @click="showNavPermissionModal">
          <template #icon>
            <icon-menu />
          </template>
          导航权限配置
        </a-button>
        <a-button type="primary" @click="showCreateModal">
          <template #icon>
            <icon-plus />
          </template>
          新增角色
        </a-button>
      </div>
    </div>

    <a-card>
      <a-table
        :columns="columns"
        :data="roleData"
        :pagination="pagination"
        :loading="loading"
      >
        <template #navPermissions="{ record }">
          <a-space wrap>
            <a-tag
              v-for="nav in record.allowedNavs"
              :key="nav.key"
              size="small"
              color="blue"
            >
              {{ nav.name }}
            </a-tag>
            <a-tag v-if="record.allowedNavs.length === 0" color="gray">
              无导航权限
            </a-tag>
          </a-space>
        </template>

        <template #createTime="{ record }">
          {{ formatDate(record.createTime) }}
        </template>

        <template #actions="{ record }">
          <a-space size="mini">
            <a-button type="text" size="small" @click="editRole(record)">编辑</a-button>
            <a-button type="text" size="small" @click="configNavPermission(record)">导航权限</a-button>
            <a-button type="text" size="small" status="danger" @click="deleteRole(record)">删除</a-button>
          </a-space>
        </template>
      </a-table>
    </a-card>

    <!-- 新增/编辑角色弹窗 -->
    <a-modal
      v-model:visible="roleModalVisible"
      :title="isEdit ? '编辑角色' : '新增角色'"
      width="600px"
      @ok="handleRoleSubmit"
      @cancel="handleRoleCancel"
    >
      <a-form :model="roleForm" :rules="roleRules" ref="roleFormRef" layout="vertical">
        <a-form-item label="角色名称" field="name">
          <a-input v-model="roleForm.name" placeholder="请输入角色名称" />
        </a-form-item>

        <a-form-item label="角色描述" field="description">
          <a-textarea v-model="roleForm.description" placeholder="请输入角色描述" :rows="3" />
        </a-form-item>


      </a-form>
    </a-modal>

    <!-- 导航权限配置弹窗 -->
    <a-modal
      v-model:visible="navPermissionModalVisible"
      title="导航权限配置"
      width="500px"
      @ok="handleNavPermissionSubmit"
      @cancel="handleNavPermissionCancel"
    >
      <div class="nav-permission-config">
        <div class="nav-permission-header">
          <p>为角色 <strong>"{{ currentRole?.name }}"</strong> 配置顶部导航权限</p>
        </div>

        <div class="nav-permission-content">
          <a-checkbox-group v-model="navPermissionForm.allowedNavs">
            <div class="nav-simple-list">
              <div class="nav-simple-item" v-for="nav in availableNavs" :key="nav.key">
                <a-checkbox :value="nav.key">
                  <div class="nav-simple-content">
                    <component :is="nav.icon" class="nav-simple-icon" />
                    <span class="nav-simple-name">{{ nav.name }}</span>
                  </div>
                </a-checkbox>
              </div>
            </div>
          </a-checkbox-group>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { Message, Modal } from '@arco-design/web-vue'
import {
  IconPlus,
  IconMenu,
  IconHome,
  IconEdit,
  IconDesktop,
  IconSettings
} from '@arco-design/web-vue/es/icon'
import {
  getRoleList,
  createRole,
  updateRole,
  deleteRole as deleteRoleAPI,
  getAvailableNavs,
  updateRoleNavPermissions,
  type Role
} from '@/api/role'

const loading = ref(false)

// 格式化日期函数
const formatDate = (dateStr: string) => {
  if (!dateStr) return '-'
  const date = new Date(dateStr)
  return date.toISOString().split('T')[0] // 返回 YYYY-MM-DD 格式
}

// 可用的顶部导航菜单
const availableNavs = ref([
  {
    key: 'index',
    name: '图片演示站',
    path: '/index',
    icon: IconHome,
    description: '图片库首页，浏览和搜索图片'
  },
  {
    key: 'editor',
    name: '图片制作站',
    path: '/editor',
    icon: IconEdit,
    description: '在线设计编辑器'
  },
  {
    key: 'design',
    name: '设计工作表',
    path: '/design',
    icon: IconDesktop,
    description: '设计团队任务管理'
  },
  {
    key: 'product',
    name: '项目开发表',
    path: '/product',
    icon: IconDesktop,
    description: '项目管理与进度跟踪'
  },
  {
    key: 'admin',
    name: '管理后台',
    path: '/admin',
    icon: IconSettings,
    description: '系统管理和配置'
  }
])

// 角色数据
const roleData = ref([
  {
    id: 1,
    name: '超级管理员',
    description: '拥有所有权限',
    allowedNavs: [
      { key: 'index', name: '图片演示站' },
      { key: 'editor', name: '图片制作站' },
      { key: 'design', name: '设计工作表' },
      { key: 'product', name: '项目开发表' },
      { key: 'admin', name: '管理后台' }
    ],
    userCount: 1,
    createTime: '2025-01-01 10:00:00'
  },
  {
    id: 2,
    name: '设计师',
    description: '设计团队成员',
    allowedNavs: [
      { key: 'index', name: '图片演示站' },
      { key: 'editor', name: '图片制作站' },
      { key: 'design', name: '设计工作表' }
    ],
    userCount: 8,
    createTime: '2025-01-15 14:30:00'
  },
  {
    id: 3,
    name: '普通用户',
    description: '基础使用权限',
    allowedNavs: [
      { key: 'index', name: '图片演示站' },
      { key: 'editor', name: '图片制作站' }
    ],
    userCount: 156,
    createTime: '2025-01-01 10:00:00'
  }
])

const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 3
})

const columns = [
  { title: '角色名称', dataIndex: 'name', width: 120 },
  { title: '描述', dataIndex: 'description', width: 180 },
  { title: '导航权限', slotName: 'navPermissions', width: 250 },
  { title: '用户数', dataIndex: 'userCount', width: 80 },
  { title: '创建时间', slotName: 'createTime', width: 160 },
  { title: '操作', slotName: 'actions', width: 180, align: 'center' }
]

// 弹窗状态
const roleModalVisible = ref(false)
const navPermissionModalVisible = ref(false)
const isEdit = ref(false)
const currentRole = ref(null)

// 表单数据
const roleForm = reactive({
  name: '',
  description: '',
  allowedNavs: []
})

const navPermissionForm = reactive({
  allowedNavs: []
})

// 表单验证规则
const roleRules = {
  name: [
    { required: true, message: '请输入角色名称' },
    { minLength: 2, message: '角色名称至少2个字符' }
  ],
  description: [
    { required: true, message: '请输入角色描述' }
  ]
}

const roleFormRef = ref()

// 显示新增角色弹窗
const showCreateModal = () => {
  isEdit.value = false
  resetRoleForm()
  roleModalVisible.value = true
}

// 编辑角色
const editRole = (record: any) => {
  isEdit.value = true
  currentRole.value = record
  roleForm.name = record.name
  roleForm.description = record.description
  roleForm.allowedNavs = record.allowedNavs.map((nav: any) => nav.key)
  roleModalVisible.value = true
}

// 配置导航权限
const configNavPermission = (record: any) => {
  currentRole.value = record
  navPermissionForm.allowedNavs = record.allowedNavs.map((nav: any) => nav.key)
  navPermissionModalVisible.value = true
}

// 显示导航权限配置弹窗
const showNavPermissionModal = () => {
  Message.info('请选择具体角色进行导航权限配置')
}

// 删除角色
const deleteRole = (record: any) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除角色"${record.name}"吗？此操作不可恢复。`,
    onOk: async () => {
      try {
        await deleteRoleAPI(record.id)
        Message.success('删除角色成功')
        fetchRoleList()
      } catch (error) {
        console.error('删除角色失败:', error)
        Message.error('删除角色失败')
      }
    }
  })
}

// 重置表单
const resetRoleForm = () => {
  roleForm.name = ''
  roleForm.description = ''
  roleForm.allowedNavs = []
}

// 角色表单提交
const handleRoleSubmit = async () => {
  try {
    const valid = await roleFormRef.value?.validate()
    if (!valid) return

    const submitData = {
      name: roleForm.name,
      description: roleForm.description,
      allowedNavs: roleForm.allowedNavs
    }

    if (isEdit.value) {
      // 编辑角色
      await updateRole(currentRole.value.id, submitData)
      Message.success('编辑角色成功')
    } else {
      // 新增角色
      await createRole(submitData)
      Message.success('新增角色成功')
    }

    roleModalVisible.value = false
    fetchRoleList()
  } catch (error) {
    console.error('保存角色失败:', error)
    Message.error('保存角色失败')
  }
}

// 角色表单取消
const handleRoleCancel = () => {
  roleModalVisible.value = false
}

// 导航权限表单提交
const handleNavPermissionSubmit = () => {
  const allowedNavsData = navPermissionForm.allowedNavs.map(key => {
    const nav = availableNavs.value.find(n => n.key === key)
    return { key, name: nav?.name || key }
  })

  const index = roleData.value.findIndex(item => item.id === currentRole.value.id)
  if (index > -1) {
    roleData.value[index].allowedNavs = allowedNavsData
    Message.success('导航权限配置成功')
  }

  navPermissionModalVisible.value = false
}

// 导航权限表单取消
const handleNavPermissionCancel = () => {
  navPermissionModalVisible.value = false
}

// 获取角色列表
const fetchRoleList = async () => {
  try {
    loading.value = true
    const response = await getRoleList({
      pageNum: pagination.current,
      pageSize: pagination.pageSize
    })

    if (response.success) {
      roleData.value = response.data.list || []
      pagination.total = response.data.total || 0
    }
  } catch (error) {
    console.error('获取角色列表失败:', error)
    Message.error('获取角色列表失败')
  } finally {
    loading.value = false
  }
}

// 获取可用导航菜单
const fetchAvailableNavs = async () => {
  try {
    const response = await getAvailableNavs()
    if (response.success) {
      availableNavs.value = response.data || []
    }
  } catch (error) {
    console.error('获取导航菜单失败:', error)
  }
}

// 页面加载时获取数据
onMounted(() => {
  fetchRoleList()
  fetchAvailableNavs()
})
</script>

<style scoped>
.role-manage {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0;
  color: #262626;
}

.header-actions {
  display: flex;
  gap: 12px;
}

/* 导航权限配置样式 */
.nav-permission-grid {
  max-height: 300px;
  overflow-y: auto;
}

.nav-item-checkbox {
  margin-bottom: 16px;
  padding: 12px;
  border: 1px solid #e5e6eb;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.nav-item-checkbox:hover {
  border-color: #165dff;
  background-color: #f2f3ff;
}

.nav-checkbox-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.nav-checkbox-icon {
  font-size: 20px;
  color: #165dff;
}

.nav-checkbox-info {
  flex: 1;
}

.nav-checkbox-name {
  font-weight: 500;
  color: #1d2129;
  margin-bottom: 4px;
}

.nav-checkbox-desc {
  font-size: 12px;
  color: #86909c;
}

/* 导航权限配置弹窗样式 */
.nav-permission-config {
  padding: 8px 0;
}

.nav-permission-header {
  margin-bottom: 20px;
}

.nav-permission-header p {
  margin: 0;
  color: #86909c;
  font-size: 14px;
  text-align: center;
}

.nav-permission-content {
  max-height: 300px;
  overflow-y: auto;
}

.nav-simple-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.nav-simple-item {
  padding: 12px 16px;
  border: 1px solid #e5e6eb;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.nav-simple-item:hover {
  border-color: #165dff;
  background-color: #f2f3ff;
}

.nav-simple-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.nav-simple-icon {
  font-size: 18px;
  color: #165dff;
}

.nav-simple-name {
  font-weight: 500;
  color: #1d2129;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-actions {
    flex-direction: column;
    gap: 8px;
  }

  .nav-simple-content {
    gap: 8px;
  }

  .nav-simple-icon {
    font-size: 16px;
  }
}
</style>
