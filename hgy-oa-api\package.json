{"name": "image-design-backend", "version": "1.0.0", "description": "Backend API for image design editor", "main": "src/app.js", "type": "module", "engines": {"node": ">=22.12.0", "pnpm": ">=8.0.0"}, "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "dev:local": "node -r dotenv/config src/app.js dotenv_config_path=.env.local", "start:test": "node -r dotenv/config src/app.js dotenv_config_path=.env.test", "setup": "node scripts/setup.js", "migrate": "node scripts/migrate-data.js", "build": "echo 'No build step required'", "test": "echo 'No tests specified'"}, "keywords": ["image-design", "api", "express", "mysql"], "author": "", "license": "MIT", "dependencies": {"compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "form-data": "^4.0.3", "fs-extra": "^11.2.0", "helmet": "^7.1.0", "multer": "^2.0.1", "mysql2": "^3.6.5", "node-fetch": "^3.3.2", "path": "^0.12.7", "qiniu": "^7.14.0", "uuid": "^9.0.1", "xlsx": "^0.18.5"}, "devDependencies": {"nodemon": "^3.0.2"}}