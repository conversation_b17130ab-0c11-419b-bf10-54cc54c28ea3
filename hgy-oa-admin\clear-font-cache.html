<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>清除字体缓存</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .success {
            color: #28a745;
            margin: 10px 0;
        }
        .info {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>清除字体缓存</h1>
        
        <div class="info">
            <strong>说明：</strong>
            <p>我们已经更新了字体配置，将所有在线字体替换为本地字体。为了让编辑器显示最新的字体列表，需要清除浏览器中的字体缓存。</p>
        </div>

        <button class="btn" onclick="clearFontCache()">清除字体缓存</button>
        <button class="btn" onclick="reloadPage()">重新加载页面</button>
        
        <div id="result"></div>

        <div class="info">
            <strong>操作步骤：</strong>
            <ol>
                <li>点击"清除字体缓存"按钮</li>
                <li>点击"重新加载页面"按钮</li>
                <li>访问编辑器页面：<a href="http://localhost:5173/#/editor" target="_blank">http://localhost:5173/#/editor</a></li>
                <li>检查字体选择器中是否显示了新的本地字体</li>
            </ol>
        </div>
    </div>

    <script>
        function clearFontCache() {
            try {
                // 清除字体相关的localStorage
                localStorage.removeItem('OPEN_FONTS');
                localStorage.removeItem('OPEN_FONTS_VERSION');
                
                // 清除其他可能的字体缓存
                const keys = Object.keys(localStorage);
                keys.forEach(key => {
                    if (key.toLowerCase().includes('font')) {
                        localStorage.removeItem(key);
                    }
                });

                document.getElementById('result').innerHTML = 
                    '<div class="success">✅ 字体缓存已清除！</div>';
                
                console.log('字体缓存已清除');
            } catch (error) {
                document.getElementById('result').innerHTML = 
                    '<div style="color: red;">❌ 清除缓存失败: ' + error.message + '</div>';
                console.error('清除缓存失败:', error);
            }
        }

        function reloadPage() {
            window.location.reload();
        }

        // 页面加载时显示当前缓存状态
        window.onload = function() {
            const fontCache = localStorage.getItem('OPEN_FONTS');
            const fontVersion = localStorage.getItem('OPEN_FONTS_VERSION');
            
            let status = '<div class="info"><strong>当前缓存状态：</strong><br>';
            status += '字体版本: ' + (fontVersion || '无') + '<br>';
            status += '缓存字体数量: ' + (fontCache ? JSON.parse(fontCache).length : 0) + '</div>';
            
            document.getElementById('result').innerHTML = status;
        };
    </script>
</body>
</html>
