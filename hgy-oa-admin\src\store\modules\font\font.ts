import {getFonts} from "@/api/editor/font";
import {Message,Notification} from "@arco-design/web-vue";
import FontFaceObserver from 'fontfaceobserver'

const defaultFonts = [
    // {
    //     code: 'arial',
    //     name: '<PERSON><PERSON>',
    // },
    // {
    //     code: 'Times New Roman',
    //     name: 'Times New Roman',
    // },
    // {
    //     code: 'Microsoft Yahei',
    //     name: '微软雅黑',
    // },
]

interface Node {
    tag?: string;
    fill?: Array<{ type?: string; color?: string }>;
    fontFamily?: string;
    text?: string;
    children?: Node[];
}
interface TemplateFontFamily{
    styleFonts: Set<string>;
    richTextFonts: Set<string>;
}

const FONT_KEY = 'OPEN_FONTS'
const FONT_VERSION_KEY = 'OPEN_FONTS_VERSION'
export const useFontStore = defineStore('font', () => {
    const fontList = ref<any>([])

    // 跳过加载的字体
    const skipLoadFonts = ref<any>(defaultFonts.map(value => value.name))


    /**
     * 初始化部分字体
     */
    async function initFonts() {
        let list = []
        // 强制清除缓存，重新从API获取字体列表
        localStorage.getItem(FONT_VERSION_KEY) !== '4' && localStorage.removeItem(FONT_KEY)
        const localFonts: any = localStorage.getItem(FONT_KEY) ? JSON.parse(localStorage.getItem(FONT_KEY) || '') : []
        if (localFonts.length > 0) {
            list.push(...localFonts)
        }

        if (list.length === 0) {
            const res = await getFonts({pageNum: 1, pageSize: 1000})
            // 映射API字段到前端期望的字段格式
            list = res.data.records.map((font: any) => ({
                ...font,
                download: font.download_url, // 映射 download_url 到 download
                fontFamily: font.font_family // 映射 font_family 到 fontFamily
            }))
            localStorage.setItem(FONT_KEY, JSON.stringify(list))
            localStorage.setItem(FONT_VERSION_KEY, '4')
        }
        fontList.value = defaultFonts.concat(list)
        return list
    }

    /**
     * 提取模板中所用的字体
     * @param jsonData 模板json
     */
    async function extractTemplateFonts(jsonData: any, load = false): Promise<TemplateFontFamily> {
        const styleFonts = new Set<string>();
        const richTextFonts = new Set<string>();
        const stack: Node[] = [jsonData];

        const regex = /font-family:\s*['"]?([^'";,]+)/g;

        while (stack.length > 0) {
            const node = stack.pop()!;
            if ('Text' === node.tag) {
                if (node.fontFamily) {
                    styleFonts.add(node.fontFamily);
                }
            } else if ('HTMLText' === node.tag) {
                if (node.text && typeof node.text === 'string') {
                    let match;
                    while ((match = regex.exec(node.text)) !== null) {
                        if (match[1]) {
                            richTextFonts.add(match[1].trim());
                        }
                    }
                }
            }
            if (node.children) {
                for (const child of node.children) {
                    stack.push(child);
                }
            }
        }
        const fonts = {styleFonts: styleFonts, richTextFonts: richTextFonts}
        if (load) {
            await loadFonts(fonts)
        }
        return fonts;
    }

    async function loadFonts(fonts: TemplateFontFamily, maxConcurrent = 3) {
        const allFonts = [...fonts.styleFonts, ...fonts.richTextFonts];

        // 使用新的字体加载器
        const { fontLoader } = await import('@/utils/fontLoader');

        const result = await fontLoader.loadFonts(allFonts, {
            maxConcurrent,
            timeout: 15000,
            showProgress: true
        });

        // 记录加载结果
        console.log('字体加载结果:', result);

        return result;
    }

    return {
        fontList,
        skipLoadFonts,
        initFonts,
        extractTemplateFonts,
        loadFonts,
    }
})
