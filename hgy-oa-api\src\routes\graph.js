import express from 'express';
import { query, paginate } from '../config/database.js';

const router = express.Router();

// 获取图形分类
router.get('/category', async (req, res) => {
  try {
    const sql = `
      SELECT
        id,
        name,
        icon,
        sort_order,
        status,
        created_at,
        updated_at
      FROM graph_categories
      WHERE status = 1
      ORDER BY sort_order ASC, created_at DESC
    `;

    const categories = await query(sql);

    // 为每个分类获取前6个图形项目
    const categoriesWithList = [];
    for (const category of categories) {
      const itemsSql = `
        SELECT
          id,
          name,
          preview_url,
          svg_content,
          width,
          height
        FROM graphs
        WHERE status = 1 AND category_id = ?
        ORDER BY created_at DESC
        LIMIT 6
      `;

      const items = await query(itemsSql, [category.id]);

      categoriesWithList.push({
        ...category,
        list: items.map(item => ({
          ...item,
          url: item.preview_url,
          title: item.name
        }))
      });
    }

    res.success({ records: categoriesWithList, total: categoriesWithList.length });
  } catch (error) {
    console.error('获取图形分类失败:', error);
    res.error('获取图形分类失败');
  }
});

// 获取图形列表
router.get('/list', async (req, res) => {
  try {
    const { pageNum = 1, pageSize = 10, query: searchQuery } = req.query;
    let { categoryId } = searchQuery || {};
    
    let sql = `
      SELECT 
        g.id,
        g.name,
        g.preview_url,
        g.svg_content,
        g.category_id,
        g.tags,
        g.width,
        g.height,
        g.created_at,
        g.updated_at,
        gc.name as category_name
      FROM graphs g
      LEFT JOIN graph_categories gc ON g.category_id = gc.id
      WHERE g.status = 1
    `;
    
    const params = [];
    
    if (categoryId) {
      sql += ' AND g.category_id = ?';
      params.push(categoryId);
    }
    
    sql += ' ORDER BY g.created_at DESC';
    
    const result = await paginate(sql, params, pageNum, pageSize);
    
    // 解析tags字段
    result.records = result.records.map(item => ({
      ...item,
      tags: item.tags ? (typeof item.tags === 'string' ? JSON.parse(item.tags) : item.tags) : [],
      category: item.category_id
    }));
    
    res.success(result);
  } catch (error) {
    console.error('获取图形列表失败:', error);
    res.error('获取图形列表失败');
  }
});

// 根据分类获取图形
router.get('/list/category/:categoryId', async (req, res) => {
  try {
    const { categoryId } = req.params;
    const { pageNum = 1, pageSize = 10 } = req.query;
    
    const sql = `
      SELECT 
        g.id,
        g.name,
        g.preview_url,
        g.svg_content,
        g.category_id,
        g.tags,
        g.width,
        g.height,
        g.created_at,
        g.updated_at,
        gc.name as category_name
      FROM graphs g
      LEFT JOIN graph_categories gc ON g.category_id = gc.id
      WHERE g.status = 1 AND g.category_id = ?
      ORDER BY g.created_at DESC
    `;
    
    const result = await paginate(sql, [categoryId], pageNum, pageSize);
    
    // 解析tags字段
    result.records = result.records.map(item => ({
      ...item,
      tags: item.tags ? (typeof item.tags === 'string' ? JSON.parse(item.tags) : item.tags) : [],
      category: item.category_id
    }));
    
    res.success(result);
  } catch (error) {
    console.error('获取分类图形失败:', error);
    res.error('获取分类图形失败');
  }
});

// 搜索图形
router.get('/search', async (req, res) => {
  try {
    const { keyword, pageNum = 1, pageSize = 10 } = req.query;
    
    if (!keyword) {
      return res.error('搜索关键词不能为空', 400);
    }
    
    const sql = `
      SELECT 
        g.id,
        g.name,
        g.preview_url,
        g.svg_content,
        g.category_id,
        g.tags,
        g.width,
        g.height,
        g.created_at,
        g.updated_at,
        gc.name as category_name
      FROM graphs g
      LEFT JOIN graph_categories gc ON g.category_id = gc.id
      WHERE g.status = 1 AND (g.name LIKE ? OR g.tags LIKE ?)
      ORDER BY g.created_at DESC
    `;
    
    const searchTerm = `%${keyword}%`;
    const result = await paginate(sql, [searchTerm, searchTerm], pageNum, pageSize);
    
    // 解析tags字段
    result.records = result.records.map(item => ({
      ...item,
      tags: item.tags ? (typeof item.tags === 'string' ? JSON.parse(item.tags) : item.tags) : [],
      category: item.category_id
    }));
    
    res.success(result);
  } catch (error) {
    console.error('搜索图形失败:', error);
    res.error('搜索图形失败');
  }
});

// 创建图形分类
router.post('/category', async (req, res) => {
  try {
    const { name, icon, sort_order = 0 } = req.body;
    
    if (!name) {
      return res.error('分类名称不能为空', 400);
    }
    
    const sql = `
      INSERT INTO graph_categories (name, icon, sort_order, status, created_at, updated_at)
      VALUES (?, ?, ?, 1, NOW(), NOW())
    `;
    
    const result = await query(sql, [name, icon, sort_order]);
    res.success({ id: result.insertId }, '图形分类创建成功');
  } catch (error) {
    console.error('创建图形分类失败:', error);
    res.error('创建图形分类失败');
  }
});

// 创建图形
router.post('/graph', async (req, res) => {
  try {
    const { name, preview_url, svg_content, category_id, tags, width, height } = req.body;
    
    if (!name || !svg_content) {
      return res.error('图形名称和SVG内容不能为空', 400);
    }
    
    const sql = `
      INSERT INTO graphs (name, preview_url, svg_content, category_id, tags, width, height, status, created_at, updated_at)
      VALUES (?, ?, ?, ?, ?, ?, ?, 1, NOW(), NOW())
    `;
    
    const tagsStr = tags ? (typeof tags === 'object' ? JSON.stringify(tags) : tags) : null;
    const result = await query(sql, [name, preview_url, svg_content, category_id, tagsStr, width, height]);
    
    res.success({ id: result.insertId }, '图形创建成功');
  } catch (error) {
    console.error('创建图形失败:', error);
    res.error('创建图形失败');
  }
});

// 更新图形
router.put('/graph/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { name, preview_url, svg_content, category_id, tags, width, height } = req.body;
    
    const sql = `
      UPDATE graphs 
      SET name = ?, preview_url = ?, svg_content = ?, category_id = ?, tags = ?, width = ?, height = ?, updated_at = NOW()
      WHERE id = ? AND status = 1
    `;
    
    const tagsStr = tags ? (typeof tags === 'object' ? JSON.stringify(tags) : tags) : null;
    const result = await query(sql, [name, preview_url, svg_content, category_id, tagsStr, width, height, id]);
    
    if (result.affectedRows === 0) {
      return res.error('图形不存在或更新失败', 404);
    }
    
    res.success(null, '图形更新成功');
  } catch (error) {
    console.error('更新图形失败:', error);
    res.error('更新图形失败');
  }
});

// 删除图形（软删除）
router.delete('/graph/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    const sql = `
      UPDATE graphs 
      SET status = 0, updated_at = NOW()
      WHERE id = ?
    `;
    
    const result = await query(sql, [id]);
    
    if (result.affectedRows === 0) {
      return res.error('图形不存在', 404);
    }
    
    res.success(null, '图形删除成功');
  } catch (error) {
    console.error('删除图形失败:', error);
    res.error('删除图形失败');
  }
});

export default router;
