#!/usr/bin/env node

/**
 * 验证API配置修复是否完整
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 要搜索的目录
const searchDir = path.join(__dirname, '../src');

// 要搜索的文件扩展名
const extensions = ['.vue', '.ts', '.js', '.tsx', '.jsx'];

// 硬编码模式
const hardcodedPatterns = [
  /localhost:3001/g,
  /127\.0\.0\.1:3001/g,
  /'http:\/\/localhost:3001'/g,
  /"http:\/\/localhost:3001"/g,
  /`http:\/\/localhost:3001`/g
];

// 正确使用模式
const correctPatterns = [
  /import.*config\/api/g,
  /getApiBaseUrl/g,
  /getEndpointUrl/g,
  /getApiUrl/g
];

// 排除的目录和文件
const excludeDirs = ['node_modules', '.git', 'dist', 'build'];
const excludeFiles = ['api.ts']; // API配置文件本身可以有默认值

// 结果存储
const hardcodedResults = [];
const correctUsageResults = [];

// 递归搜索文件
function searchFiles(dir) {
  const files = fs.readdirSync(dir);
  
  for (const file of files) {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      if (!excludeDirs.includes(file)) {
        searchFiles(filePath);
      }
    } else if (stat.isFile()) {
      const ext = path.extname(file);
      if (extensions.includes(ext) && !excludeFiles.includes(file)) {
        searchInFile(filePath);
      }
    }
  }
}

// 在文件中搜索模式
function searchInFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const lines = content.split('\n');
    const relativePath = path.relative(searchDir, filePath);
    
    lines.forEach((line, lineNumber) => {
      // 检查硬编码模式
      hardcodedPatterns.forEach(pattern => {
        if (pattern.test(line) && !line.includes('import.meta.env')) {
          hardcodedResults.push({
            file: relativePath,
            line: lineNumber + 1,
            content: line.trim(),
            type: 'hardcoded'
          });
        }
      });
      
      // 检查正确使用模式
      correctPatterns.forEach(pattern => {
        if (pattern.test(line)) {
          correctUsageResults.push({
            file: relativePath,
            line: lineNumber + 1,
            content: line.trim(),
            type: 'correct'
          });
        }
      });
    });
  } catch (error) {
    console.error(`读取文件失败: ${filePath}`, error.message);
  }
}

// 执行搜索
console.log('🔍 验证API配置修复...\n');

searchFiles(searchDir);

// 输出结果
console.log('📊 验证结果:\n');

if (hardcodedResults.length === 0) {
  console.log('✅ 没有发现硬编码的API地址！');
} else {
  console.log(`❌ 发现 ${hardcodedResults.length} 个硬编码的API地址:`);
  hardcodedResults.forEach(result => {
    console.log(`   ${result.file}:${result.line} - ${result.content}`);
  });
}

console.log('');

if (correctUsageResults.length > 0) {
  console.log(`✅ 发现 ${correctUsageResults.length} 个正确使用API配置的地方:`);
  
  // 按文件分组
  const groupedCorrect = {};
  correctUsageResults.forEach(result => {
    if (!groupedCorrect[result.file]) {
      groupedCorrect[result.file] = 0;
    }
    groupedCorrect[result.file]++;
  });
  
  Object.keys(groupedCorrect).forEach(file => {
    console.log(`   ${file}: ${groupedCorrect[file]} 处使用`);
  });
} else {
  console.log('⚠️  没有发现使用API配置的地方，可能需要检查实现');
}

console.log('\n🎯 总结:');
console.log(`- 硬编码地址: ${hardcodedResults.length} 个`);
console.log(`- 正确使用: ${correctUsageResults.length} 个`);

if (hardcodedResults.length === 0) {
  console.log('\n🎉 所有API地址都已正确配置！');
} else {
  console.log('\n⚠️  仍有硬编码地址需要修复');
}

console.log('\n验证完成！');
