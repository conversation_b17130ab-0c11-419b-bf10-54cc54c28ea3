import { request } from '@/utils/request';
import qs from 'query-string';

const BASE_URL = '/live/sessionCopy';


export function listLiveSessionCopy(params) {
  return request.get(`${BASE_URL}/page`, {
    params,
    paramsSerializer: (obj) => {
      return qs.stringify(obj);
    },
  });
}

export function getLiveSessionCopy(id) {
  return request.get(`${BASE_URL}/info/${id}`);
}

export function addLiveSessionCopy(req) {
  return request.post(`${BASE_URL}/add`, req);
}

export function updateLiveSessionCopy(req) {
  return request.post(`${BASE_URL}/update`, req);
}

export function deleteLiveSessionCopy(ids) {
  return request.delete(`${BASE_URL}/remove/${ids}`);
}
