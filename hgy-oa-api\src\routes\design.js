import express from 'express';
import { query } from '../config/database.js';
import { authenticateUser } from '../middleware/auth.js';

const router = express.Router();

// 获取设计任务列表
router.get('/tasks', authenticateUser, async (req, res) => {
  try {
    const { 
      pageNum = 1, 
      pageSize = 10, 
      keyword = '', 
      taskType = '', 
      status = '', 
      department = '' 
    } = req.query;

    let sql = `
      SELECT
        dt.id, dt.task_name, dt.task_type, dt.assigner, dt.department,
        dt.assign_time, dt.delivery_time, dt.status, dt.rating, dt.assignee,
        dt.complete_time, dt.storage_location, dt.creator, dt.remarks,
        dt.project_overview, dt.current_status, dt.is_approved, dt.creator_user_id,
        dt.created_at as create_time, dt.updated_at
      FROM design_tasks dt
      WHERE dt.deleted_at IS NULL
    `;

    // 权限控制：普通用户只能看自己的任务，管理员可以看全部
    if (req.user.role !== 'admin') {
      sql += ` AND dt.creator_user_id = ${req.user.id}`;
    }

    const params = [];

    // 关键词搜索
    if (keyword) {
      sql += ` AND dt.task_name LIKE ?`;
      params.push(`%${keyword}%`);
    }

    // 任务类型筛选
    if (taskType) {
      sql += ` AND dt.task_type = ?`;
      params.push(taskType);
    }

    // 状态筛选
    if (status) {
      sql += ` AND dt.status = ?`;
      params.push(status);
    }

    // 部门筛选
    if (department) {
      sql += ` AND dt.department = ?`;
      params.push(department);
    }

    // 排序
    sql += ` ORDER BY dt.created_at DESC`;

    // 分页
    const pageNumInt = parseInt(pageNum) || 1;
    const pageSizeInt = parseInt(pageSize) || 10;
    const offset = (pageNumInt - 1) * pageSizeInt;
    sql += ` LIMIT ${offset}, ${pageSizeInt}`;
    // 不需要推送参数，因为我们直接在SQL中使用了值

    const tasks = await query(sql, params);

    // 获取总数
    let countSql = `
      SELECT COUNT(*) as total
      FROM design_tasks dt
      WHERE dt.deleted_at IS NULL
    `;

    // 权限控制：普通用户只能看自己的任务，管理员可以看全部
    if (req.user.role !== 'admin') {
      countSql += ` AND dt.creator_user_id = ${req.user.id}`;
    }

    const countParams = [];

    if (keyword) {
      countSql += ` AND dt.task_name LIKE ?`;
      countParams.push(`%${keyword}%`);
    }
    if (taskType) {
      countSql += ` AND dt.task_type = ?`;
      countParams.push(taskType);
    }
    if (status) {
      countSql += ` AND dt.status = ?`;
      countParams.push(status);
    }
    if (department) {
      countSql += ` AND dt.department = ?`;
      countParams.push(department);
    }

    const countResult = await query(countSql, countParams);
    const total = countResult[0].total;

    // 不再获取子任务

    res.success({
      records: tasks,
      total: total,
      pageNum: parseInt(pageNum),
      pageSize: parseInt(pageSize)
    });

  } catch (error) {
    console.error('获取设计任务列表失败:', error);
    res.error('获取设计任务列表失败', 500);
  }
});

// 创建设计任务
router.post('/tasks', authenticateUser, async (req, res) => {
  try {
    const {
      task_name,
      task_type,
      assigner,
      department,
      assign_time,
      delivery_time,
      status = '待处理',
      rating = 0,
      assignee,
      storage_location,
      creator,
      remarks,
      project_overview,
    } = req.body;

    if (!task_name || !task_type || !assigner || !department || !assign_time || !delivery_time || !creator) {
      return res.error('请填写必填字段', 400);
    }

    // 插入主任务
    const taskSql = `
      INSERT INTO design_tasks (
        task_name, task_type, assigner, department, assign_time, delivery_time,
        status, rating, assignee, storage_location, creator, remarks, project_overview,
        current_status, creator_user_id, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, '设计中', ?, NOW(), NOW())
    `;

    // 转换日期格式
    const formatDateTime = (dateStr) => {
      if (!dateStr) return null;
      const date = new Date(dateStr);
      return date.toISOString().slice(0, 19).replace('T', ' ');
    };

    const taskResult = await query(taskSql, [
      task_name, task_type, assigner, department,
      formatDateTime(assign_time), formatDateTime(delivery_time),
      status, rating, assignee, storage_location, creator, remarks, project_overview, req.user.id
    ]);

    const taskId = taskResult.insertId;

    res.success({ id: taskId }, '设计任务创建成功');

  } catch (error) {
    console.error('创建设计任务失败:', error);
    res.error('创建设计任务失败', 500);
  }
});

// 更新设计任务
router.put('/tasks/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const {
      task_name,
      task_type,
      assigner,
      department,
      assign_time,
      delivery_time,
      status,
      rating,
      assignee,
      complete_time,
      storage_location,
      creator,
      remarks,
      project_overview,
    } = req.body;

    // 检查任务是否存在
    const existingTask = await query(
      'SELECT id FROM design_tasks WHERE id = ? AND deleted_at IS NULL',
      [id]
    );

    if (existingTask.length === 0) {
      return res.error('任务不存在', 404);
    }

    // 更新主任务
    const updateSql = `
      UPDATE design_tasks SET
        task_name = ?, task_type = ?, assigner = ?, department = ?,
        assign_time = ?, delivery_time = ?, status = ?, rating = ?,
        assignee = ?, complete_time = ?, storage_location = ?, creator = ?,
        remarks = ?, project_overview = ?, updated_at = NOW()
      WHERE id = ?
    `;

    // 转换日期格式
    const formatDateTime = (dateStr) => {
      if (!dateStr) return null;
      const date = new Date(dateStr);
      return date.toISOString().slice(0, 19).replace('T', ' ');
    };

    await query(updateSql, [
      task_name, task_type, assigner, department,
      formatDateTime(assign_time), formatDateTime(delivery_time),
      status, rating, assignee, formatDateTime(complete_time),
      storage_location, creator, remarks, project_overview, id
    ]);

    res.success(null, '设计任务更新成功');

  } catch (error) {
    console.error('更新设计任务失败:', error);
    res.error('更新设计任务失败', 500);
  }
});

// 删除设计任务（软删除）
router.delete('/tasks/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // 检查任务是否存在
    const existingTask = await query(
      'SELECT id, task_name FROM design_tasks WHERE id = ? AND deleted_at IS NULL',
      [id]
    );

    if (existingTask.length === 0) {
      return res.error('任务不存在', 404);
    }

    // 软删除主任务
    await query(
      'UPDATE design_tasks SET deleted_at = NOW() WHERE id = ?',
      [id]
    );

    res.success(null, '设计任务删除成功');

  } catch (error) {
    console.error('删除设计任务失败:', error);
    res.error('删除设计任务失败', 500);
  }
});

// 获取任务详情
router.get('/tasks/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const tasks = await query(`
      SELECT 
        dt.id, dt.task_name, dt.task_type, dt.assigner, dt.department,
        dt.assign_time, dt.delivery_time, dt.status, dt.rating, dt.assignee,
        dt.complete_time, dt.storage_location, dt.creator, dt.remarks,
        dt.created_at as create_time, dt.updated_at
      FROM design_tasks dt
      WHERE dt.id = ? AND dt.deleted_at IS NULL
    `, [id]);

    if (tasks.length === 0) {
      return res.error('任务不存在', 404);
    }

    const task = tasks[0];

    res.success(task);

  } catch (error) {
    console.error('获取任务详情失败:', error);
    res.error('获取任务详情失败', 500);
  }
});

// 切换任务状态（设计中 <-> 设计完）
router.patch('/tasks/:id/toggle-status', authenticateUser, async (req, res) => {
  try {
    const { id } = req.params;

    // 检查任务是否存在
    const existingTask = await query(
      'SELECT id, current_status, creator_user_id FROM design_tasks WHERE id = ? AND deleted_at IS NULL',
      [id]
    );

    if (existingTask.length === 0) {
      return res.error('任务不存在', 404);
    }

    const task = existingTask[0];

    // 权限检查：只有任务创建者可以切换状态
    if (req.user.role !== 'admin' && task.creator_user_id !== req.user.id) {
      return res.error('无权限操作此任务', 403);
    }

    // 切换状态：设计中 <-> 设计完
    const newStatus = task.current_status === '设计中' ? '设计完' : '设计中';

    await query(
      'UPDATE design_tasks SET current_status = ?, updated_at = NOW() WHERE id = ?',
      [newStatus, id]
    );

    res.success({ current_status: newStatus }, '状态切换成功');

  } catch (error) {
    console.error('切换任务状态失败:', error);
    res.error('切换任务状态失败', 500);
  }
});

// 管理员审核任务
router.patch('/tasks/:id/approve', authenticateUser, async (req, res) => {
  try {
    const { id } = req.params;

    // 权限检查：只有管理员可以审核
    if (req.user.role !== 'admin') {
      return res.error('无权限进行审核操作', 403);
    }

    // 检查任务是否存在
    const existingTask = await query(
      'SELECT id, is_approved FROM design_tasks WHERE id = ? AND deleted_at IS NULL',
      [id]
    );

    if (existingTask.length === 0) {
      return res.error('任务不存在', 404);
    }

    const task = existingTask[0];
    const newApprovalStatus = task.is_approved ? 0 : 1;
    const newCurrentStatus = newApprovalStatus ? '验收完' : '设计完';

    await query(
      'UPDATE design_tasks SET is_approved = ?, current_status = ?, updated_at = NOW() WHERE id = ?',
      [newApprovalStatus, newCurrentStatus, id]
    );

    res.success({
      is_approved: newApprovalStatus,
      current_status: newCurrentStatus
    }, newApprovalStatus ? '审核通过' : '取消审核');

  } catch (error) {
    console.error('审核任务失败:', error);
    res.error('审核任务失败', 500);
  }
});

// 获取设计任务统计
router.get('/stats', async (req, res) => {
  try {
    const { timeRange = '7' } = req.query; // 默认7天

    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(endDate.getDate() - parseInt(timeRange));

    // 总任务数
    const totalTasks = await query('SELECT COUNT(*) as count FROM design_tasks WHERE deleted_at IS NULL');

    // 已完成任务数（按完成时间筛选）
    const completedTasks = await query(
      'SELECT COUNT(*) as count FROM design_tasks WHERE complete_time BETWEEN ? AND ? AND deleted_at IS NULL',
      [startDate.toISOString().slice(0, 19).replace('T', ' '), endDate.toISOString().slice(0, 19).replace('T', ' ')]
    );

    // 进行中任务数
    const inProgressTasks = await query(
      'SELECT COUNT(*) as count FROM design_tasks WHERE current_status = ? AND deleted_at IS NULL',
      ['设计中']
    );

    // 已验收任务数
    const approvedTasks = await query(
      'SELECT COUNT(*) as count FROM design_tasks WHERE current_status = ? AND deleted_at IS NULL',
      ['验收完']
    );

    // 按状态统计
    const statusStats = await query(`
      SELECT current_status, COUNT(*) as count
      FROM design_tasks
      WHERE complete_time BETWEEN ? AND ? AND deleted_at IS NULL
      GROUP BY current_status
    `, [startDate.toISOString().slice(0, 19).replace('T', ' '), endDate.toISOString().slice(0, 19).replace('T', ' ')]);

    // 按派发部门统计分数
    const deptStats = await query(`
      SELECT
        d.department,
        COUNT(*) as task_count,
        ROUND(AVG(d.rating), 2) as avg_rating,
        SUM(d.rating) as total_score
      FROM design_tasks d
      WHERE d.complete_time BETWEEN ? AND ? AND d.deleted_at IS NULL AND d.department IS NOT NULL AND d.department != ''
      GROUP BY d.department
      ORDER BY total_score DESC
      LIMIT 10
    `, [startDate.toISOString().slice(0, 19).replace('T', ' '), endDate.toISOString().slice(0, 19).replace('T', ' ')]);

    // 按处理人统计分数
    const assigneeStats = await query(`
      SELECT
        d.assignee,
        COUNT(*) as task_count,
        ROUND(AVG(d.rating), 2) as avg_rating,
        SUM(d.rating) as total_score
      FROM design_tasks d
      WHERE d.complete_time BETWEEN ? AND ? AND d.deleted_at IS NULL AND d.assignee IS NOT NULL AND d.assignee != ''
      GROUP BY d.assignee
      ORDER BY total_score DESC
      LIMIT 10
    `, [startDate.toISOString().slice(0, 19).replace('T', ' '), endDate.toISOString().slice(0, 19).replace('T', ' ')]);

    res.success({
      totalTasks: totalTasks[0].count,
      completedTasks: completedTasks[0].count,
      inProgressTasks: inProgressTasks[0].count,
      approvedTasks: approvedTasks[0].count,
      statusStats,
      deptStats,
      assigneeStats,
      timeRange: parseInt(timeRange)
    }, '获取设计任务统计成功');
  } catch (error) {
    console.error('获取设计任务统计失败:', error);
    res.error('获取设计任务统计失败', 500);
  }
});

export default router;
