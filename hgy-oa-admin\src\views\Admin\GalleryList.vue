<template>
  <div class="gallery-list">
    <!-- 搜索筛选 -->
    <a-card style="margin-bottom: 16px;">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-input v-model="searchForm.keyword" placeholder="搜索图片标题" allow-clear @press-enter="handleSearch">
            <template #prefix>
              <icon-search />
            </template>
          </a-input>
        </a-col>
        <a-col :span="4">
          <a-select v-model="searchForm.primaryCategory" placeholder="一级分类" allow-clear @change="handleSearchPrimaryCategoryChange">
            <a-option v-for="category in primaryCategories" :key="category.id" :value="category.id">
              {{ category.name }}
            </a-option>
          </a-select>
        </a-col>
        <a-col :span="4">
          <a-select v-model="searchForm.secondaryCategory" placeholder="二级分类" allow-clear :disabled="!searchForm.primaryCategory">
            <a-option v-for="category in currentSecondaryCategories" :key="category.id" :value="category.id">
              {{ category.name }}
            </a-option>
          </a-select>
        </a-col>
        <a-col :span="6">
          <a-button type="primary" @click="handleSearch">搜索</a-button>
          <a-button style="margin-left: 8px;" @click="handleReset">重置</a-button>
          <a-button style="margin-left: 8px;" @click="handleRefresh">
            <template #icon>
              <icon-refresh />
            </template>
            刷新
          </a-button>
          <a-button style="margin-left: 8px;" type="primary" @click="showUploadModal">
            <template #icon>
              <icon-upload />
            </template>
            上传图片
          </a-button>
        </a-col>
      </a-row>
    </a-card>

    <a-card>
      <a-table :columns="columns" :data="imageData" :pagination="pagination" :loading="loading" @page-change="handlePageChange" @page-size-change="handlePageSizeChange">
        <template #id="{ record }">
          <div class="image-id">#{{ formatImageId(record.id) }}</div>
        </template>

        <template #thumbnail="{ record }">
          <div class="thumbnail-wrapper">
            <img :src="record.thumbnail_path || record.file_path" :alt="record.title" class="thumbnail-image" @click="showPreviewModal(record)" />
            <a-tag v-if="record.has_suite" class="suite-tag" color="red" size="small">
              套件
            </a-tag>
          </div>
        </template>

        <template #size="{ record }">
          <div>{{ record.width }} × {{ record.height }}</div>
          <div class="file-size">{{ formatFileSize(record.file_size) }}</div>
        </template>

        <template #category="{ record }">
          <div v-if="record.categories && record.categories.length > 0">
            <a-tag v-for="category in record.categories" :key="category.id" color="blue" size="small" style="margin-right: 4px; margin-bottom: 4px;">
              {{ category.name }}
            </a-tag>
          </div>
          <span v-else class="no-data">-</span>
        </template>

        <template #suite_category="{ record }">
          <div v-if="record.suites && record.suites.length > 0">
            <a-tag v-for="suite in record.suites" :key="suite.id" color="purple" size="small" style="margin-right: 4px; margin-bottom: 4px;" :title="suite.description">
              {{ suite.name }}
            </a-tag>
          </div>
          <span v-else class="no-data">-</span>
        </template>

        <template #documents="{ record }">
          <div v-if="record.documents && record.documents.length > 0" class="documents-list">
            <a-tag v-for="document in record.documents" :key="document.id" color="green" size="small" style="margin-right: 4px; margin-bottom: 4px;" :title="document.title">
              {{ document.title.length > 8 ? document.title.substring(0, 8) + '...' : document.title }}
            </a-tag>
          </div>
          <span v-else class="no-data">-</span>
        </template>

        <template #upload_user="{ record }">
          {{ record.upload_user_nickname || record.upload_user || '-' }}
        </template>

        <template #netdisk_url="{ record }">
          <div class="netdisk-url">
            <span v-if="record.description" style="word-break: break-all; font-size: 12px;">
              {{ record.description }}
            </span>
            <span v-else class="no-data">-</span>
          </div>
        </template>

        <template #status="{ record }">
          <a-switch :model-value="record.is_public === 1" @change="(value) => handleStatusChange(record, value)" size="small" />
        </template>

        <template #created_at="{ record }">
          {{ formatDate(record.created_at) }}
        </template>

        <template #actions="{ record }">
          <a-space size="mini">
            <a-button type="text" size="small" @click="showEditModal(record)">
              编辑
            </a-button>
            <a-button type="text" size="small" status="danger" @click="handleDelete(record)">
              删除
            </a-button>
          </a-space>
        </template>
      </a-table>
    </a-card>

    <!-- 上传图片弹窗 -->
    <a-modal v-model:visible="uploadModalVisible" title="上传图片" width="600px" @ok="handleUploadSubmit" @cancel="handleUploadCancel">
      <a-form :model="uploadForm" :rules="uploadRules" ref="uploadFormRef" layout="vertical">
        <a-form-item label="图片文件" field="file">
          <a-upload ref="uploadRef" :file-list="fileList" :auto-upload="false" :show-file-list="true" :limit="1" accept="image/*" @change="handleFileChange" @before-upload="handleBeforeUpload">
            <template #upload-button>
              <div class="upload-area">
                <icon-upload style="font-size: 48px; color: #c9cdd4;" />
                <div style="margin-top: 8px; color: #86909c;">点击或拖拽上传图片</div>
                <div style="margin-top: 4px; color: #c9cdd4; font-size: 12px;">
                  支持 JPG、PNG、GIF 格式，文件大小不超过 10MB
                </div>
              </div>
            </template>
          </a-upload>
        </a-form-item>

        <a-form-item label="图片标题" field="title">
          <a-input v-model="uploadForm.title" placeholder="请输入图片标题" />
        </a-form-item>

        <!-- 基础分类选择 -->
        <a-form-item label="基础分类" field="basic_primary_category" required>
          <div class="category-section">
            <a-row :gutter="16">
              <a-col :span="12">
                <a-select v-model="uploadForm.basic_primary_category" placeholder="请选择一级分类" @change="handleBasicPrimaryCategoryChange">
                  <a-option v-for="category in basicCategories" :key="category.id" :value="category.id">
                    {{ category.name }}
                  </a-option>
                </a-select>
              </a-col>
              <a-col :span="12">
                <a-select v-model="uploadForm.basic_secondary_category" placeholder="请选择二级分类" allow-clear :disabled="!uploadForm.basic_primary_category" @change="handleBasicSecondaryCategoryChange">
                  <a-option v-for="category in currentBasicSecondaryCategories" :key="category.id" :value="category.id">
                    {{ category.name }}
                  </a-option>
                </a-select>
              </a-col>
            </a-row>
          </div>
        </a-form-item>

        <!-- 套件分类选择（可选） -->
        <a-form-item label="套件分类（可选）">
          <a-row :gutter="16">
            <a-col :span="12">
              <a-select
                v-model="uploadForm.suite_primary_category"
                placeholder="请选择一级分类"
                allow-clear
                :loading="suiteCategoriesLoading"
                @change="handleSuitePrimaryCategoryChange"
              >
                <a-option v-for="category in suitePrimaryCategories" :key="category.id" :value="category.id">
                  {{ category.name }}
                </a-option>
              </a-select>
            </a-col>
            <a-col :span="12">
              <a-select
                v-model="uploadForm.suite_secondary_category"
                placeholder="请选择二级分类"
                allow-clear
                :disabled="!uploadForm.suite_primary_category || currentSuiteSecondaryCategories.length === 0"
              >
                <a-option v-for="category in currentSuiteSecondaryCategories" :key="category.id" :value="category.id">
                  {{ category.name }}
                </a-option>
              </a-select>
            </a-col>
          </a-row>
        </a-form-item>

        <!-- 文案关联选择 -->
        <a-form-item label="关联文案（可选）">
          <a-select v-model="uploadForm.document_ids" placeholder="请选择关联文案" multiple allow-clear :loading="documentsLoading">
            <a-option v-for="document in availableDocuments" :key="document.id" :value="document.id">
              <div class="document-option">
                <span class="document-title">{{ document.title }}</span>
                <a-tag size="small" :color="getDocumentTypeColor(document.file_type)">
                  {{ getDocumentTypeName(document.file_type) }}
                </a-tag>
              </div>
            </a-option>
          </a-select>
        </a-form-item>

        <a-form-item label="网盘地址" field="description">
          <a-textarea v-model="uploadForm.description" placeholder="请输入网盘地址" :rows="3" />
        </a-form-item>

        <a-form-item label="资料准备" field="material_preparation">
          <a-textarea v-model="uploadForm.material_preparation" placeholder="资料准备说明将根据选择的分类自动填充，您也可以手动修改" :rows="3" />
        </a-form-item>

        <a-form-item label="是否公开" field="is_public">
          <a-switch v-model="uploadForm.is_public" checked-text="公开" unchecked-text="私有" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 图片预览弹窗 -->
    <a-modal v-model:visible="previewModalVisible" :footer="false" width="90%" :mask-closable="true">
      <template #title>
        <span>{{ currentPreviewImage?.title }}</span>
      </template>
      <div class="preview-container" v-if="currentPreviewImage">
        <a-row :gutter="24">
          <!-- 左侧图片 -->
          <a-col :span="12">
            <div class="preview-image-container">
              <div class="image-wrapper">
                <img :src="currentPreviewImage.file_path" :alt="currentPreviewImage.title" class="preview-image" />
              </div>
            </div>
          </a-col>

          <!-- 右侧信息 -->
          <a-col :span="12">
            <div class="info-section">
              <h4>图片信息</h4>
              <a-descriptions :column="1" size="small" bordered>
                <a-descriptions-item label="尺寸">{{ currentPreviewImage.width }} × {{ currentPreviewImage.height }}</a-descriptions-item>
                <a-descriptions-item label="格式">{{ currentPreviewImage.format.toUpperCase() }}</a-descriptions-item>
                <a-descriptions-item label="大小">{{ formatFileSize(currentPreviewImage.file_size) }}</a-descriptions-item>

                <a-descriptions-item label="基础分类">
                  <div v-if="currentPreviewImage.basic_categories && currentPreviewImage.basic_categories.length > 0">
                    <a-tag v-for="category in currentPreviewImage.basic_categories" :key="category.id" style="margin-right: 4px; margin-bottom: 4px;">
                      {{ category.name }}
                    </a-tag>
                  </div>
                  <span v-else class="no-data">暂无分类</span>
                </a-descriptions-item>

                <a-descriptions-item label="网盘地址">
                  <div v-if="currentPreviewImage.description" class="netdisk-url">
                    <span style="word-break: break-all; font-size: 12px;">{{ currentPreviewImage.description }}</span>
                  </div>
                  <span v-else class="no-data">暂无网盘地址</span>
                </a-descriptions-item>

                <a-descriptions-item label="资料准备">
                  <div v-if="currentPreviewImage.material_preparation" class="material-preparation">
                    <div style="background: #f5f5f5; padding: 8px; border-radius: 4px; max-height: 120px; overflow-y: auto;">
                      <pre style="white-space: pre-wrap; font-family: inherit; margin: 0; font-size: 12px;">{{ currentPreviewImage.material_preparation }}</pre>
                    </div>
                  </div>
                  <span v-else class="no-data">暂无资料准备说明</span>
                </a-descriptions-item>

                <a-descriptions-item label="文案文件">
                  <div v-if="currentPreviewImage.documents && currentPreviewImage.documents.length > 0" class="related-documents">
                    <div v-for="document in currentPreviewImage.documents" :key="document.id" class="document-item" style="margin-bottom: 8px;">
                      <a-tag :color="getDocumentTypeColor(document.file_type)" size="small">
                        {{ getDocumentTypeName(document.file_type) }}
                      </a-tag>
                      <a v-if="document.remark" :href="document.remark" target="_blank" rel="noopener noreferrer" class="document-link" style="margin-left: 8px; font-size: 12px; color: #165dff; text-decoration: none;">
                        {{ document.title }}
                      </a>
                      <span v-else class="document-name" style="margin-left: 8px; font-size: 12px; color: #86909c;">
                        {{ document.title }}（无下载链接）
                      </span>
                    </div>
                  </div>
                  <span v-else class="no-data">暂无关联文案</span>
                </a-descriptions-item>

                <a-descriptions-item label="是否套件">
                  <div v-if="currentPreviewImage.suite_categories && currentPreviewImage.suite_categories.length > 0" class="suite-info">
                    <div style="display: flex; align-items: center; gap: 8px;">
                      <div>
                        <a-tag v-for="category in currentPreviewImage.suite_categories" :key="category.id" color="red" style="margin-right: 4px;">
                          {{ category.name }}
                        </a-tag>
                      </div>
                      <a-button type="primary" size="small" @click="goToSuiteCategory">
                        查看套件
                      </a-button>
                    </div>
                  </div>
                  <span v-else class="no-data">非套件图片</span>
                </a-descriptions-item>
              </a-descriptions>
            </div>
          </a-col>
        </a-row>
      </div>
    </a-modal>

    <!-- 编辑图片弹窗 -->
    <a-modal v-model:visible="editModalVisible" title="编辑图片信息" width="600px" @ok="handleEditSave" @cancel="editModalVisible = false">
      <a-form :model="editForm" layout="vertical">
        <a-form-item label="当前图片">
          <div class="current-image-preview">
            <img :src="currentEditImage?.thumbnail_path || currentEditImage?.file_path" :alt="editForm.title" style="max-width: 200px; max-height: 150px; object-fit: cover; border-radius: 4px;" />
          </div>
        </a-form-item>

        <a-form-item label="替换图片">
          <a-upload :file-list="editFileList" :show-file-list="true" :before-upload="handleEditBeforeUpload" @change="handleEditFileChange" accept="image/*" :limit="1">
            <template #upload-button>
              <div class="upload-button">
                <icon-upload />
                <div>点击替换图片</div>
              </div>
            </template>
          </a-upload>
          <div style="margin-top: 8px; color: #86909c; font-size: 12px;">
            支持 jpg、png、gif 格式，文件大小不超过 10MB
          </div>
        </a-form-item>

        <a-form-item label="图片标题" required>
          <a-input v-model="editForm.title" placeholder="请输入图片标题" />
        </a-form-item>

        <!-- 基础分类选择 -->
        <a-form-item label="基础分类" required>
          <div class="category-section">
            <a-row :gutter="16">
              <a-col :span="12">
                <a-select v-model="editForm.basic_primary_category" placeholder="请选择一级分类" @change="handleEditBasicPrimaryCategoryChange">
                  <a-option v-for="category in basicCategories" :key="category.id" :value="category.id">
                    {{ category.name }}
                  </a-option>
                </a-select>
              </a-col>
              <a-col :span="12">
                <a-select v-model="editForm.basic_secondary_category" placeholder="请选择二级分类" allow-clear :disabled="!editForm.basic_primary_category" @change="handleEditBasicSecondaryCategoryChange">
                  <a-option v-for="category in editBasicSecondaryCategories" :key="category.id" :value="category.id">
                    {{ category.name }}
                  </a-option>
                </a-select>
              </a-col>
            </a-row>
          </div>
        </a-form-item>

        <!-- 套件分类选择（可选） -->
        <a-form-item label="套件分类（可选）">
          <a-row :gutter="16">
            <a-col :span="12">
              <a-select
                v-model="editForm.suite_primary_category"
                placeholder="请选择一级分类"
                allow-clear
                :loading="suiteCategoriesLoading"
                @change="handleEditSuitePrimaryCategoryChange"
              >
                <a-option v-for="category in suitePrimaryCategories" :key="category.id" :value="category.id">
                  {{ category.name }}
                </a-option>
              </a-select>
            </a-col>
            <a-col :span="12">
              <a-select
                v-model="editForm.suite_secondary_category"
                placeholder="请选择二级分类"
                allow-clear
                :disabled="!editForm.suite_primary_category || editSuiteSecondaryCategories.length === 0"
              >
                <a-option v-for="category in editSuiteSecondaryCategories" :key="category.id" :value="category.id">
                  {{ category.name }}
                </a-option>
              </a-select>
            </a-col>
          </a-row>
        </a-form-item>

        <!-- 文案关联选择 -->
        <a-form-item label="关联文案（可选）">
          <a-select v-model="editForm.document_ids" placeholder="请选择关联文案" multiple allow-clear :loading="documentsLoading">
            <a-option v-for="document in availableDocuments" :key="document.id" :value="document.id">
              <div class="document-option">
                <span class="document-title">{{ document.title }}</span>
                <a-tag size="small" :color="getDocumentTypeColor(document.file_type)">
                  {{ getDocumentTypeName(document.file_type) }}
                </a-tag>
              </div>
            </a-option>
          </a-select>
        </a-form-item>

        <a-form-item label="网盘地址">
          <a-textarea v-model="editForm.description" placeholder="请输入网盘地址" :rows="3" />
        </a-form-item>

        <a-form-item label="资料准备">
          <a-textarea v-model="editForm.material_preparation" placeholder="资料准备说明将根据选择的分类自动填充，您也可以手动修改" :rows="3" />
        </a-form-item>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="公开状态">
              <a-switch v-model="editForm.is_public">
                <template #checked>公开</template>
                <template #unchecked>私有</template>
              </a-switch>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="推荐状态">
              <a-switch v-model="editForm.is_featured">
                <template #checked>推荐</template>
                <template #unchecked>普通</template>
              </a-switch>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from 'vue'
import { Message, Modal } from '@arco-design/web-vue'
import { IconRefresh, IconUpload, IconSearch, IconStarFill } from '@arco-design/web-vue/es/icon'
import { getCategoryList, getImageList, uploadImage, deleteImage, updateImage, batchDeleteImages } from '@/api/gallery'
import { request } from '@/utils/request'

// 响应式数据
const loading = ref(false)
const imageData = ref([])
const primaryCategories = ref([])
const secondaryCategories = ref([])
const currentSecondaryCategories = ref([])
const basicCategories = ref([])
const suitePrimaryCategories = ref([])
const suiteCategoriesLoading = ref(false)
const currentSuiteSecondaryCategories = ref([])
const editSuiteSecondaryCategories = ref([])
const currentBasicSecondaryCategories = ref([])
const availableDocuments = ref([])
const documentsLoading = ref(false)
const uploadModalVisible = ref(false)
const previewModalVisible = ref(false)
const currentPreviewImage = ref(null)
const fileList = ref([])

// 搜索表单
const searchForm = reactive({
  keyword: '',
  primaryCategory: '',
  secondaryCategory: ''
})

// 上传表单
const uploadForm = reactive({
  title: '',
  description: '',
  material_preparation: '',
  basic_primary_category: null,
  basic_secondary_category: null,
  suite_primary_category: null,
  suite_secondary_category: null,
  document_ids: [],
  is_public: true,
  file: null
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showTotal: true,
  showPageSize: true
})

// 表格列配置
const columns = [
  { title: '缩略图', slotName: 'thumbnail', width: 100, align: 'center' },
  { title: '标题', dataIndex: 'title', width: 180 },
  { title: '尺寸/大小', slotName: 'size', width: 120 },
  { title: '格式', dataIndex: 'format', width: 80, align: 'center' },
  { title: '图片分类', slotName: 'category', width: 150 },
  { title: '套件分类', slotName: 'suite_category', width: 150 },
  { title: '关联文案', slotName: 'documents', width: 150 },
  { title: '上传用户', slotName: 'upload_user', width: 100 },
  { title: '网盘地址', slotName: 'netdisk_url', width: 150 },
  { title: '上传时间', slotName: 'created_at', width: 120 },
  { title: '状态', slotName: 'status', width: 80, align: 'center' },
  { title: '操作', slotName: 'actions', width: 100, align: 'center', fixed: 'right' }
]

// 表单引用
const uploadFormRef = ref()
const uploadRef = ref()

// 表单验证规则
const uploadRules = {
  title: [{ required: true, message: '请输入图片标题' }],
  basic_primary_category: [{ required: true, message: '请选择基础分类' }]
}

// 工具函数
const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 格式化日期（只显示年月日）
const formatDate = (dateTime) => {
  if (!dateTime) return '-'
  const date = new Date(dateTime)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

// 获取图片列表
const fetchImageList = async () => {
  loading.value = true
  try {
    const params = {
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
      keyword: searchForm.keyword,
      categoryId: searchForm.primaryCategory || searchForm.secondaryCategory,
      categoryType: searchForm.primaryCategory ? 'basic' : '',
      isPublic: ''
    }

    // 使用新的多分类API
    const response = await request.get('/api/gallery/images/multi-category', { params })

    if (response && response.success) {
      imageData.value = response.data.records || []
      pagination.total = response.data.total || 0
    } else {
      // 使用原有API作为后备
      const fallbackResponse = await getImageList({
        pageNum: pagination.current,
        pageSize: pagination.pageSize,
        keyword: searchForm.keyword,
        primaryCategory: searchForm.primaryCategory,
        secondaryCategory: searchForm.secondaryCategory
      })

      if (fallbackResponse.success) {
        const result = fallbackResponse.data
        imageData.value = result.records || []
        pagination.total = result.total || 0
      }
    }
  } catch (error) {
    console.error('获取图片列表失败:', error)
    Message.error('获取图片列表失败')
  } finally {
    loading.value = false
  }
}

// 获取分类列表
const fetchCategories = async () => {
  try {
    const response = await getCategoryList()
    if (response.success) {
      const categories = response.data

      // 分离一级和二级分类
      const primary = []
      const secondary = []

      categories.forEach((category) => {
        if (category.children && category.children.length > 0) {
          primary.push(category)
          secondary.push(...category.children)
        } else if (!category.parent_id) {
          primary.push(category)
        }
      })

      primaryCategories.value = primary
      secondaryCategories.value = secondary
    }
  } catch (error) {
    console.error('获取分类列表失败:', error)
    Message.error('获取分类列表失败')
  }
}

// 搜索
const handleSearch = () => {
  pagination.current = 1
  fetchImageList()
}

// 重置搜索
const handleReset = () => {
  searchForm.keyword = ''
  searchForm.primaryCategory = ''
  searchForm.secondaryCategory = ''
  pagination.current = 1
  fetchImageList()
}

// 刷新
const handleRefresh = () => {
  fetchImageList()
}

// 分页处理
const handlePageChange = (page) => {
  pagination.current = page
  fetchImageList()
}

const handlePageSizeChange = (pageSize) => {
  pagination.pageSize = pageSize
  pagination.current = 1
  fetchImageList()
}

// 显示上传弹窗
const showUploadModal = () => {
  resetUploadForm()
  uploadModalVisible.value = true
}

// 重置上传表单
const resetUploadForm = () => {
  uploadForm.title = ''
  uploadForm.description = ''
  uploadForm.material_preparation = ''
  uploadForm.basic_primary_category = null
  uploadForm.basic_secondary_category = null
  uploadForm.suite_primary_category = null
  uploadForm.suite_secondary_category = null
  uploadForm.document_ids = []
  uploadForm.is_public = true
  uploadForm.file = null
  fileList.value = []
  currentBasicSecondaryCategories.value = []
  currentSuiteSecondaryCategories.value = []
}

// 搜索表单一级分类变化
const handleSearchPrimaryCategoryChange = (categoryId) => {
  searchForm.secondaryCategory = ''
  currentSecondaryCategories.value = secondaryCategories.value.filter((cat) => cat.parent_id === categoryId)
}

// 上传表单一级分类变化（这个函数可能不再需要，因为现在使用新的分类系统）
const handlePrimaryCategoryChange = (categoryId) => {
  // 这个函数保留以防其他地方使用
  currentSecondaryCategories.value = secondaryCategories.value.filter((cat) => cat.parent_id === categoryId)
}

// 文件上传前的处理
const handleBeforeUpload = (file) => {
  // 检查文件类型
  const isImage = file.type.startsWith('image/')
  if (!isImage) {
    Message.error('只能上传图片文件!')
    return false
  }

  // 检查文件大小 (10MB)
  const isLt10M = file.size / 1024 / 1024 < 10
  if (!isLt10M) {
    Message.error('图片大小不能超过 10MB!')
    return false
  }

  // 手动创建文件列表项
  const fileItem = {
    uid: Date.now().toString(),
    name: file.name,
    status: 'init',
    originFile: file,
    file: file
  }

  // 更新文件列表
  fileList.value = [fileItem]

  // 自动设置标题为文件名（去掉扩展名）
  if (file.name) {
    const filename = file.name
    const dotIndex = filename.lastIndexOf('.')
    const title = dotIndex > 0 ? filename.substring(0, dotIndex) : filename
    uploadForm.title = title
  }

  return false // 阻止自动上传，我们手动处理
}

// 文件变化
const handleFileChange = (fileListParam, file) => {
  // 更新文件列表
  fileList.value = fileListParam

  // 处理新添加的文件，自动设置标题
  if (file) {
    const rawFile = file.originFile || file.file || file
    if (rawFile && rawFile.name) {
      const filename = rawFile.name
      const dotIndex = filename.lastIndexOf('.')
      const title = dotIndex > 0 ? filename.substring(0, dotIndex) : filename
      uploadForm.title = title
    }
  }
}

// 上传提交
const handleUploadSubmit = async () => {
  try {
    // 检查表单引用
    if (!uploadFormRef.value) {
      Message.error('表单初始化失败')
      return
    }

    const errors = await uploadFormRef.value.validate()
    if (errors) {
      return
    }

    if (fileList.value.length === 0) {
      Message.error('请选择要上传的图片')
      return
    }

    const fileItem = fileList.value[0]

    // 获取原始文件对象
    const file = fileItem.originFile || fileItem.file || fileItem

    if (!file || !(file instanceof File)) {
      Message.error('文件不存在或格式不正确')
      return
    }

    // 创建FormData
    const formData = new FormData()
    formData.append('file', file)
    formData.append('title', uploadForm.title)
    formData.append('description', uploadForm.description || '')
    formData.append('material_preparation', uploadForm.material_preparation || '')

    // 处理基础分类
    const basicCategories = []
    if (uploadForm.basic_primary_category) {
      basicCategories.push(uploadForm.basic_primary_category)
    }
    if (uploadForm.basic_secondary_category) {
      basicCategories.push(uploadForm.basic_secondary_category)
    }
    formData.append('basic_categories', basicCategories.join(','))

    // 处理套件分类关联
    if (uploadForm.suite_secondary_category) {
      formData.append('suite_category_id', uploadForm.suite_secondary_category)
    }

    // 处理文案关联
    if (uploadForm.document_ids && uploadForm.document_ids.length > 0) {
      formData.append('document_ids', uploadForm.document_ids.join(','))
    }

    formData.append('is_public', uploadForm.is_public ? '1' : '0')

    // 调用上传API
    const response = await uploadImage(formData)

    if (response.success) {
      Message.success('图片上传成功')
      uploadModalVisible.value = false
      resetUploadForm()
      fetchImageList()
    } else {
      Message.error(response.msg || '上传失败')
    }
  } catch (error: any) {
    console.error('上传图片失败:', error)
    Message.error('上传图片失败: ' + (error.message || '未知错误'))
  }
}

// 上传取消
const handleUploadCancel = () => {
  uploadModalVisible.value = false
  uploadFormRef.value?.resetFields()
}

// 显示预览弹窗
const showPreviewModal = (record) => {
  currentPreviewImage.value = record
  previewModalVisible.value = true
}

// 编辑相关状态
const editModalVisible = ref(false)
const editForm = reactive({
  id: null,
  title: '',
  description: '',
  material_preparation: '',
  basic_primary_category: null,
  basic_secondary_category: null,
  suite_primary_category: null,
  suite_secondary_category: null,
  document_ids: [],
  is_public: true,
  is_featured: false
})
const editBasicSecondaryCategories = ref([])
const currentEditImage = ref(null)
const editFileList = ref([])
const editHasNewFile = ref(false)

// 显示编辑弹窗
const showEditModal = async (record: any) => {
  console.log('编辑记录数据:', record) // 调试用
  console.log('分类数据:', record.categories) // 调试分类数据

  editForm.id = record.id
  editForm.title = record.title || ''
  editForm.description = record.description || ''
  editForm.material_preparation = record.material_preparation || ''
  editForm.is_public = record.is_public === 1
  editForm.is_featured = record.is_featured === 1

  // 映射基础分类数据 - 从categories字段映射到表单字段
  if (record.categories && record.categories.length > 0) {
    // 找到一级分类（parent_id为null的）
    const primaryCategory = record.categories.find((cat) => !cat.parent_id)
    editForm.basic_primary_category = primaryCategory ? primaryCategory.id : null

    // 找到二级分类（有parent_id的）- 只取第一个
    const secondaryCategories = record.categories.filter((cat) => cat.parent_id)
    editForm.basic_secondary_category = secondaryCategories.length > 0 ? secondaryCategories[0].id : null

    console.log('映射后的一级分类ID:', editForm.basic_primary_category)
    console.log('映射后的二级分类ID:', editForm.basic_secondary_category)
  } else {
    // 兼容旧的数据结构
    editForm.basic_primary_category = record.primary_category_id || null
    editForm.basic_secondary_category = record.secondary_category_id || null
    console.log('使用兼容数据结构')
  }

  // 映射套件分类数据
  console.log('套件数据:', record.suites) // 调试套件数据
  if (record.suites && record.suites.length > 0) {
    // 获取第一个套件的分类信息
    const firstSuite = record.suites[0]
    if (firstSuite.category_id) {
      // 根据套件的分类ID找到对应的套件分类
      // 需要在所有套件分类中查找（包括一级和二级）
      let suiteCategory = null
      for (const primaryCat of suitePrimaryCategories.value) {
        if (primaryCat.id === firstSuite.category_id) {
          suiteCategory = primaryCat
          break
        }
        if (primaryCat.children) {
          const secondaryCat = primaryCat.children.find((cat: any) => cat.id === firstSuite.category_id)
          if (secondaryCat) {
            suiteCategory = secondaryCat
            break
          }
        }
      }
      if (suiteCategory) {
        if (suiteCategory.parent_id) {
          // 这是二级分类
          editForm.suite_primary_category = suiteCategory.parent_id
          editForm.suite_secondary_category = suiteCategory.id
        } else {
          // 这是一级分类
          editForm.suite_primary_category = suiteCategory.id
          editForm.suite_secondary_category = null
        }
        console.log('映射后的套件一级分类ID:', editForm.suite_primary_category)
        console.log('映射后的套件二级分类ID:', editForm.suite_secondary_category)

        // 加载对应的二级分类列表
        if (editForm.suite_primary_category) {
          const primaryCategory = suitePrimaryCategories.value.find((cat: any) => cat.id === editForm.suite_primary_category)
          editSuiteSecondaryCategories.value = primaryCategory ? primaryCategory.children : []
        }
      }
    }
  } else {
    editForm.suite_primary_category = null
    editForm.suite_secondary_category = null
    editSuiteSecondaryCategories.value = []
  }

  // 映射关联文案数据
  if (record.documents && record.documents.length > 0) {
    editForm.document_ids = record.documents.map((doc) => doc.id)
  } else {
    editForm.document_ids = record.document_ids || []
  }

  // 保存当前图片信息
  currentEditImage.value = record

  // 重置文件列表
  editFileList.value = []
  editHasNewFile.value = false

  // 设置基础分类的二级分类选项
  if (editForm.basic_primary_category) {
    const primaryCategory = basicCategories.value.find((cat) => cat.id === editForm.basic_primary_category)
    if (primaryCategory && primaryCategory.children) {
      editBasicSecondaryCategories.value = primaryCategory.children
    } else {
      editBasicSecondaryCategories.value = []
    }

    // 获取并设置资料准备信息（如果当前没有的话）
    if (!editForm.material_preparation) {
      const materialPreparation = await getCategoryMaterialPreparation(editForm.basic_primary_category)
      editForm.material_preparation = materialPreparation || ''
    }
  } else {
    editBasicSecondaryCategories.value = []
  }



  editModalVisible.value = true
}

// 编辑时基础分类一级分类变化处理
const handleEditBasicPrimaryCategoryChange = async (categoryId: any) => {
  editForm.basic_secondary_category = null
  if (categoryId) {
    const selectedCategory = basicCategories.value.find((cat: any) => cat.id === categoryId)
    editBasicSecondaryCategories.value = selectedCategory?.children || []

    // 获取并设置资料准备信息
    const materialPreparation = await getCategoryMaterialPreparation(categoryId)
    editForm.material_preparation = materialPreparation
  } else {
    editBasicSecondaryCategories.value = []
    editForm.material_preparation = ''
  }
}

// 编辑时基础分类二级分类变化处理
const handleEditBasicSecondaryCategoryChange = (categoryIds: any[]) => {
  // 这里可以添加二级分类变化的逻辑
  console.log('编辑时基础二级分类变化:', categoryIds)
}



// 编辑文件上传前处理
const handleEditBeforeUpload = (file: any) => {
  // 检查文件类型
  const isImage = file.type.startsWith('image/')
  if (!isImage) {
    Message.error('只能上传图片文件!')
    return false
  }

  // 检查文件大小 (10MB)
  const isLt10M = file.size / 1024 / 1024 < 10
  if (!isLt10M) {
    Message.error('图片大小不能超过 10MB!')
    return false
  }

  return false // 阻止自动上传
}

// 编辑文件变化处理
const handleEditFileChange = (fileList: any) => {
  editFileList.value = fileList
  editHasNewFile.value = fileList.length > 0
}

// 保存编辑
const handleEditSave = async () => {
  try {
    // 如果有新文件，先上传新文件
    if (editHasNewFile.value && editFileList.value.length > 0) {
      const fileItem = editFileList.value[0]
      const file = fileItem.originFile || fileItem.file || fileItem

      if (!file || !(file instanceof File)) {
        Message.error('文件不存在或格式不正确')
        return
      }

      // 创建FormData上传新文件
      const formData = new FormData()
      formData.append('file', file)
      formData.append('title', editForm.title)
      formData.append('description', editForm.description || '')
      formData.append('material_preparation', editForm.material_preparation || '')
      // 处理基础分类
      const basicCategories = []
      if (editForm.basic_primary_category) {
        basicCategories.push(editForm.basic_primary_category)
      }
      if (editForm.basic_secondary_category) {
        basicCategories.push(editForm.basic_secondary_category)
      }
      formData.append('basic_categories', basicCategories.join(','))

      // 处理套件分类关联
      if (editForm.suite_secondary_category) {
        formData.append('suite_category_id', editForm.suite_secondary_category)
      }

      // 添加关联文案数据
      if (editForm.document_ids && editForm.document_ids.length > 0) {
        formData.append('document_ids', editForm.document_ids.join(','))
      }

      formData.append('is_public', editForm.is_public ? '1' : '0')
      formData.append('is_featured', editForm.is_featured ? '1' : '0')
      formData.append('replace_image_id', editForm.id) // 标记这是替换操作

      const uploadResponse = await uploadImage(formData)

      // 响应拦截器已经处理了success判断，如果到这里说明成功了
      if (uploadResponse.success || uploadResponse.success === undefined) {
        Message.success('图片替换成功')
        editModalVisible.value = false
        await fetchImageList()
      } else {
        Message.error(uploadResponse.msg || uploadResponse.message || '图片替换失败')
      }
    } else {
      // 只更新信息，不替换图片
      const updateData = {
        title: editForm.title,
        description: editForm.description,
        material_preparation: editForm.material_preparation,
        // 处理基础分类
        basic_categories: (() => {
          const categories = []
          if (editForm.basic_primary_category) categories.push(editForm.basic_primary_category)
          if (editForm.basic_secondary_category) categories.push(editForm.basic_secondary_category)
          return categories.join(',')
        })(),
        // 处理套件分类
        suite_category_id: editForm.suite_secondary_category || null,
        // 添加关联文案数据
        document_ids: editForm.document_ids || [],
        is_public: editForm.is_public ? 1 : 0,
        is_featured: editForm.is_featured ? 1 : 0
      }

      const response = await updateImage(editForm.id, updateData)

      // 响应拦截器已经处理了success判断，如果到这里说明成功了
      if (response.success || response.success === undefined) {
        Message.success('图片信息更新成功')
        editModalVisible.value = false
        await fetchImageList()
      } else {
        Message.error(response.msg || response.message || '更新失败')
      }
    }
  } catch (error) {
    console.error('更新图片信息失败:', error)
    Message.error('更新图片信息失败')
  }
}

// 状态切换
const handleStatusChange = async (record, value) => {
  try {
    record.is_public = value ? 1 : 0
    Message.success(`${value ? '公开' : '私有'}设置成功`)
  } catch (error) {
    console.error('更新图片状态失败:', error)
    Message.error('更新图片状态失败')
  }
}

// 删除图片（软删除）
const handleDelete = (record: any) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除图片"${record.title}"吗？删除后可在回收站中恢复。`,
    onOk: async () => {
      try {
        const response = await deleteImage(record.id)
        if (response.data.success) {
          Message.success('删除图片成功')
          fetchImageList()
        } else {
          Message.error(response.data.message || '删除失败')
        }
      } catch (error) {
        console.error('删除图片失败:', error)
        Message.error('删除图片失败')
      }
    }
  })
}

// 监听文件列表变化
watch(
  fileList,
  (newFileList, oldFileList) => {
    // 如果有新文件添加
    if (newFileList.length > oldFileList.length) {
      const newFile = newFileList[newFileList.length - 1]
      if (newFile && newFile.file && newFile.file.name) {
        // 自动设置标题为文件名（去掉扩展名）
        const filename = newFile.file.name
        const dotIndex = filename.lastIndexOf('.')
        const title = dotIndex > 0 ? filename.substring(0, dotIndex) : filename
        uploadForm.title = title
      }
    }
  },
  { deep: true }
)

// 获取基础分类
const fetchBasicCategories = async () => {
  try {
    const response = await request.get('/api/gallery/categories/basic')
    if (response && response.success) {
      // 后端返回的是树形结构，保存完整的树形结构
      // 但在下拉框中只显示一级分类（parent_id为null的）
      const allBasicCategories = response.data
      basicCategories.value = allBasicCategories.filter((category: any) => category.parent_id === null)
    }
  } catch (error) {
    console.error('获取基础分类失败:', error)
  }
}

// 获取套件分类列表
const fetchSuiteCategories = async () => {
  try {
    suiteCategoriesLoading.value = true
    const response = await request.get('/api/suite-categories/tree')
    if (response && response.success) {
      suitePrimaryCategories.value = response.data
    }
  } catch (error) {
    console.error('获取套件分类失败:', error)
  } finally {
    suiteCategoriesLoading.value = false
  }
}

// 处理套件一级分类变化
const handleSuitePrimaryCategoryChange = (categoryId: any) => {
  uploadForm.suite_secondary_category = null
  if (categoryId) {
    const primaryCategory = suitePrimaryCategories.value.find((cat: any) => cat.id === categoryId)
    currentSuiteSecondaryCategories.value = primaryCategory ? primaryCategory.children : []
  } else {
    currentSuiteSecondaryCategories.value = []
  }
}

// 处理编辑时套件一级分类变化
const handleEditSuitePrimaryCategoryChange = (categoryId: any) => {
  editForm.suite_secondary_category = null
  if (categoryId) {
    const primaryCategory = suitePrimaryCategories.value.find((cat: any) => cat.id === categoryId)
    editSuiteSecondaryCategories.value = primaryCategory ? primaryCategory.children : []
  } else {
    editSuiteSecondaryCategories.value = []
  }
}

// 获取可用文案
const fetchAvailableDocuments = async () => {
  documentsLoading.value = true
  try {
    const response = await request.get('/api/documents', {
      params: { pageSize: 1000 } // 获取所有文案
    })
    if (response && response.success) {
      availableDocuments.value = response.data.records || response.data.list || []
    }
  } catch (error) {
    console.error('获取文案列表失败:', error)
  } finally {
    documentsLoading.value = false
  }
}

// 获取分类的资料准备信息
const getCategoryMaterialPreparation = async (categoryId: any) => {
  if (!categoryId) return ''
  try {
    const response = await request.get(`/api/gallery/categories/${categoryId}`)
    if (response && response.success) {
      return response.data.description || ''
    }
  } catch (error) {
    console.error('获取分类资料准备信息失败:', error)
  }
  return ''
}

// 基础分类一级分类变化处理
const handleBasicPrimaryCategoryChange = async (categoryId: any) => {
  uploadForm.basic_secondary_category = null
  if (categoryId) {
    const selectedCategory = basicCategories.value.find((cat: any) => cat.id === categoryId)
    currentBasicSecondaryCategories.value = selectedCategory?.children || []

    // 获取并设置资料准备信息
    const materialPreparation = await getCategoryMaterialPreparation(categoryId)
    uploadForm.material_preparation = materialPreparation
  } else {
    currentBasicSecondaryCategories.value = []
    uploadForm.material_preparation = ''
  }
}

// 基础分类二级分类变化处理
const handleBasicSecondaryCategoryChange = async (categoryIds: any[]) => {
  // 如果选择了二级分类，使用最后选择的二级分类的资料准备信息
  if (categoryIds && categoryIds.length > 0) {
    const lastSelectedId = categoryIds[categoryIds.length - 1]
    const materialPreparation = await getCategoryMaterialPreparation(lastSelectedId)
    uploadForm.material_preparation = materialPreparation
  }
}



// 文案类型颜色
const getDocumentTypeColor = (type: string) => {
  const colorMap: Record<string, string> = {
    word: 'blue',
    excel: 'green',
    image: 'orange'
  }
  return colorMap[type] || 'gray'
}

// 文案类型名称
const getDocumentTypeName = (type: string) => {
  const nameMap: Record<string, string> = {
    word: 'Word',
    excel: 'Excel',
    image: '图片'
  }
  return nameMap[type] || type
}

// 跳转到套件分类页面
const goToSuiteCategory = () => {
  if (currentPreviewImage.value && currentPreviewImage.value.suite_categories && currentPreviewImage.value.suite_categories.length > 0) {
    // 找到套件的二级分类（如果有的话）
    const secondaryCategory = currentPreviewImage.value.suite_categories.find((cat: any) => cat.parent_id !== null)
    const primaryCategory = currentPreviewImage.value.suite_categories.find((cat: any) => cat.parent_id === null)

    // 构建跳转URL，优先使用二级分类，如果没有则使用一级分类
    const targetCategory = secondaryCategory || primaryCategory
    if (targetCategory) {
      // 跳转到首页并设置分类筛选
      const url = `/#/index?categoryId=${targetCategory.id}&categoryType=suite`
      window.open(url, '_blank')
    }
  }
}

// 下载文案
const downloadDocument = (document: any) => {
  if (document.remark) {
    window.open(document.remark, '_blank')
  } else {
    Message.warning('暂无下载地址')
  }
}

// 页面加载时获取数据
onMounted(() => {
  fetchCategories()
  fetchImageList()
  fetchBasicCategories()
  fetchSuiteCategories()
  fetchAvailableDocuments()
})

// 监听一级分类变化，更新二级分类选项
watch(
  () => searchForm.primaryCategory,
  (newValue) => {
    searchForm.secondaryCategory = ''
    if (newValue) {
      secondaryCategories.value = secondaryCategories.value.filter((cat) => cat.parent_id === newValue)
    } else {
      fetchCategories() // 重新获取所有二级分类
    }
  }
)
</script>

<style scoped>
.gallery-list {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0;
  color: #262626;
}

.header-actions {
  display: flex;
  align-items: center;
}

.thumbnail-wrapper {
  width: 60px;
  height: 60px;
  border-radius: 4px;
  overflow: hidden;
  cursor: pointer;
}

.thumbnail-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.2s;
}

.thumbnail-image:hover {
  transform: scale(1.1);
}

.title-cell {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.image-title {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.image-id {
  font-size: 12px;
  color: #86909c;
  background: #f2f3ff;
  padding: 2px 6px;
  border-radius: 8px;
  margin-left: 8px;
}

.file-size {
  font-size: 12px;
  color: #86909c;
  margin-top: 2px;
}

.upload-area {
  width: 100%;
  height: 120px;
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: border-color 0.3s;
}

.upload-area:hover {
  border-color: #165dff;
}

.preview-container {
  text-align: center;
}

.preview-image {
  max-width: 100%;
  max-height: 60vh;
  object-fit: contain;
  border-radius: 8px;
  margin-bottom: 16px;
}

.preview-info {
  text-align: left;
}

:deep(.arco-table-th) {
  background-color: #fafafa;
}

:deep(.arco-table-td) {
  border-bottom: 1px solid #f0f0f0;
}

/* 网盘地址样式 */
.netdisk-url a {
  color: #165dff;
  text-decoration: none;
  word-break: break-all;
}

.netdisk-url a:hover {
  text-decoration: underline;
}

.no-data {
  color: #86909c;
  font-style: italic;
}

.category-section {
  border: 1px solid #e5e6eb;
  border-radius: 6px;
  padding: 12px;
  background: #fafafa;
}

.document-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.document-title {
  flex: 1;
  margin-right: 8px;
}

.thumbnail-wrapper {
  position: relative;
  display: inline-block;
}

.suite-tag {
  position: absolute;
  top: 4px;
  right: 4px;
  z-index: 1;
}

.suite-categories {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.related-documents {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.document-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.document-name {
  flex: 1;
}

/* 预览弹窗样式 */
.preview-container {
  padding: 16px 0;
}

.preview-image-container {
  text-align: center;
  padding: 0 20px;
}

.image-wrapper {
  display: inline-block;
  max-width: 80%;
  text-align: center;
}

.preview-image {
  max-width: 100%;
  max-height: 400px;
  object-fit: contain;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.info-section {
  height: 100%;
}

.info-section h4 {
  margin: 0 0 8px 0;
  color: #1d2129;
  font-size: 14px;
  font-weight: 600;
  border-bottom: 1px solid #e5e6eb;
  padding-bottom: 4px;
}

.no-data {
  color: #86909c;
  font-style: italic;
  font-size: 12px;
}

.documents-list {
  max-width: 150px;
}

.documents-list .arco-tag {
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.material-preparation {
  font-size: 12px;
  line-height: 1.4;
}

.suite-info {
  font-size: 12px;
}

.document-link:hover {
  text-decoration: underline;
  color: #0e42d2;
}
</style>
