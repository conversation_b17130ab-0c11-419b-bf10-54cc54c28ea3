<template>
  <div class="template-category-container">
    <a-card title="模板分类管理" :bordered="false">
      <!-- 操作栏 -->
      <div class="action-bar">
        <a-space>
          <a-button type="primary" @click="showCreateModal">
            <template #icon>
              <icon-plus />
            </template>
            新增分类
          </a-button>
          <a-input-search
            v-model="searchKeyword"
            placeholder="搜索分类名称"
            style="width: 300px"
            @search="fetchCategoryList"
            @clear="fetchCategoryList"
            allow-clear
          />
        </a-space>
      </div>

      <!-- 分类表格 -->
      <a-table
        :columns="columns"
        :data="categoryData"
        :loading="loading"
        :pagination="pagination"
        @page-change="handlePageChange"
        @page-size-change="handlePageSizeChange"
        row-key="id"
      >


        <template #status="{ record }">
          <a-tag :color="record.status === 1 ? 'green' : 'red'">
            {{ record.status === 1 ? '启用' : '禁用' }}
          </a-tag>
        </template>

        <template #created_at="{ record }">
          {{ formatDateTime(record.created_at) }}
        </template>

        <template #actions="{ record }">
          <a-space size="mini">
            <a-button type="text" size="small" @click="showEditModal(record)">
              编辑
            </a-button>
            <a-button type="text" size="small" status="danger" @click="deleteCategory(record)">
              删除
            </a-button>
          </a-space>
        </template>
      </a-table>
    </a-card>

    <!-- 新增/编辑分类弹窗 -->
    <a-modal
      v-model:visible="modalVisible"
      :title="isEdit ? '编辑分类' : '新增分类'"
      width="500px"
      @ok="handleSubmit"
      @cancel="handleCancel"
    >
      <a-form ref="formRef" :model="form" :rules="rules" layout="vertical">
        <a-form-item label="分类名称" field="name" required>
          <a-input v-model="form.name" placeholder="请输入分类名称" />
        </a-form-item>

        <a-form-item label="排序" field="sort_order">
          <a-input-number
            v-model="form.sort_order"
            :min="0"
            :max="999"
            placeholder="排序值，数字越小越靠前"
            style="width: 100%"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { Message, Modal } from '@arco-design/web-vue'
import {
  getTemplateCategoryList,
  getTemplateCategoryTree,
  createTemplateCategory,
  updateTemplateCategory,
  deleteTemplateCategory,
  type TemplateCategory
} from '@/api/template-category'

// 响应式数据
const loading = ref(false)
const categoryData = ref<TemplateCategory[]>([])
const searchKeyword = ref('')
const modalVisible = ref(false)
const isEdit = ref(false)
const formRef = ref()

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0,
  showTotal: true,
  showPageSize: true
})

// 表单数据
const form = reactive({
  id: null as number | null,
  name: '',
  sort_order: 0
})

// 表格列配置
const columns = [
  {
    title: '分类名称',
    dataIndex: 'name',
    width: 200
  },
  {
    title: '排序',
    dataIndex: 'sort_order',
    width: 100
  },
  {
    title: '状态',
    dataIndex: 'status',
    slotName: 'status',
    width: 100
  },
  {
    title: '创建时间',
    dataIndex: 'created_at',
    slotName: 'created_at',
    width: 150
  },
  {
    title: '操作',
    slotName: 'actions',
    width: 150,
    fixed: 'right'
  }
]

// 表单验证规则
const rules = {
  name: [{ required: true, message: '请输入分类名称' }]
}

// 获取分类列表
const fetchCategoryList = async () => {
  try {
    loading.value = true
    const response = await getTemplateCategoryList({
      page: pagination.current,
      pageSize: pagination.pageSize,
      keyword: searchKeyword.value
    })

    if (response.success) {
      categoryData.value = response.data.list
      pagination.total = response.data.total
    }
  } catch (error) {
    console.error('获取分类列表失败:', error)
    Message.error('获取分类列表失败')
  } finally {
    loading.value = false
  }
}



// 显示新增弹窗
const showCreateModal = () => {
  isEdit.value = false
  resetForm()
  modalVisible.value = true
}

// 显示编辑弹窗
const showEditModal = (record: TemplateCategory) => {
  isEdit.value = true
  form.id = record.id
  form.name = record.name
  form.sort_order = record.sort_order
  modalVisible.value = true
}

// 重置表单
const resetForm = () => {
  form.id = null
  form.name = ''
  form.sort_order = 0
}

// 提交表单
const handleSubmit = async () => {
  try {
    const errors = await formRef.value?.validate()
    if (errors) return

    if (isEdit.value) {
      const response = await updateTemplateCategory(form.id!, {
        name: form.name,
        sort_order: form.sort_order
      })
      if (response.data.success) {
        Message.success('更新分类成功')
        modalVisible.value = false
        fetchCategoryList()
      }
    } else {
      const response = await createTemplateCategory({
        name: form.name,
        sort_order: form.sort_order
      })
      if (response.data.success) {
        Message.success('创建分类成功')
        modalVisible.value = false
        fetchCategoryList()
      }
    }
  } catch (error) {
    console.error('提交失败:', error)
    Message.error('操作失败')
  }
}

// 取消操作
const handleCancel = () => {
  modalVisible.value = false
  resetForm()
}

// 删除分类
const deleteCategory = (record: TemplateCategory) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除分类"${record.name}"吗？此操作不可恢复。`,
    onOk: async () => {
      try {
        const response = await deleteTemplateCategory(record.id)
        if (response.data.success) {
          Message.success('删除成功')
          fetchCategoryList()
        }
      } catch (error) {
        console.error('删除失败:', error)
        Message.error('删除失败')
      }
    }
  })
}

// 分页处理
const handlePageChange = (page: number) => {
  pagination.current = page
  fetchCategoryList()
}

const handlePageSizeChange = (pageSize: number) => {
  pagination.pageSize = pageSize
  pagination.current = 1
  fetchCategoryList()
}

// 格式化时间
const formatDateTime = (dateString: string) => {
  if (!dateString) return '-'
  const date = new Date(dateString)
  if (isNaN(date.getTime())) return '-'
  
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  
  return `${year}-${month}-${day}`
}

// 初始化
onMounted(() => {
  fetchCategoryList()
})
</script>

<style scoped>
.template-category-container {
  padding: 20px;
}

.action-bar {
  margin-bottom: 20px;
}
</style>
