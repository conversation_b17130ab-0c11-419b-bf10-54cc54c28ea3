<template>
  <div class="suite-category">
    <div class="page-header">
      <h1>套件管理</h1>
      <div class="header-actions">
        <a-button type="primary" @click="showCreateModal">
          <template #icon>
            <icon-plus />
          </template>
          新增套件
        </a-button>
      </div>
    </div>

    <!-- 套件树形表格 -->
    <a-card>
      <a-table
        :columns="columns"
        :data="categoryData"
        :pagination="false"
        :loading="loading"
        row-key="id"
        :default-expand-all-rows="true"
        size="medium"
      >
        <template #status="{ record }">
          <a-switch
            :model-value="record.is_visible === 1"
            @change="(value) => handleStatusChange(record, value)"
            size="small"
          />
        </template>

        <template #created_at="{ record }">
          <div style="font-size: 12px; color: #86909c;">
            {{ formatDateTime(record.created_at) }}
          </div>
        </template>

        <template #actions="{ record }">
          <a-space size="mini">
            <a-button type="text" size="mini" @click="showEditModal(record)">
              编辑
            </a-button>
            <a-button type="text" size="mini" @click="showCreateModal(record)">
              添加子套件
            </a-button>
            <a-popconfirm
              content="确定要删除这个套件吗？"
              @ok="handleDelete(record)"
            >
              <a-button type="text" size="mini" status="danger">
                删除
              </a-button>
            </a-popconfirm>
          </a-space>
        </template>
      </a-table>
    </a-card>

    <!-- 创建/编辑套件弹窗 -->
    <a-modal
      v-model:visible="modalVisible"
      :title="isEdit ? '编辑套件' : '新增套件'"
      @ok="handleSubmit"
      @cancel="handleCancel"
      :ok-loading="submitLoading"
    >
      <a-form :model="form" layout="vertical">
        <a-form-item label="套件名称" required>
          <a-input v-model="form.name" placeholder="请输入套件名称" />
        </a-form-item>

        <a-form-item label="套件描述">
          <a-textarea v-model="form.description" placeholder="请输入套件描述" :rows="3" />
        </a-form-item>

        <a-form-item label="父套件">
          <a-tree-select
            v-model="form.parent_id"
            :data="parentCategoryOptions"
            placeholder="请选择父套件（不选则为顶级套件）"
            allow-clear
            :field-names="{ key: 'id', title: 'name', children: 'children' }"
          />
        </a-form-item>

        <a-form-item label="排序权重">
          <a-input-number v-model="form.sort_order" placeholder="数字越小排序越靠前" :min="0" />
        </a-form-item>

        <a-form-item label="是否显示">
          <a-switch v-model="form.is_visible" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { Message, Modal } from '@arco-design/web-vue'
import { IconPlus } from '@arco-design/web-vue/es/icon'
import { request } from '@/utils/request'

// 响应式数据
const loading = ref(false)
const categoryData = ref([])
const modalVisible = ref(false)
const isEdit = ref(false)
const submitLoading = ref(false)
const parentCategoryOptions = ref([])

// 表格列配置
const columns = [
  {
    title: '套件名称',
    dataIndex: 'name',
    width: 200,
    ellipsis: true,
    tooltip: true
  },
  {
    title: '描述',
    dataIndex: 'description',
    ellipsis: true,
    tooltip: true
  },
  {
    title: '排序',
    dataIndex: 'sort_order',
    width: 80,
    align: 'center'
  },
  {
    title: '状态',
    dataIndex: 'is_visible',
    slotName: 'status',
    width: 80,
    align: 'center'
  },
  {
    title: '创建时间',
    dataIndex: 'created_at',
    slotName: 'created_at',
    width: 180
  },
  {
    title: '操作',
    slotName: 'actions',
    width: 200,
    align: 'center'
  }
]

// 表单数据
const form = reactive({
  id: null,
  name: '',
  description: '',
  parent_id: null,
  sort_order: 0,
  is_visible: true
})

// 获取套件列表
const fetchCategories = async () => {
  try {
    loading.value = true
    const response = await request.get('/api/suite-categories')
    if (response.success) {
      categoryData.value = buildCategoryTree(response.data.records)
    }
  } catch (error) {
    console.error('获取套件列表失败:', error)
    Message.error('获取套件列表失败')
  } finally {
    loading.value = false
  }
}

// 构建套件树
const buildCategoryTree = (categories: any[]) => {
  const categoryMap = new Map()
  const rootCategories: any[] = []

  // 创建套件映射
  categories.forEach(category => {
    categoryMap.set(category.id, { ...category, children: [] })
  })

  // 构建树形结构
  categories.forEach(category => {
    if (category.parent_id) {
      const parent = categoryMap.get(category.parent_id)
      if (parent) {
        parent.children.push(categoryMap.get(category.id))
      }
    } else {
      rootCategories.push(categoryMap.get(category.id))
    }
  })

  return rootCategories
}

// 获取父套件选项
const fetchParentCategoryOptions = async () => {
  try {
    const response = await request.get('/api/suite-categories/tree')
    if (response.success) {
      parentCategoryOptions.value = response.data
    }
  } catch (error) {
    console.error('获取父套件选项失败:', error)
  }
}

// 显示创建弹窗
const showCreateModal = (parentCategory?: any) => {
  isEdit.value = false
  resetForm()
  if (parentCategory) {
    form.parent_id = parentCategory.id
  }
  modalVisible.value = true
  fetchParentCategoryOptions()
}

// 显示编辑弹窗
const showEditModal = (record: any) => {
  isEdit.value = true
  form.id = record.id
  form.name = record.name
  form.description = record.description || ''
  form.parent_id = record.parent_id
  form.sort_order = record.sort_order || 0
  form.is_visible = record.is_visible === 1
  modalVisible.value = true
  fetchParentCategoryOptions()
}

// 重置表单
const resetForm = () => {
  form.id = null
  form.name = ''
  form.description = ''
  form.parent_id = null
  form.sort_order = 0
  form.is_visible = true
}

// 提交表单
const handleSubmit = async () => {
  if (!form.name.trim()) {
    Message.error('请输入套件名称')
    return
  }

  try {
    submitLoading.value = true
    const data = {
      name: form.name.trim(),
      description: form.description.trim(),
      parent_id: form.parent_id || null,
      sort_order: form.sort_order || 0,
      is_visible: form.is_visible ? 1 : 0
    }

    if (isEdit.value) {
      await request.put(`/api/suite-categories/${form.id}`, data)
      Message.success('更新套件成功')
    } else {
      await request.post('/api/suite-categories', data)
      Message.success('创建套件成功')
    }

    modalVisible.value = false
    fetchCategories()
  } catch (error) {
    console.error('提交失败:', error)
    Message.error(isEdit.value ? '更新套件失败' : '创建套件失败')
  } finally {
    submitLoading.value = false
  }
}

// 取消弹窗
const handleCancel = () => {
  modalVisible.value = false
  resetForm()
}

// 状态切换
const handleStatusChange = async (record: any, value: boolean) => {
  try {
    await request.put(`/api/suite-categories/${record.id}`, {
      is_visible: value ? 1 : 0
    })
    record.is_visible = value ? 1 : 0
    Message.success(`${value ? '显示' : '隐藏'}设置成功`)
  } catch (error) {
    console.error('状态切换失败:', error)
    Message.error('状态切换失败')
  }
}

// 删除套件
const handleDelete = async (record: any) => {
  try {
    await request.delete(`/api/suite-categories/${record.id}`)
    Message.success('删除套件成功')
    fetchCategories()
  } catch (error) {
    console.error('删除套件失败:', error)
    Message.error('删除套件失败')
  }
}

// 格式化日期时间
const formatDateTime = (dateTime: string) => {
  if (!dateTime) return '-'
  const date = new Date(dateTime)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 页面加载时获取数据
onMounted(() => {
  fetchCategories()
})
</script>

<style scoped>
.suite-category {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #1d2129;
}

.header-actions {
  display: flex;
  gap: 12px;
}
</style>
