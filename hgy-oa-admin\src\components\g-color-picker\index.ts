import GColorPickerPanel from './g-color-picker-panel.vue';
import GColorPicker from './g-color-picker.vue';
// import withInstall from '@/utils/withInstall';
// import { TdColorPickerProps } from './type';
//
// import './style';
//
// export * from './type';
// export type ColorPickerProps = TdColorPickerProps;
// export type ColorPickerPanelProps = TdColorPickerProps;
//
// export const GColorPickerPanel = withInstall(_ColorPickerPanel);
// export const GColorPicker = withInstall(_ColorPicker);


export { GColorPicker, GColorPickerPanel }
