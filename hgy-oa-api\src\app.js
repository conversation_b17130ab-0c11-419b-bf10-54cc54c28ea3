import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import rateLimit from 'express-rate-limit';
import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';
import { corsOptions } from './config/cors.js';

// 导入路由
import templateRoutes from './routes/template.js';
import templateCategoryRoutes from './routes/template-category.js';
import materialRoutes from './routes/material.js';
import fontRoutes from './routes/font.js';
import uploadRoutes from './routes/upload.js';
import graphRoutes from './routes/graph.js';
import elementRoutes from './routes/element.js';
import backgroundRoutes from './routes/background.js';
import userRoutes from './routes/user.js';
import galleryRoutes from './routes/gallery.js';
import displayRoutes from './routes/display.js';
import documentsRoutes from './routes/documents.js';
import suiteCategoriesRoutes from './routes/suite-categories.js';
import departmentRoutes from './routes/department.js';
import designRoutes from './routes/design.js';
import roleRoutes from './routes/role.js';
import authRoutes from './routes/auth.js';
import projectRoutes from './routes/project.js';
import suiteRoutes from './routes/suites.js';

// 导入中间件
import { errorHandler } from './middleware/errorHandler.js';
import { responseFormatter } from './middleware/responseFormatter.js';

// 获取当前文件的目录路径
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 加载环境变量
dotenv.config({ path: path.join(__dirname, '../.env') });

const app = express();
const PORT = process.env.PORT || 3001;

// 安全中间件
app.use(helmet());

// 压缩中间件
app.use(compression());

// 限流中间件
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 1000, // 限制每个IP 15分钟内最多1000个请求
  message: {
    success: false,
    code: 429,
    msg: '请求过于频繁，请稍后再试',
    data: null
  }
});
app.use(limiter);

// CORS配置
app.use(cors(corsOptions));

// 解析JSON和URL编码的请求体
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// 静态文件服务
app.use('/uploads', express.static(path.join(__dirname, '../uploads')));

// 响应格式化中间件
app.use(responseFormatter);

// API路由
const apiPrefix = process.env.API_PREFIX || '/api';
app.use(`${apiPrefix}/template`, templateRoutes);
app.use(`${apiPrefix}/template-category`, templateCategoryRoutes);
app.use(`${apiPrefix}/material`, materialRoutes);
app.use(`${apiPrefix}/text`, materialRoutes);
app.use(`${apiPrefix}/image`, materialRoutes);
app.use(`${apiPrefix}/font`, fontRoutes);
app.use(`${apiPrefix}/oss`, uploadRoutes);
app.use(`${apiPrefix}/graph`, graphRoutes);
app.use(`${apiPrefix}/element`, elementRoutes);
app.use(`${apiPrefix}/background`, backgroundRoutes);
app.use(`${apiPrefix}/user`, userRoutes);
app.use(`${apiPrefix}/gallery`, galleryRoutes);
app.use(`${apiPrefix}/display`, displayRoutes);
app.use(`${apiPrefix}/documents`, documentsRoutes);
app.use(`${apiPrefix}/suite-categories`, suiteCategoriesRoutes);
app.use(`${apiPrefix}/department`, departmentRoutes);
app.use(`${apiPrefix}/design`, designRoutes);
app.use(`${apiPrefix}/role`, roleRoutes);
app.use(`${apiPrefix}/auth`, authRoutes);
app.use(`${apiPrefix}/project`, projectRoutes);
app.use(`${apiPrefix}/suites`, suiteRoutes);

// 健康检查端点
app.get('/health', (req, res) => {
  res.success({
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});

// 404处理
app.use('*', (req, res) => {
  res.status(404).error('API端点不存在', 404);
});

// 错误处理中间件
app.use(errorHandler);

// 启动服务器
app.listen(PORT, () => {
  console.log(`🚀 服务器运行在 http://localhost:${PORT}`);
  console.log(`📝 API文档: http://localhost:${PORT}${apiPrefix}`);
  console.log(`🏥 健康检查: http://localhost:${PORT}/health`);
});

export default app;
