import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import { Message } from '@arco-design/web-vue'

// 创建axios实例
const request: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001',
  timeout: 60000,
  headers: {
    'Content-Type': 'application/json'
  }
})
// 请求拦截器
request.interceptors.request.use(
  (config: AxiosRequestConfig) => {
    // 添加用户认证信息
    const userInfo = localStorage.getItem('userInfo')
    if (userInfo) {
      try {
        const user = JSON.parse(userInfo)
        if (user.username) {
          config.headers['x-username'] = user.username
        }
      } catch (error) {
        console.error('解析用户信息失败:', error)
      }
    }

    return config
  },
  (error) => {
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

export interface HttpResponse<T = unknown> {
    success: boolean; // 是否成功
    code: number; // 状态码
    msg: string; // 状态信息
    data: T; // 返回数据
    timestamp: string; // 时间戳
}

// 响应拦截器
request.interceptors.response.use(
  (response: AxiosResponse) => {
    const { data } = response

    // 二进制数据则直接返回
    if (
      response.request.responseType === 'blob' ||
      response.request.responseType === 'arraybuffer'
    ) {
      return response
    }

    // 如果是成功响应，直接返回数据
    if (data.success) {
      return data
    }

    // 如果是失败响应，显示错误信息
    Message.error({
      content: data.msg || '请求失败',
      duration: 3000,
    })

    // 处理401未授权
    if (data.code === 401) {
      // 可以在这里处理登录跳转
      // router.replace('/login')
    }

    return Promise.reject(new Error(data.msg || '请求失败'))
  },
  (error) => {
    console.error('响应错误:', error)

    let errorMessage = '网络错误'

    if (error.response) {
      const { status, data } = error.response

      switch (status) {
        case 400:
          errorMessage = data.msg || '请求参数错误'
          break
        case 401:
          errorMessage = '未授权，请重新登录'
          break
        case 403:
          errorMessage = '拒绝访问'
          break
        case 404:
          errorMessage = '请求的资源不存在'
          break
        case 500:
          errorMessage = '服务器内部错误'
          break
        default:
          errorMessage = data.msg || `请求失败 (${status})`
      }
    } else if (error.request) {
      errorMessage = '网络连接失败'
    }

    Message.error({
      content: errorMessage,
      duration: 3000,
    })

    return Promise.reject(error)
  }
)

export { request }
