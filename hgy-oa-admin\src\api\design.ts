import { request } from '@/utils/request'

// 设计任务相关接口类型定义
export type TaskType = '设计' | '视频'
export type TaskStatus = '待处理' | '处理中' | '已完成' | '已取消'

export interface DesignTask {
  id: number
  task_name: string
  task_type: TaskType
  assigner: string
  department: string
  assign_time: string
  delivery_time: string
  status: TaskStatus
  rating: number
  assignee?: string
  complete_time?: string
  storage_location?: string
  creator: string
  remarks?: string
  create_time: string
  updated_at: string
}

export interface CreateTaskParams {
  task_name: string
  task_type: TaskType
  assigner: string
  department: string
  assign_time: string
  delivery_time: string
  status?: TaskStatus
  rating?: number
  assignee?: string
  storage_location?: string
  creator: string
  remarks?: string
}

export interface UpdateTaskParams {
  task_name: string
  task_type: string
  assigner: string
  department: string
  assign_time: string
  delivery_time: string
  status: string
  rating: number
  assignee?: string
  complete_time?: string
  storage_location?: string
  creator: string
  remarks?: string
}

export interface TaskListParams {
  pageNum?: number
  pageSize?: number
  keyword?: string
  taskType?: string
  status?: string
  department?: string
}

export interface TaskListResponse {
  records: DesignTask[]
  total: number
  pageNum: number
  pageSize: number
}

// 设计任务管理API
export const getTaskList = (params: TaskListParams) => {
  return request.get('/api/design/tasks', { params })
}

export const getTaskDetail = (id: number) => {
  return request.get(`/api/design/tasks/${id}`)
}

export const createTask = (data: CreateTaskParams) => {
  return request.post('/api/design/tasks', data)
}

export const updateTask = (id: number, data: UpdateTaskParams) => {
  return request.put(`/api/design/tasks/${id}`, data)
}

export const deleteTask = (id: number) => {
  return request.delete(`/api/design/tasks/${id}`)
}

export const batchDeleteTasks = (ids: number[]) => {
  return request.post('/api/design/tasks/batch-delete', { ids })
}
