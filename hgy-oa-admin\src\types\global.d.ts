// 全局类型声明文件

declare module '@/utils/request' {
  import { AxiosInstance } from 'axios'
  export const request: AxiosInstance
}

declare module '@/api/design' {
  export type TaskType = '设计' | '视频'
  export type TaskStatus = '待处理' | '处理中' | '已完成' | '已取消'
  
  export interface DesignTask {
    id: number
    task_name: string
    task_type: TaskType
    assigner: string
    department: string
    assign_time: string
    delivery_time: string
    status: TaskStatus
    rating: number
    assignee?: string
    complete_time?: string
    storage_location?: string
    creator: string
    remarks?: string
    create_time: string
    updated_at: string
  }
  
  export interface TaskListParams {
    pageNum: number
    pageSize: number
    keyword?: string
    taskType?: string
    status?: string
    department?: string
  }
  
  export const getTaskList: (params: TaskListParams) => Promise<any>
  export const createTask: (data: any) => Promise<any>
  export const updateTask: (id: number, data: any) => Promise<any>
  export const deleteTask: (id: number) => Promise<any>
}

declare module '@/api/user' {
  export const getUserList: (params?: any) => Promise<any>
  export const getCurrentUser: () => Promise<any>
}

declare module '@/api/gallery' {
  export const getAllDepartments: () => Promise<any>
}

declare module '@/utils/pinyin-filter' {
  export const createSelectFilter: (labelKey?: string) => (inputValue: string, option: any) => boolean
}
