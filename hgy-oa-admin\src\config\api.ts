/**
 * API配置文件
 * 统一管理API地址配置
 */

// 获取API基础地址
export const getApiBaseUrl = (): string => {
  return import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001'
}

// 获取完整的API地址
export const getApiUrl = (path: string): string => {
  const baseUrl = getApiBaseUrl()
  const cleanPath = path.startsWith('/') ? path : `/${path}`
  return `${baseUrl}${cleanPath}`
}

// 常用API地址
export const API_ENDPOINTS = {
  // 认证相关
  LOGIN: '/api/auth/login',
  LOGOUT: '/api/auth/logout',
  
  // 用户相关
  USER_LIST: '/api/user/list',
  USER_INFO: '/api/user/info',
  
  // 模板相关
  TEMPLATE_LIST: '/api/template/templateList',
  TEMPLATE_SAVE: '/api/template/save',
  TEMPLATE_USER_LIST: '/api/template/user/list',
  
  // 字体相关
  FONT_LIST: '/api/font/list',
  
  // 素材相关
  MATERIAL_CATEGORIES: '/api/material/categories',
  MATERIAL_LIST: '/api/material/list',

  // 部门相关
  DEPARTMENT_LIST: '/api/department/list',
  DEPARTMENT_CREATE: '/api/department',

  // 文档相关
  DOCUMENT_DOWNLOAD: '/api/documents',
  
  // 文件上传
  UPLOAD: '/api/oss/upload',
} as const

// 获取完整的API端点地址
export const getEndpointUrl = (endpoint: keyof typeof API_ENDPOINTS): string => {
  return getApiUrl(API_ENDPOINTS[endpoint])
}

// 默认导出配置对象
export default {
  getApiBaseUrl,
  getApiUrl,
  getEndpointUrl,
  API_ENDPOINTS
}
