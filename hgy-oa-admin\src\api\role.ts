import { request } from '@/utils/request'

// 角色相关接口类型定义
export interface Role {
  id: number
  name: string
  description: string
  allowedNavs: NavPermission[]
  userCount: number
  createTime: string
}

export interface NavPermission {
  key: string
  name: string
}

export interface CreateRoleParams {
  name: string
  description: string
  allowedNavs: string[]
}

export interface UpdateRoleParams {
  name: string
  description: string
  allowedNavs: string[]
}

export interface RoleListParams {
  pageNum?: number
  pageSize?: number
  keyword?: string
}

// 获取角色列表
export const getRoleList = (params: RoleListParams) => {
  return request.get('/api/role/list', { params })
}

// 获取角色详情
export const getRoleDetail = (id: number) => {
  return request.get(`/api/role/${id}`)
}

// 创建角色
export const createRole = (data: CreateRoleParams) => {
  return request.post('/api/role', data)
}

// 更新角色
export const updateRole = (id: number, data: UpdateRoleParams) => {
  return request.put(`/api/role/${id}`, data)
}

// 删除角色
export const deleteRole = (id: number) => {
  return request.delete(`/api/role/${id}`)
}

// 批量删除角色
export const batchDeleteRoles = (ids: number[]) => {
  return request.post('/api/role/batch-delete', { ids })
}

// 获取可用的导航菜单
export const getAvailableNavs = () => {
  return request.get('/api/role/available-navs')
}

// 更新角色导航权限
export const updateRoleNavPermissions = (id: number, allowedNavs: string[]) => {
  return request.put(`/api/role/${id}/nav-permissions`, { allowedNavs })
}

// 获取用户角色的导航权限
export const getUserNavPermissions = (userId?: number) => {
  const url = userId ? `/api/role/user/${userId}/nav-permissions` : '/api/role/current-user/nav-permissions'
  return request.get(url)
}
