<template>
  <div class="main-layout">
    <!-- 顶部导航 -->
    <header class="main-header">
      <div class="header-container">
        <!-- 左侧Logo -->
        <div class="header-left">
          <router-link to="/index" class="logo-link">
            <img src="/logo.png" alt="红果设计" class="logo-img" />
            <span class="logo-text">红果设计</span>
          </router-link>
        </div>

        <!-- 中间导航 -->
        <nav class="header-nav">
          <router-link
            v-for="item in allowedNavItems"
            :key="item.path"
            :to="item.path"
            class="nav-item"
            :class="{ active: isNavActive(item.path) }"
          >
            <component :is="item.icon" class="nav-icon" />
            <span>{{ item.name }}</span>
          </router-link>
        </nav>

        <!-- 右侧用户信息 -->
        <div class="header-right">
          <a-dropdown @select="handleUserAction">
            <div class="user-info">
              <a-avatar :size="32" class="user-avatar">
                {{ userInfo?.username?.charAt(0)?.toUpperCase() }}
              </a-avatar>
              <span class="username">{{ userInfo?.username }}</span>
              <icon-down class="dropdown-icon" />
            </div>
            <template #content>
              <a-doption value="profile">
                <icon-user />
                个人资料
              </a-doption>
              <a-doption value="settings">
                <icon-settings />
                设置
              </a-doption>
              <a-divider style="margin: 4px 0;" />
              <a-doption value="logout">
                <icon-export />
                退出登录
              </a-doption>
            </template>
          </a-dropdown>
        </div>
      </div>
    </header>

    <!-- 主内容区域 -->
    <main class="main-content">
      <router-view />
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { Modal, Message } from '@arco-design/web-vue'
import {
  IconHome,
  IconEdit,
  IconSettings,
  IconDown,
  IconUser,
  IconExport,
  IconDesktop,
  IconApps
} from '@arco-design/web-vue/es/icon'

const router = useRouter()
const route = useRoute()

// 用户信息
const userInfo = ref(null)

// 所有可用的导航菜单
const allNavItems = [
  {
    key: 'index',
    name: '图片演示站',
    path: '/index',
    icon: IconHome
  },
  {
    key: 'editor',
    name: '图片制作站',
    path: '/editor',
    icon: IconEdit
  },
  {
    key: 'design',
    name: '设计工作表',
    path: '/design',
    icon: IconDesktop
  },
  {
    key: 'product',
    name: '项目开发表',
    path: '/product',
    icon: IconApps
  },
  {
    key: 'admin',
    name: '管理',
    path: '/admin',
    icon: IconSettings
  }
]

// 角色导航权限配置
const roleNavPermissions: Record<string, string[]> = {
  'admin': ['index', 'editor', 'design', 'product', 'admin'],
  'manager': ['index', 'editor', 'design', 'product'],
  'designer': ['index', 'editor', 'design'],
  'developer': ['index', 'editor', 'product'],
  'salesman': ['index'],
  'guest': ['index'],
  'user': ['index', 'editor', 'product']
}

// 根据用户角色过滤导航菜单
const allowedNavItems = computed(() => {
  if (!userInfo.value) {
    // 未登录用户只能访问演示站
    return allNavItems.filter(item => item.key === 'index')
  }

  const userRole = userInfo.value.role || 'user'
  const allowedKeys = roleNavPermissions[userRole] || ['index']

  return allNavItems.filter(item => allowedKeys.includes(item.key))
})

// 判断导航是否激活
const isNavActive = (path: string) => {
  if (path === '/admin') {
    // 管理页面：所有以/admin开头的路径都激活管理导航
    return route.path.startsWith('/admin')
  }
  return route.path === path
}

// 获取用户信息
const getUserInfo = () => {
  const userInfoStr = localStorage.getItem('userInfo')
  if (userInfoStr) {
    userInfo.value = JSON.parse(userInfoStr)
  }
}

// 用户操作处理
const handleUserAction = (value: string) => {
  switch (value) {
    case 'profile':
      Message.info('个人资料功能开发中...')
      break
    case 'settings':
      Message.info('设置功能开发中...')
      break
    case 'logout':
      handleLogout()
      break
  }
}

// 退出登录
const handleLogout = () => {
  Modal.confirm({
    title: '确认退出',
    content: '确定要退出登录吗？',
    onOk: () => {
      // 清除用户信息
      localStorage.removeItem('userInfo')
      localStorage.removeItem('isLoggedIn')
      
      Message.success('已退出登录')
      
      // 跳转到登录页
      router.push('/login')
    }
  })
}

onMounted(() => {
  getUserInfo()
})
</script>

<style scoped>
.main-layout {
  min-height: 100vh;
  background: #f7f8fa;
}

.main-header {
  background: white;
  border-bottom: 1px solid #e5e6eb;
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.header-container {
  margin: 0 auto;
  padding: 0 24px;
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header-left {
  flex-shrink: 0;
}

.logo-link {
  display: flex;
  align-items: center;
  gap: 12px;
  text-decoration: none;
  color: inherit;
}

.logo-img {
  width: 36px;
  height: 36px;
  border-radius: 8px;
}

.logo-text {
  font-size: 20px;
  font-weight: 600;
  color: #1d2129;
}

.header-nav {
  display: flex;
  align-items: center;
  gap: 8px;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: 8px;
  text-decoration: none;
  color: #4e5969;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.nav-item:hover {
  background: #f2f3ff;
  color: #165dff;
}

.nav-item.active {
  background: #e6f7ff;
  color: #165dff;
}

.nav-icon {
  font-size: 16px;
}

.header-right {
  flex-shrink: 0;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 12px 4px 4px;
  border-radius: 20px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.user-info:hover {
  background: #f2f3ff;
}

.user-avatar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-weight: 600;
}

.username {
  font-size: 14px;
  font-weight: 500;
  color: #1d2129;
}

.dropdown-icon {
  font-size: 12px;
  color: #86909c;
  transition: transform 0.2s ease;
}

.user-info:hover .dropdown-icon {
  transform: rotate(180deg);
}

.main-content {
  min-height: calc(100vh - 64px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-container {
    padding: 0 16px;
  }
  
  .header-nav {
    gap: 4px;
  }
  
  .nav-item {
    padding: 6px 12px;
    font-size: 13px;
  }
  
  .nav-item span {
    display: none;
  }
  
  .logo-text {
    display: none;
  }
}

:deep(.arco-dropdown-option) {
  display: flex;
  align-items: center;
  gap: 8px;
}
</style>
