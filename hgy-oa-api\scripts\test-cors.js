#!/usr/bin/env node

/**
 * CORS配置测试脚本
 * 用于验证CORS配置是否正确
 */

import fetch from 'node-fetch';

const API_BASE_URL = process.env.API_BASE_URL || 'http://localhost:3001';
const FRONTEND_URL = process.env.FRONTEND_URL || 'http://localhost:5173';

console.log('🧪 开始CORS配置测试...\n');
console.log(`API地址: ${API_BASE_URL}`);
console.log(`前端地址: ${FRONTEND_URL}\n`);

// 测试健康检查端点
const testHealthCheck = async () => {
  console.log('📋 测试健康检查端点...');
  
  try {
    const response = await fetch(`${API_BASE_URL}/health`, {
      method: 'GET',
      headers: {
        'Origin': FRONTEND_URL
      }
    });
    
    console.log(`状态码: ${response.status}`);
    console.log('响应头:');
    
    const corsHeaders = [
      'access-control-allow-origin',
      'access-control-allow-credentials',
      'access-control-allow-methods',
      'access-control-allow-headers'
    ];
    
    corsHeaders.forEach(header => {
      const value = response.headers.get(header);
      if (value) {
        console.log(`  ${header}: ${value}`);
      } else {
        console.log(`  ${header}: ❌ 缺失`);
      }
    });
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ 健康检查成功');
      console.log(`服务状态: ${data.status}`);
    } else {
      console.log('❌ 健康检查失败');
    }
    
  } catch (error) {
    console.error('❌ 健康检查请求失败:', error.message);
  }
  
  console.log('');
};

// 测试预检请求
const testPreflightRequest = async () => {
  console.log('🔍 测试预检请求 (OPTIONS)...');
  
  try {
    const response = await fetch(`${API_BASE_URL}/api/auth/login`, {
      method: 'OPTIONS',
      headers: {
        'Origin': FRONTEND_URL,
        'Access-Control-Request-Method': 'POST',
        'Access-Control-Request-Headers': 'Content-Type,x-username'
      }
    });
    
    console.log(`状态码: ${response.status}`);
    
    const allowOrigin = response.headers.get('access-control-allow-origin');
    const allowMethods = response.headers.get('access-control-allow-methods');
    const allowHeaders = response.headers.get('access-control-allow-headers');
    
    if (allowOrigin === FRONTEND_URL || allowOrigin === '*') {
      console.log('✅ Origin允许正确');
    } else {
      console.log(`❌ Origin不匹配: ${allowOrigin}`);
    }
    
    if (allowMethods && allowMethods.includes('POST')) {
      console.log('✅ POST方法允许');
    } else {
      console.log(`❌ POST方法不允许: ${allowMethods}`);
    }
    
    if (allowHeaders && allowHeaders.includes('Content-Type')) {
      console.log('✅ Content-Type头部允许');
    } else {
      console.log(`❌ Content-Type头部不允许: ${allowHeaders}`);
    }
    
  } catch (error) {
    console.error('❌ 预检请求失败:', error.message);
  }
  
  console.log('');
};

// 测试实际API请求
const testApiRequest = async () => {
  console.log('🌐 测试实际API请求...');
  
  try {
    const response = await fetch(`${API_BASE_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Origin': FRONTEND_URL,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        username: 'test',
        password: 'test'
      })
    });
    
    console.log(`状态码: ${response.status}`);
    
    const allowOrigin = response.headers.get('access-control-allow-origin');
    if (allowOrigin) {
      console.log(`✅ CORS头部存在: ${allowOrigin}`);
    } else {
      console.log('❌ 缺少CORS头部');
    }
    
    // 不检查响应内容，只检查CORS
    console.log('✅ API请求CORS检查完成');
    
  } catch (error) {
    console.error('❌ API请求失败:', error.message);
  }
  
  console.log('');
};

// 运行所有测试
const runTests = async () => {
  await testHealthCheck();
  await testPreflightRequest();
  await testApiRequest();
  
  console.log('🎉 CORS测试完成！');
  console.log('\n💡 如果看到❌错误，请检查:');
  console.log('1. 后端服务是否正常运行');
  console.log('2. 环境变量FRONTEND_URL是否正确设置');
  console.log('3. CORS配置是否包含正确的域名');
  console.log('4. 查看后端日志了解详细错误信息');
};

// 执行测试
runTests().catch(error => {
  console.error('测试执行失败:', error);
  process.exit(1);
});
