import mysql from 'mysql2/promise';
import dotenv from 'dotenv';

dotenv.config();

// 数据库连接配置
const dbConfig = {
  host: process.env.DB_HOST || '*************',
  user: process.env.DB_USER || 'image_design',
  password: process.env.DB_PASSWORD || 'Mr7ybjSWaxz5aYZT',
  database: process.env.DB_NAME || 'image_design',
  port: process.env.DB_PORT || 3306,
  charset: 'utf8mb4',
  collation: 'utf8mb4_unicode_ci',
  connectionLimit: 10,           // 连接池大小
  queueLimit: 0,
  idleTimeout: 180000,           // 空闲连接超时时间 3分钟
  enableKeepAlive: true,         // 启用保活
  keepAliveInitialDelay: 0,      // 保活初始延迟
  // 确保字符编码正确
  typeCast: function (field, next) {
    if (field.type === 'VAR_STRING' || field.type === 'STRING') {
      return field.string();
    }
    return next();
  }
};

// 创建连接池
const pool = mysql.createPool(dbConfig);

// 连接池事件监听
pool.on('connection', (connection) => {
  console.log('🔗 新的数据库连接建立:', connection.threadId);
});

pool.on('error', (err) => {
  console.error('❌ 数据库连接池错误:', err);
  if (err.code === 'PROTOCOL_CONNECTION_LOST') {
    console.log('🔄 尝试重新建立连接...');
  }
});

// 测试数据库连接
export const testConnection = async () => {
  try {
    const connection = await pool.getConnection();
    console.log('✅ 数据库连接成功');
    connection.release();
    return true;
  } catch (error) {
    console.error('❌ 数据库连接失败:', error.message);
    return false;
  }
};

// 执行查询（带重试机制）
export const query = async (sql, params = [], retries = 3) => {
  for (let attempt = 1; attempt <= retries; attempt++) {
    try {
      const [rows] = await pool.query(sql, params);
      return rows;
    } catch (error) {
      console.error(`数据库查询错误 (尝试 ${attempt}/${retries}):`, error.message);

      // 如果是连接重置错误且还有重试次数，则重试
      if ((error.code === 'ECONNRESET' || error.code === 'PROTOCOL_CONNECTION_LOST') && attempt < retries) {
        console.log(`等待 ${attempt * 1000}ms 后重试...`);
        await new Promise(resolve => setTimeout(resolve, attempt * 1000));
        continue;
      }

      // 最后一次尝试失败或其他错误，抛出异常
      throw error;
    }
  }
};

// 执行事务
export const transaction = async (callback) => {
  const connection = await pool.getConnection();
  try {
    await connection.beginTransaction();
    const result = await callback(connection);
    await connection.commit();
    return result;
  } catch (error) {
    await connection.rollback();
    throw error;
  } finally {
    connection.release();
  }
};

// 分页查询辅助函数（带重试机制）
export const paginate = async (sql, params = [], pageNum = 1, pageSize = 10, retries = 3) => {
  for (let attempt = 1; attempt <= retries; attempt++) {
    try {
      // 计算总数
      const countSql = `SELECT COUNT(*) as total FROM (${sql}) as count_table`;
      const countResult = await query(countSql, params, 1); // 单次重试计数查询
      const total = countResult[0]?.total || 0;

      // 分页查询
      const offset = (pageNum - 1) * pageSize;
      const paginatedSql = `${sql} LIMIT ${offset}, ${pageSize}`;
      const records = await query(paginatedSql, params, 1); // 单次重试数据查询

      return {
        records,
        total,
        pageNum: parseInt(pageNum),
        pageSize: parseInt(pageSize),
        totalPages: Math.ceil(total / pageSize)
      };
    } catch (error) {
      console.error(`分页查询错误 (尝试 ${attempt}/${retries}):`, error.message);

      // 如果是连接重置错误且还有重试次数，则重试
      if ((error.code === 'ECONNRESET' || error.code === 'PROTOCOL_CONNECTION_LOST') && attempt < retries) {
        console.log(`等待 ${attempt * 1000}ms 后重试分页查询...`);
        await new Promise(resolve => setTimeout(resolve, attempt * 1000));
        continue;
      }

      // 最后一次尝试失败或其他错误，抛出异常
      throw error;
    }
  }
};

// 获取连接池状态
export const getPoolStatus = () => {
  try {
    return {
      connectionLimit: dbConfig.connectionLimit,
      poolConnections: pool.pool ? pool.pool.allConnections?.length || 0 : 0,
      freeConnections: pool.pool ? pool.pool.freeConnections?.length || 0 : 0,
      status: 'active'
    };
  } catch (error) {
    return {
      connectionLimit: dbConfig.connectionLimit,
      poolConnections: 0,
      freeConnections: 0,
      status: 'error',
      error: error.message
    };
  }
};

export default pool;
