import { request } from '@/utils/request'

// 用户相关接口类型定义
export interface User {
  id: number
  username: string
  nickname?: string
  avatar?: string
  department_id?: number
  department_name?: string
  department_code?: string
  role?: 'admin' | 'user' | 'manager'
  is_active: 0 | 1
  last_login?: string
  created_at: string
  updated_at: string
}

export interface UserListParams {
  pageNum?: number
  pageSize?: number
  keyword?: string
  is_active?: string
}

export interface CreateUserParams {
  username: string
  nickname?: string
  department_id?: number
  password: string
  role?: 'admin' | 'user' | 'manager'
  is_active?: 0 | 1
}

export interface UpdateUserParams {
  username: string
  nickname?: string
  department_id?: number
  role?: 'admin' | 'user' | 'manager'
  is_active?: 0 | 1
}

export interface UserStats {
  total: number
  active: number
  admin: number
  todayRegistered: number
}

// 获取用户列表
export const getUserList = (params: UserListParams = {}) => {
  return request.get('/api/user/list', { params })
}

// 获取用户详情
export const getUserDetail = (id: number) => {
  return request.get(`/api/user/${id}`)
}

// 创建用户
export const createUser = (data: CreateUserParams) => {
  return request.post('/api/user', data)
}

// 更新用户
export const updateUser = (id: number, data: UpdateUserParams) => {
  return request.put(`/api/user/${id}`, data)
}

// 删除用户
export const deleteUser = (id: number) => {
  return request.delete(`/api/user/${id}`)
}

// 批量删除用户
export const batchDeleteUsers = (ids: number[]) => {
  return request.delete(`/api/user/batch/${ids.join(',')}`)
}

// 更新用户状态
export const updateUserStatus = (id: number, status: 0 | 1) => {
  return request.patch(`/api/user/${id}/status`, { status })
}

// 重置用户密码
export const resetUserPassword = (id: number, password: string) => {
  return request.patch(`/api/user/${id}/password`, { password })
}

// 获取用户统计信息
export const getUserStats = () => {
  return request.get('/api/user/stats/overview')
}

// 获取当前用户信息
export const getCurrentUser = () => {
  return request.get('/api/user/current')
}
