import { query } from '../src/config/database.js';

const debugUserTemplates = async () => {
  try {
    console.log('🔍 调试用户模板查询...\n');

    // 1. 检查用户信息
    console.log('👤 检查用户信息:');
    const users = await query('SELECT id, username, nickname FROM system_users WHERE username = ?', ['admin']);
    if (users.length > 0) {
      const user = users[0];
      console.log(`   用户ID: ${user.id}`);
      console.log(`   用户名: ${user.username}`);
      console.log(`   昵称: ${user.nickname}`);

      // 2. 检查该用户的模板
      console.log('\n📋 检查该用户的模板:');
      const userTemplates = await query(`
        SELECT id, name, created_by, created_at 
        FROM editor_templates 
        WHERE created_by = ? 
        ORDER BY created_at DESC
      `, [user.id]);

      if (userTemplates.length > 0) {
        console.log(`   找到 ${userTemplates.length} 个模板:`);
        userTemplates.forEach(template => {
          console.log(`     ID: ${template.id}, 名称: ${template.name}, 创建时间: ${template.created_at}`);
        });
      } else {
        console.log('   该用户没有模板');
      }

      // 3. 检查所有模板的创建者
      console.log('\n📊 所有模板的创建者分布:');
      const allTemplates = await query(`
        SELECT created_by, COUNT(*) as count 
        FROM editor_templates 
        GROUP BY created_by 
        ORDER BY count DESC
      `);

      allTemplates.forEach(item => {
        console.log(`   创建者ID: ${item.created_by || 'NULL'}, 模板数量: ${item.count}`);
      });

      // 4. 测试API查询逻辑
      console.log('\n🔧 测试API查询逻辑:');
      const apiQuery = `
        SELECT
          id,
          name,
          COALESCE(cover, thumbnail_url) as thumbnail_url,
          COALESCE(json, canvas_data) as canvas_data,
          description,
          created_at,
          updated_at
        FROM editor_templates
        WHERE created_by = ? AND COALESCE(state, 1) = 1
        ORDER BY created_at DESC
      `;

      const apiResults = await query(apiQuery, [user.id]);
      console.log(`   API查询结果: ${apiResults.length} 个模板`);
      
      if (apiResults.length > 0) {
        apiResults.forEach(template => {
          console.log(`     ID: ${template.id}, 名称: ${template.name}`);
          console.log(`     缩略图: ${template.thumbnail_url || 'NULL'}`);
          console.log(`     状态检查: state字段存在且为1`);
          console.log(`     ---`);
        });
      }

      // 5. 检查state字段
      console.log('\n🔍 检查state字段:');
      const stateCheck = await query(`
        SELECT id, name, state, COALESCE(state, 1) as effective_state
        FROM editor_templates 
        WHERE created_by = ?
      `, [user.id]);

      stateCheck.forEach(template => {
        console.log(`   ID: ${template.id}, state: ${template.state}, effective_state: ${template.effective_state}`);
      });

    } else {
      console.log('   用户不存在');
    }

    console.log('\n✅ 调试完成！');

  } catch (error) {
    console.error('❌ 调试过程中发生错误:', error);
  }
};

// 执行调试
debugUserTemplates().then(() => {
  console.log('程序执行完成');
  process.exit(0);
}).catch(error => {
  console.error('程序执行失败:', error);
  process.exit(1);
});
