import express from 'express';
import { query, paginate } from '../config/database.js';

const router = express.Router();

// 获取元素分类
router.get('/category', async (req, res) => {
  try {
    const sql = `
      SELECT
        id,
        name,
        icon,
        sort_order,
        status,
        created_at,
        updated_at
      FROM element_categories
      WHERE status = 1
      ORDER BY sort_order ASC, created_at DESC
    `;

    const categories = await query(sql);

    // 为每个分类获取前6个元素项目
    const categoriesWithList = [];
    for (const category of categories) {
      const itemsSql = `
        SELECT
          id,
          name,
          preview_url,
          content_url,
          element_type,
          width,
          height
        FROM elements
        WHERE status = 1 AND category_id = ?
        ORDER BY created_at DESC
        LIMIT 6
      `;

      const items = await query(itemsSql, [category.id]);

      categoriesWithList.push({
        ...category,
        list: items.map(item => ({
          ...item,
          url: item.content_url,
          title: item.name,
          type: item.element_type
        }))
      });
    }

    res.success({ records: categoriesWithList, total: categoriesWithList.length });
  } catch (error) {
    console.error('获取元素分类失败:', error);
    res.error('获取元素分类失败');
  }
});

// 获取元素列表
router.get('/list', async (req, res) => {
  try {
    const { pageNum = 1, pageSize = 10, query: searchQuery } = req.query;
    let { categoryId } = searchQuery || {};
    
    let sql = `
      SELECT 
        e.id,
        e.name,
        e.preview_url,
        e.content_url,
        e.element_type,
        e.category_id,
        e.tags,
        e.width,
        e.height,
        e.file_size,
        e.created_at,
        e.updated_at,
        ec.name as category_name
      FROM elements e
      LEFT JOIN element_categories ec ON e.category_id = ec.id
      WHERE e.status = 1
    `;
    
    const params = [];
    
    if (categoryId) {
      sql += ' AND e.category_id = ?';
      params.push(categoryId);
    }
    
    sql += ' ORDER BY e.created_at DESC';
    
    const result = await paginate(sql, params, pageNum, pageSize);
    
    // 解析tags字段
    result.records = result.records.map(item => ({
      ...item,
      tags: item.tags ? (typeof item.tags === 'string' ? JSON.parse(item.tags) : item.tags) : [],
      category: item.category_id
    }));
    
    res.success(result);
  } catch (error) {
    console.error('获取元素列表失败:', error);
    res.error('获取元素列表失败');
  }
});

// 根据分类获取元素
router.get('/list/category/:categoryId', async (req, res) => {
  try {
    const { categoryId } = req.params;
    const { pageNum = 1, pageSize = 10 } = req.query;
    
    const sql = `
      SELECT 
        e.id,
        e.name,
        e.preview_url,
        e.content_url,
        e.element_type,
        e.category_id,
        e.tags,
        e.width,
        e.height,
        e.file_size,
        e.created_at,
        e.updated_at,
        ec.name as category_name
      FROM elements e
      LEFT JOIN element_categories ec ON e.category_id = ec.id
      WHERE e.status = 1 AND e.category_id = ?
      ORDER BY e.created_at DESC
    `;
    
    const result = await paginate(sql, [categoryId], pageNum, pageSize);
    
    // 解析tags字段
    result.records = result.records.map(item => ({
      ...item,
      tags: item.tags ? (typeof item.tags === 'string' ? JSON.parse(item.tags) : item.tags) : [],
      category: item.category_id
    }));
    
    res.success(result);
  } catch (error) {
    console.error('获取分类元素失败:', error);
    res.error('获取分类元素失败');
  }
});

// 根据类型获取元素
router.get('/list/type/:elementType', async (req, res) => {
  try {
    const { elementType } = req.params;
    const { pageNum = 1, pageSize = 10 } = req.query;
    
    const sql = `
      SELECT 
        e.id,
        e.name,
        e.preview_url,
        e.content_url,
        e.element_type,
        e.category_id,
        e.tags,
        e.width,
        e.height,
        e.file_size,
        e.created_at,
        e.updated_at,
        ec.name as category_name
      FROM elements e
      LEFT JOIN element_categories ec ON e.category_id = ec.id
      WHERE e.status = 1 AND e.element_type = ?
      ORDER BY e.created_at DESC
    `;
    
    const result = await paginate(sql, [elementType], pageNum, pageSize);
    
    // 解析tags字段
    result.records = result.records.map(item => ({
      ...item,
      tags: item.tags ? (typeof item.tags === 'string' ? JSON.parse(item.tags) : item.tags) : [],
      category: item.category_id
    }));
    
    res.success(result);
  } catch (error) {
    console.error('获取类型元素失败:', error);
    res.error('获取类型元素失败');
  }
});

// 搜索元素
router.get('/search', async (req, res) => {
  try {
    const { keyword, pageNum = 1, pageSize = 10 } = req.query;
    
    if (!keyword) {
      return res.error('搜索关键词不能为空', 400);
    }
    
    const sql = `
      SELECT 
        e.id,
        e.name,
        e.preview_url,
        e.content_url,
        e.element_type,
        e.category_id,
        e.tags,
        e.width,
        e.height,
        e.file_size,
        e.created_at,
        e.updated_at,
        ec.name as category_name
      FROM elements e
      LEFT JOIN element_categories ec ON e.category_id = ec.id
      WHERE e.status = 1 AND (e.name LIKE ? OR e.tags LIKE ?)
      ORDER BY e.created_at DESC
    `;
    
    const searchTerm = `%${keyword}%`;
    const result = await paginate(sql, [searchTerm, searchTerm], pageNum, pageSize);
    
    // 解析tags字段
    result.records = result.records.map(item => ({
      ...item,
      tags: item.tags ? (typeof item.tags === 'string' ? JSON.parse(item.tags) : item.tags) : [],
      category: item.category_id
    }));
    
    res.success(result);
  } catch (error) {
    console.error('搜索元素失败:', error);
    res.error('搜索元素失败');
  }
});

// 创建元素分类
router.post('/category', async (req, res) => {
  try {
    const { name, icon, sort_order = 0 } = req.body;
    
    if (!name) {
      return res.error('分类名称不能为空', 400);
    }
    
    const sql = `
      INSERT INTO element_categories (name, icon, sort_order, status, created_at, updated_at)
      VALUES (?, ?, ?, 1, NOW(), NOW())
    `;
    
    const result = await query(sql, [name, icon, sort_order]);
    res.success({ id: result.insertId }, '元素分类创建成功');
  } catch (error) {
    console.error('创建元素分类失败:', error);
    res.error('创建元素分类失败');
  }
});

// 创建元素
router.post('/element', async (req, res) => {
  try {
    const { 
      name, 
      preview_url, 
      content_url, 
      element_type, 
      category_id, 
      tags, 
      width, 
      height, 
      file_size 
    } = req.body;
    
    if (!name || !content_url || !element_type) {
      return res.error('元素名称、内容URL和类型不能为空', 400);
    }
    
    const sql = `
      INSERT INTO elements (
        name, preview_url, content_url, element_type, category_id, 
        tags, width, height, file_size, status, created_at, updated_at
      )
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 1, NOW(), NOW())
    `;
    
    const tagsStr = tags ? (typeof tags === 'object' ? JSON.stringify(tags) : tags) : null;
    const result = await query(sql, [
      name, preview_url, content_url, element_type, category_id,
      tagsStr, width, height, file_size
    ]);
    
    res.success({ id: result.insertId }, '元素创建成功');
  } catch (error) {
    console.error('创建元素失败:', error);
    res.error('创建元素失败');
  }
});

export default router;
