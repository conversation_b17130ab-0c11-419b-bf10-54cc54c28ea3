import { createRouter, createWebHashHistory } from 'vue-router'
import { Message } from '@arco-design/web-vue'

// 布局组件
import MainLayout from '@/layouts/MainLayout.vue'

// 页面组件
import Login from '@/views/Login/Login.vue'
import Index from '@/views/Index/Index.vue'
import Editor from '@/views/Editor/editor.vue'
import PsParser from '@/views/PsParser/index.vue'
import CusComponents from '@/views/CusComponents/index.vue'
import Home from '@/views/Home/home.vue'

// 管理页面
import AdminLayout from '@/views/Admin/AdminLayout.vue'
import Dashboard from '@/views/Admin/Dashboard.vue'
import Logs from '@/views/Admin/Logs.vue'
import UserList from '@/views/Admin/UserList.vue'
import RoleManage from '@/views/Admin/RoleManage.vue'
import MaterialCategory from '@/views/Admin/MaterialCategory.vue'
import MaterialList from '@/views/Admin/MaterialList.vue'
import TemplateCategory from '@/views/Admin/TemplateCategory.vue'
import TemplateList from '@/views/Admin/TemplateList.vue'
import GalleryCategory from '@/views/Admin/GalleryCategory.vue'
import GalleryList from '@/views/Admin/GalleryList.vue'
import SuiteManagement from '@/views/Admin/SuiteManagement.vue'
import DocumentList from '@/views/Admin/DocumentList.vue'
import DepartmentList from '@/views/Admin/DepartmentList.vue'
import DesignStatistics from '@/views/Admin/DesignStatistics.vue'
import TestDept from '@/views/test-dept.vue'

// 项目站页面
import Product from '@/views/Product/index.vue'

// 检查用户是否已登录
const isLoggedIn = () => {
  return localStorage.getItem('isLoggedIn') === 'true'
}

const router = createRouter({
  history: createWebHashHistory(),
  routes: [
    // 登录页面
    {
      path: '/login',
      name: 'Login',
      component: Login,
      meta: { requiresAuth: false }
    },

    // 主应用布局
    {
      path: '/',
      component: MainLayout,
      meta: { requiresAuth: true },
      children: [
        // 重定向到首页
        {
          path: '',
          redirect: '/index'
        },
        // 首页
        {
          path: '/index',
          name: 'Index',
          component: Index,
        },
        // 编辑器
        {
          path: '/editor',
          name: 'Editor',
          component: Editor,
        },
        // 设计组
        {
          path: '/design',
          name: 'Design',
          component: () => import('@/views/Design/DesignTask.vue'),
        },
        // 项目站
        {
          path: '/product',
          name: 'Product',
          component: Product,
        },
        // 管理系统
        {
          path: '/admin',
          component: AdminLayout,
          redirect: '/admin/dashboard',
          children: [
            {
              path: 'dashboard',
              name: 'AdminDashboard',
              component: Dashboard,
            },
            {
              path: 'logs',
              name: 'AdminLogs',
              component: Logs,
            },
            {
              path: 'user-list',
              name: 'AdminUserList',
              component: UserList,
            },
            {
              path: 'department-list',
              name: 'AdminDepartmentList',
              component: DepartmentList,
            },
            {
              path: 'role-manage',
              name: 'AdminRoleManage',
              component: RoleManage,
            },
            {
              path: 'test-dept',
              name: 'TestDept',
              component: TestDept,
            },
            {
              path: 'material-category',
              name: 'AdminMaterialCategory',
              component: MaterialCategory,
            },
            {
              path: 'material-category-test',
              name: 'AdminMaterialCategoryTest',
              component: () => import('@/views/Admin/MaterialCategoryTest.vue'),
            },
            {
              path: 'material-list',
              name: 'AdminMaterialList',
              component: MaterialList,
            },
            {
              path: 'template-category',
              name: 'AdminTemplateCategory',
              component: TemplateCategory,
            },
            {
              path: 'template-list',
              name: 'AdminTemplateList',
              component: TemplateList,
            },
            {
              path: 'gallery-category',
              name: 'AdminGalleryCategory',
              component: GalleryCategory,
            },
            {
              path: 'gallery-list',
              name: 'AdminGalleryList',
              component: GalleryList,
            },
            {
              path: 'suite-management',
              name: 'AdminSuiteManagement',
              component: SuiteManagement,
            },
            {
              path: 'document-list',
              name: 'AdminDocumentList',
              component: DocumentList,
            },
            {
              path: 'design-statistics',
              name: 'AdminDesignStatistics',
              component: DesignStatistics,
            },
          ]
        },
      ]
    },

    // 其他独立页面（保持原有功能）
    {
      path: '/home',
      name: 'Home',
      component: Home,
      meta: { requiresAuth: false }
    },
    {
      path: '/psParser',
      name: 'PsParser',
      component: PsParser,
      meta: { requiresAuth: false }
    },
    {
      path: '/components',
      name: 'CusComponents',
      component: CusComponents,
      meta: { requiresAuth: false }
    },

  ],
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const requiresAuth = to.meta.requiresAuth !== false // 默认需要登录
  const loggedIn = isLoggedIn()

  if (requiresAuth && !loggedIn) {
    // 需要登录但未登录，跳转到登录页
    Message.warning('请先登录')
    next('/login')
  } else if (to.path === '/login' && loggedIn) {
    // 已登录用户访问登录页，跳转到首页
    next('/index')
  } else {
    // 正常访问
    next()
  }
})

export default router
