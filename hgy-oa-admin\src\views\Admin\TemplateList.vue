<template>
  <div class="template-list-container">
    <a-card title="模板管理" :bordered="false">
      <!-- 筛选栏 -->
      <div class="filter-bar">
        <a-row :gutter="16">
          <a-col :span="6">
            <a-input-search
              v-model="filters.keyword"
              placeholder="搜索模板名称"
              @search="fetchTemplateList"
              @clear="fetchTemplateList"
              allow-clear
            />
          </a-col>
          <a-col :span="4">
            <a-select
              v-model="filters.category_id"
              placeholder="选择分类"
              allow-clear
              @change="fetchTemplateList"
            >
              <a-option value="">全部分类</a-option>
              <a-option
                v-for="category in categoryOptions"
                :key="category.id"
                :value="category.id"
              >
                {{ category.display }}
              </a-option>
            </a-select>
          </a-col>
          <a-col :span="3">
            <a-select
              v-model="filters.is_featured"
              placeholder="推荐状态"
              allow-clear
              @change="fetchTemplateList"
            >
              <a-option value="">全部</a-option>
              <a-option :value="1">推荐</a-option>
              <a-option :value="0">不推荐</a-option>
            </a-select>
          </a-col>
          <a-col :span="3">
            <a-select
              v-model="filters.is_public"
              placeholder="公开状态"
              allow-clear
              @change="fetchTemplateList"
            >
              <a-option value="">全部</a-option>
              <a-option :value="1">公开</a-option>
              <a-option :value="0">私有</a-option>
            </a-select>
          </a-col>
          <a-col :span="4">
            <a-button @click="resetFilters">重置</a-button>
          </a-col>
        </a-row>
      </div>

      <!-- 模板表格 -->
      <a-table
        :columns="columns"
        :data="templateData"
        :loading="loading"
        :pagination="pagination"
        @page-change="handlePageChange"
        @page-size-change="handlePageSizeChange"
        row-key="id"
      >
        <template #cover="{ record }">
          <div class="template-cover">
            <img
              v-if="record.cover"
              :src="record.cover"
              :alt="record.name"
              class="cover-image"
            />
            <div v-else class="no-cover">无封面</div>
          </div>
        </template>

        <template #category="{ record }">
          <span v-if="record.category_display">{{ record.category_display }}</span>
          <span v-else>-</span>
        </template>

        <template #size="{ record }">
          {{ record.width }} × {{ record.height }}
        </template>

        <template #is_featured="{ record }">
          <a-tag :color="record.is_featured ? 'red' : 'gray'">
            {{ record.is_featured ? '推荐' : '普通' }}
          </a-tag>
        </template>

        <template #is_public="{ record }">
          <a-tag :color="record.is_public ? 'green' : 'orange'">
            {{ record.is_public ? '公开' : '私有' }}
          </a-tag>
        </template>

        <template #usage_count="{ record }">
          <a-tag color="blue">{{ record.usage_count || 0 }}</a-tag>
        </template>

        <template #created_at="{ record }">
          {{ record.created_at }}
        </template>

        <template #actions="{ record }">
          <a-space size="mini">
            <a-button type="text" size="small" @click="showEditModal(record)">
              编辑
            </a-button>
            <a-button type="text" size="small" status="danger" @click="deleteTemplate(record)">
              删除
            </a-button>
          </a-space>
        </template>
      </a-table>
    </a-card>

    <!-- 编辑模板弹窗 -->
    <a-modal
      v-model:visible="editModalVisible"
      title="编辑模板"
      width="600px"
      @ok="handleEditSubmit"
      @cancel="handleEditCancel"
    >
      <a-form ref="editFormRef" :model="editForm" :rules="editRules" layout="vertical">
        <a-form-item label="模板名称" field="name" required>
          <a-input v-model="editForm.name" placeholder="请输入模板名称" />
        </a-form-item>

        <a-form-item label="模板描述" field="description">
          <a-textarea
            v-model="editForm.description"
            placeholder="请输入模板描述"
            :rows="3"
          />
        </a-form-item>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="分类" field="category_id">
              <a-select
                v-model="editForm.category_id"
                placeholder="请选择分类"
                allow-clear
              >
                <a-option
                  v-for="category in categoryOptions"
                  :key="category.id"
                  :value="category.id"
                >
                  {{ category.display }}
                </a-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="推荐状态" field="is_featured">
              <a-select v-model="editForm.is_featured" placeholder="请选择推荐状态">
                <a-option :value="0">普通</a-option>
                <a-option :value="1">推荐</a-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-form-item label="公开状态" field="is_public">
          <a-select v-model="editForm.is_public" placeholder="请选择公开状态">
            <a-option :value="1">公开</a-option>
            <a-option :value="0">私有</a-option>
          </a-select>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { Message, Modal } from '@arco-design/web-vue'
import {
  getTemplateAdminList,
  updateTemplateAdmin,
  deleteTemplateAdmin,
  type Template
} from '@/api/template'
import { getTemplateCategoryTree, type TemplateCategory } from '@/api/template-category'

// 响应式数据
const loading = ref(false)
const templateData = ref<Template[]>([])
const categoryOptions = ref<any[]>([])
const editModalVisible = ref(false)
const editFormRef = ref()

// 筛选条件
const filters = reactive({
  keyword: '',
  category_id: '',
  is_featured: '',
  is_public: ''
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0,
  showTotal: true,
  showPageSize: true
})

// 编辑表单
const editForm = reactive({
  id: null as number | null,
  name: '',
  description: '',
  category_id: null as number | null,
  is_featured: 0,
  is_public: 1
})

// 表格列配置
const columns = [
  {
    title: 'ID',
    dataIndex: 'id',
    width: 80
  },
  {
    title: '封面',
    dataIndex: 'cover',
    slotName: 'cover',
    width: 100
  },
  {
    title: '模板名称',
    dataIndex: 'name',
    width: 200
  },
  {
    title: '分类',
    dataIndex: 'category_display',
    slotName: 'category',
    width: 150
  },
  {
    title: '尺寸',
    dataIndex: 'size',
    slotName: 'size',
    width: 120
  },
  {
    title: '推荐',
    dataIndex: 'is_featured',
    slotName: 'is_featured',
    width: 80
  },
  {
    title: '状态',
    dataIndex: 'is_public',
    slotName: 'is_public',
    width: 80
  },
  {
    title: '使用次数',
    dataIndex: 'usage_count',
    slotName: 'usage_count',
    width: 100
  },
  {
    title: '创建者',
    dataIndex: 'creator_name',
    width: 120
  },
  {
    title: '创建时间',
    dataIndex: 'created_at',
    slotName: 'created_at',
    width: 120
  },
  {
    title: '操作',
    slotName: 'actions',
    width: 120,
    fixed: 'right'
  }
]

// 表单验证规则
const editRules = {
  name: [{ required: true, message: '请输入模板名称' }]
}

// 获取模板列表
const fetchTemplateList = async () => {
  try {
    loading.value = true
    const response = await getTemplateAdminList({
      page: pagination.current,
      pageSize: pagination.pageSize,
      ...filters
    })

    if (response.success) {
      templateData.value = response.data.list
      pagination.total = response.data.total
    }
  } catch (error) {
    console.error('获取模板列表失败:', error)
    Message.error('获取模板列表失败')
  } finally {
    loading.value = false
  }
}

// 获取分类选项（一级分类）
const fetchCategoryOptions = async () => {
  try {
    console.log('开始获取分类选项...')
    const response = await getTemplateCategoryTree()
    console.log('分类API响应:', response)

    // 响应拦截器已经处理了success判断，直接使用response
    if (response.success) {
      const categories = response.data
      const options: any[] = []

      console.log('原始分类数据:', categories)

      // 现在是一级分类数组，直接处理
      categories.forEach((category: TemplateCategory) => {
        options.push({
          id: category.id,
          display: category.name
        })
      })

      categoryOptions.value = options
      console.log('处理后的分类选项:', options)
    } else {
      console.warn('分类API返回失败:', response)
    }
  } catch (error) {
    console.error('获取分类选项失败:', error)
  }
}

// 显示编辑弹窗
const showEditModal = async (record: Template) => {
  console.log('编辑模板数据:', record)

  // 确保分类选项已加载
  if (categoryOptions.value.length === 0) {
    await fetchCategoryOptions()
  }

  editForm.id = record.id
  editForm.name = record.name
  editForm.description = record.description || ''
  editForm.category_id = record.category_id || null
  editForm.is_featured = record.is_featured || 0
  editForm.is_public = record.is_public || 1

  console.log('编辑表单数据:', editForm)
  console.log('分类选项:', categoryOptions.value)

  editModalVisible.value = true
}

// 提交编辑
const handleEditSubmit = async () => {
  try {
    const errors = await editFormRef.value?.validate()
    if (errors) return

    const response = await updateTemplateAdmin(editForm.id!, {
      name: editForm.name,
      description: editForm.description,
      category_id: editForm.category_id,
      is_featured: editForm.is_featured,
      is_public: editForm.is_public
    })

    if (response.success) {
      Message.success('更新模板成功')
      editModalVisible.value = false
      fetchTemplateList()
    }
  } catch (error) {
    console.error('更新模板失败:', error)
    Message.error('更新模板失败')
  }
}

// 取消编辑
const handleEditCancel = () => {
  editModalVisible.value = false
}

// 删除模板
const deleteTemplate = (record: Template) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除模板"${record.name}"吗？此操作不可恢复。`,
    onOk: async () => {
      try {
        const response = await deleteTemplateAdmin(record.id)
        if (response.success) {
          Message.success('删除成功')
          fetchTemplateList()
        }
      } catch (error) {
        console.error('删除失败:', error)
        Message.error('删除失败')
      }
    }
  })
}

// 重置筛选条件
const resetFilters = () => {
  filters.keyword = ''
  filters.category_id = ''
  filters.is_featured = ''
  filters.is_public = ''
  pagination.current = 1
  fetchTemplateList()
}

// 分页处理
const handlePageChange = (page: number) => {
  pagination.current = page
  fetchTemplateList()
}

const handlePageSizeChange = (pageSize: number) => {
  pagination.pageSize = pageSize
  pagination.current = 1
  fetchTemplateList()
}

// 初始化
onMounted(() => {
  fetchCategoryOptions()
  fetchTemplateList()
})
</script>

<style scoped>
.template-list-container {
  padding: 20px;
}

.filter-bar {
  margin-bottom: 20px;
}

.template-cover {
  display: flex;
  align-items: center;
  justify-content: center;
}

.cover-image {
  width: 60px;
  height: 40px;
  object-fit: cover;
  border-radius: 4px;
  border: 1px solid #e5e6eb;
}

.no-cover {
  width: 60px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f7f8fa;
  border: 1px solid #e5e6eb;
  border-radius: 4px;
  font-size: 12px;
  color: #86909c;
}
</style>
