<template>
  <div class="my-templates-wrap">
    <div class="header">
      <h3>我的模板</h3>
      <p class="subtitle">您保存的设计模板</p>
    </div>
    
    <div class="templates-list">
      <div 
        v-for="template in templateList" 
        :key="template.id"
        class="template-item"
        @click="loadTemplate(template)"
      >
        <div class="template-preview">
          <img
            v-if="template.thumbnail_url || template.cover"
            :src="template.thumbnail_url || template.cover"
            :alt="template.name || template.title"
            @error="handleImageError"
          />
          <div v-else class="no-preview">
            <icon-image :size="24" />
          </div>
        </div>
        <div class="template-info">
          <div class="template-title">{{ template.name || template.title }}</div>
          <div class="template-date">{{ formatDate(template.created_at) }}</div>
        </div>
        <div class="template-actions">
          <a-button type="text" size="mini" @click.stop="editTemplate(template)">
            <icon-edit />
          </a-button>
          <a-button type="text" size="mini" status="danger" @click.stop="deleteTemplate(template)">
            <icon-delete />
          </a-button>
        </div>
      </div>
    </div>
    
    <!-- 空状态 -->
    <div v-if="!loading && templateList.length === 0" class="empty-state">
      <icon-folder :size="48" />
      <p>暂无保存的模板</p>
      <p class="tip">在编辑器中保存设计后，模板会显示在这里</p>
    </div>
    
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-state">
      <a-spin />
      <p>加载中...</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { Message, Modal } from '@arco-design/web-vue'
import { useEditor } from '@/views/Editor/app'
import { getUserTemplates, deleteUserTemplate } from '@/api/template'

// 响应式数据
const templateList = ref<any[]>([])
const loading = ref(false)

const { editor } = useEditor()

// 获取用户模板列表
const fetchMyTemplates = async () => {
  try {
    loading.value = true

    console.log('开始获取用户模板...')

    // 调用真实的API
    const response = await getUserTemplates()

    if (response.success || response.data) {
      templateList.value = response.data?.list || response.data || []
      console.log('用户模板加载成功:', templateList.value)
    } else {
      console.warn('API返回无数据')
      templateList.value = []
    }
  } catch (error) {
    console.error('获取用户模板失败:', error)

    // 如果API失败，显示空状态而不是错误
    templateList.value = []

    // 只有在网络错误等情况下才显示错误消息
    if (error.message && !error.message.includes('404')) {
      Message.error('获取模板失败')
    }
  } finally {
    loading.value = false
  }
}

// 等待编辑器初始化
const waitForEditor = (maxAttempts = 10, delay = 500): Promise<any> => {
  return new Promise((resolve, reject) => {
    let attempts = 0

    const checkEditor = () => {
      attempts++

      if (editor && editor.importJsonToCurrentPage) {
        resolve(editor)
        return
      }

      if (attempts >= maxAttempts) {
        reject(new Error('编辑器初始化超时'))
        return
      }

      setTimeout(checkEditor, delay)
    }

    checkEditor()
  })
}

// 加载模板到编辑器
const loadTemplate = async (template: any) => {
  try {
    console.log('加载模板:', template)

    // 等待编辑器初始化
    try {
      await waitForEditor()
    } catch (error) {
      Message.error('编辑器未初始化，请刷新页面重试')
      return
    }

    // 获取模板数据
    const canvasData = template.canvas_data || template.data || template.json
    if (canvasData) {
      try {
        // 如果canvas_data是字符串，需要解析为JSON
        const data = typeof canvasData === 'string' ? JSON.parse(canvasData) : canvasData

        console.log('准备加载模板数据:', data)

        // 使用编辑器的API加载数据
        editor.importJsonToCurrentPage(data, true)

        Message.success(`已加载模板: ${template.name || template.title}`)
      } catch (parseError) {
        console.error('解析模板数据失败:', parseError)
        Message.error('模板数据格式错误')
      }
    } else {
      Message.warning('模板数据为空')
      console.warn('模板数据为空:', template)
    }
  } catch (error) {
    console.error('加载模板失败:', error)
    Message.error('加载模板失败')
  }
}

// 编辑模板
const editTemplate = (template: any) => {
  console.log('编辑模板:', template)
  // TODO: 实现模板编辑功能
  Message.info('编辑功能开发中...')
}

// 删除模板
const deleteTemplate = (template: any) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除模板"${template.name || template.title}"吗？`,
    onOk: async () => {
      try {
        // 调用删除API
        await deleteUserTemplate(template.id)

        // 从列表中移除
        const index = templateList.value.findIndex(t => t.id === template.id)
        if (index > -1) {
          templateList.value.splice(index, 1)
        }

        Message.success('模板删除成功')
      } catch (error) {
        console.error('删除模板失败:', error)
        Message.error('删除模板失败')
      }
    }
  })
}

// 格式化日期
const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

// 图片加载错误处理
const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.style.display = 'none'
}

// 监听模板保存事件
const handleTemplateSaved = (event: CustomEvent) => {
  console.log('收到模板保存事件:', event.detail)
  // 刷新模板列表
  fetchMyTemplates()
}

// 组件挂载时获取数据
onMounted(() => {
  console.log('MyTemplatesWrap 组件挂载')
  fetchMyTemplates()

  // 监听模板保存事件
  window.addEventListener('template-saved', handleTemplateSaved)
})

// 组件卸载时清理事件监听
onUnmounted(() => {
  window.removeEventListener('template-saved', handleTemplateSaved)
})
</script>

<style scoped>
.my-templates-wrap {
  padding: 16px;
  height: 100%;
  overflow-y: auto;
}

.header {
  margin-bottom: 20px;
  text-align: center;
}

.header h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.subtitle {
  margin: 0;
  font-size: 14px;
  color: #666;
}

.templates-list {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
}

.template-item {
  display: flex;
  flex-direction: column;
  padding: 8px;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  background: #fff;
  text-align: center;
}

.template-item:hover {
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.template-preview {
  width: 100%;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 8px;
}

.template-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.no-preview {
  color: #ccc;
}

.template-info {
  flex: 1;
  min-width: 0;
  margin-bottom: 8px;
}

.template-title {
  font-size: 12px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.template-date {
  font-size: 10px;
  color: #999;
}

.template-actions {
  display: flex;
  justify-content: center;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s;
}

.template-item:hover .template-actions {
  opacity: 1;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #999;
}

.empty-state p {
  margin: 12px 0 0 0;
  font-size: 14px;
}

.tip {
  font-size: 12px !important;
  color: #ccc !important;
}

.loading-state {
  text-align: center;
  padding: 40px 20px;
  color: #999;
}

.loading-state p {
  margin-top: 12px;
  font-size: 14px;
}
</style>
