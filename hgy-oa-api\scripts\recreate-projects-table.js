import { query } from '../src/config/database.js';

async function recreateProjectsTable() {
  try {
    console.log('开始重新创建项目表...');

    // 删除现有表
    await query('DROP TABLE IF EXISTS `projects`');
    console.log('删除现有项目表');

    // 创建新的项目表
    const createTableSQL = `
      CREATE TABLE IF NOT EXISTS \`projects\` (
        \`id\` int(11) NOT NULL AUTO_INCREMENT COMMENT '项目ID',
        \`code\` varchar(50) NOT NULL COMMENT '项目编号',
        \`name\` varchar(255) NOT NULL COMMENT '项目名称',
        \`category\` varchar(100) NOT NULL COMMENT '项目分类',
        \`terminal_type\` varchar(100) NOT NULL COMMENT '终端类型',
        \`start_date\` date NOT NULL COMMENT '启动日期',
        \`current_stage\` varchar(50) NOT NULL DEFAULT '1.开发意向' COMMENT '当前阶段',
        \`product_doc\` varchar(500) DEFAULT NULL COMMENT '产品文档',
        \`ui_design\` varchar(500) DEFAULT NULL COMMENT 'UI设计稿',
        \`progress_desc\` text DEFAULT NULL COMMENT '进度描述',
        \`user_id\` int(11) DEFAULT NULL COMMENT '创建者用户ID',
        \`status\` tinyint(1) DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
        \`created_at\` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        \`updated_at\` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        PRIMARY KEY (\`id\`),
        UNIQUE KEY \`unique_code\` (\`code\`),
        KEY \`idx_category\` (\`category\`),
        KEY \`idx_terminal_type\` (\`terminal_type\`),
        KEY \`idx_current_stage\` (\`current_stage\`),
        KEY \`idx_user_id\` (\`user_id\`),
        KEY \`idx_status\` (\`status\`),
        KEY \`idx_created_at\` (\`created_at\`)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='项目管理表'
    `;

    await query(createTableSQL);
    console.log('项目表创建成功');

    // 插入新的示例数据
    const insertDataSQL = `
      INSERT INTO \`projects\` (
        \`code\`, \`name\`, \`category\`, \`terminal_type\`, \`start_date\`, \`current_stage\`,
        \`product_doc\`, \`ui_design\`, \`progress_desc\`, \`user_id\`
      ) VALUES 
      (
        '06', '公考系统', '工具型', '微信小程序', '2025-07-15', '4.设计预评',
        '#', '#', '待评审', 1
      ),
      (
        '05', '作文改改', '创收型', '微信小程序', '2025-07-15', '4.设计预评',
        '#', '#', '待评审', 1
      ),
      (
        '04', '设计站', '工具型', 'PC端', '2025-06-25', '7.测试阶段',
        '#', '#', '待资料录入和验收', 1
      ),
      (
        '03', '题库系统', '工具型', 'PC后台', '2025-06-01', '7.测试阶段',
        '#', '#', '待资料录入和验收', 1
      ),
      (
        '02', '真题练练', '创收型', '微信小程序', '2025-05-15', '8.交付完成',
        '#', '#', '教研资料准备中', 1
      ),
      (
        '01', '刷题系统', '工具型', '微信小程序', '2024-11-01', '9.上线维护',
        '#', '#', '上线5家，准备中6家，年底上线8家', 1
      )
    `;

    await query(insertDataSQL);
    console.log('示例数据插入成功');

    console.log('项目表重新创建完成！');
    process.exit(0);

  } catch (error) {
    console.error('重新创建项目表失败:', error);
    process.exit(1);
  }
}

recreateProjectsTable();
