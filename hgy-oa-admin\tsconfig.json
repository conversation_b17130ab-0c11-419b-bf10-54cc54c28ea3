{
  "compilerOptions": {
    "target": "esnext",
    "useDefineForClassFields": true,
    "module": "esnext",
    "moduleResolution": "node",
    "strict": true,
    "jsx": "preserve",
    "sourceMap": true,
    "isolatedModules": false,
    "experimentalDecorators": true,
    "esModuleInterop": true,
    "noImplicitAny": true,
    "noImplicitThis": true,
    "strictNullChecks": false,
    "removeComments": true,
    "allowSyntheticDefaultImports": true,
    "resolveJsonModule": true,
    "allowJs": true,
    "lib": ["esnext", "dom"],
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"],
    },
    "skipLibCheck": true,
    "outDir": "./dist"
  },
  "include": ["auto-imports.d.ts","components.d.ts", "src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue"],
  "exclude": [
    "node_modules",
    "dist",
    "src/**/*.js",
    "src/**/style/index.js"
  ],
  "references": [{ "path": "./tsconfig.node.json" }]
}
