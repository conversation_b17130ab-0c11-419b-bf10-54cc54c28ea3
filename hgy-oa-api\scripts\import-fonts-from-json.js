import { query } from '../src/config/database.js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 读取 fonts.json 文件
const fontsJsonPath = path.join(__dirname, '../../hgy-oa-admin/src/assets/data/fonts.json');

const importFontsFromJson = async () => {
  try {
    console.log('🚀 开始从 fonts.json 导入字体数据...\n');

    // 读取 fonts.json 文件
    if (!fs.existsSync(fontsJsonPath)) {
      console.error('❌ fonts.json 文件不存在:', fontsJsonPath);
      return;
    }

    const fontsData = JSON.parse(fs.readFileSync(fontsJsonPath, 'utf8'));
    console.log(`📖 读取到 ${fontsData.list.length} 个字体配置`);

    // 清空现有字体数据（保留系统字体）
    console.log('🧹 清理现有非系统字体数据...');
    await query('DELETE FROM fonts WHERE is_system = 0');

    let successCount = 0;
    let errorCount = 0;

    // 导入字体数据
    for (const font of fontsData.list) {
      try {
        // 提取字体族名（从 code 或 name 推导）
        let fontFamily = font.code;
        if (font.name.includes('思源')) {
          fontFamily = font.name;
        } else if (font.name.includes('阿里')) {
          fontFamily = font.name;
        } else if (font.name.includes('方正')) {
          fontFamily = font.name;
        } else if (font.name.includes('优设')) {
          fontFamily = font.name;
        } else if (font.name.includes('钉钉')) {
          fontFamily = font.name;
        } else if (font.name.includes('抖音')) {
          fontFamily = font.name;
        }

        // 确定字体粗细
        let fontWeight = 'normal';
        if (font.name.includes('粗体') || font.name.includes('Bold')) {
          fontWeight = 'bold';
        } else if (font.name.includes('细体') || font.name.includes('Light')) {
          fontWeight = 'light';
        } else if (font.name.includes('中等') || font.name.includes('Medium')) {
          fontWeight = 'medium';
        } else if (font.name.includes('重体') || font.name.includes('Heavy')) {
          fontWeight = 'heavy';
        } else if (font.name.includes('极细') || font.name.includes('ExtraLight')) {
          fontWeight = 'extralight';
        } else if (font.name.includes('半粗') || font.name.includes('SemiBold')) {
          fontWeight = 'semibold';
        }

        const sql = `
          INSERT INTO fonts (
            name, code, preview_url, download_url, font_family, 
            font_weight, font_style, is_system, status, 
            created_at, updated_at
          )
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, 1, NOW(), NOW())
        `;

        await query(sql, [
          font.name,
          font.code,
          font.preview || null,
          font.download,
          fontFamily,
          fontWeight,
          'normal', // font_style
          0 // is_system (非系统字体)
        ]);

        console.log(`✅ 导入成功: ${font.name} (${font.code})`);
        successCount++;
      } catch (error) {
        console.error(`❌ 导入失败: ${font.name} - ${error.message}`);
        errorCount++;
      }
    }

    console.log(`\n📊 导入完成统计:`);
    console.log(`   成功: ${successCount} 个字体`);
    console.log(`   失败: ${errorCount} 个字体`);

    // 显示当前数据库中的字体列表
    console.log('\n📋 当前数据库中的字体列表:');
    const allFonts = await query(`
      SELECT id, name, code, font_family, font_weight, is_system 
      FROM fonts 
      WHERE status = 1 
      ORDER BY is_system DESC, name ASC
    `);

    console.log('系统字体:');
    allFonts.filter(f => f.is_system).forEach(font => {
      console.log(`   ${font.id}. ${font.name} (${font.code}) - ${font.font_family}`);
    });

    console.log('\n本地字体:');
    allFonts.filter(f => !f.is_system).forEach(font => {
      console.log(`   ${font.id}. ${font.name} (${font.code}) - ${font.font_family} [${font.font_weight}]`);
    });

    console.log('\n🎉 字体数据导入完成！');

  } catch (error) {
    console.error('❌ 导入过程中发生错误:', error);
  }
};

// 执行导入
importFontsFromJson().then(() => {
  console.log('程序执行完成');
  process.exit(0);
}).catch(error => {
  console.error('程序执行失败:', error);
  process.exit(1);
});
