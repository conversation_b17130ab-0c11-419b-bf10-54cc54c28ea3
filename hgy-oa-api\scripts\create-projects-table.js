import { query } from '../src/config/database.js';

async function createProjectsTable() {
  try {
    console.log('开始创建项目表...');

    // 创建项目表
    const createTableSQL = `
      CREATE TABLE IF NOT EXISTS \`projects\` (
        \`id\` int(11) NOT NULL AUTO_INCREMENT COMMENT '项目ID',
        \`name\` varchar(255) NOT NULL COMMENT '项目名称',
        \`category\` varchar(100) NOT NULL COMMENT '项目分类',
        \`start_date\` date NOT NULL COMMENT '启动日期',
        \`current_stage\` varchar(50) NOT NULL DEFAULT '1.开发意向' COMMENT '当前阶段',
        \`launch_date\` date DEFAULT NULL COMMENT '上线日期',
        \`product_doc_url\` varchar(500) DEFAULT NULL COMMENT '产品文档URL',
        \`ui_design_url\` varchar(500) DEFAULT NULL COMMENT 'UI设计稿URL',
        \`participants\` json DEFAULT NULL COMMENT '参与者信息 JSON格式',
        \`progress_description\` text DEFAULT NULL COMMENT '进度描述',
        \`user_id\` int(11) DEFAULT NULL COMMENT '创建者用户ID',
        \`status\` tinyint(1) DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
        \`created_at\` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        \`updated_at\` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        PRIMARY KEY (\`id\`),
        KEY \`idx_category\` (\`category\`),
        KEY \`idx_current_stage\` (\`current_stage\`),
        KEY \`idx_user_id\` (\`user_id\`),
        KEY \`idx_status\` (\`status\`),
        KEY \`idx_created_at\` (\`created_at\`)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='项目管理表'
    `;

    await query(createTableSQL);
    console.log('项目表创建成功');

    // 插入示例数据
    const insertDataSQL = `
      INSERT INTO \`projects\` (
        \`name\`, \`category\`, \`start_date\`, \`current_stage\`, \`launch_date\`,
        \`product_doc_url\`, \`ui_design_url\`, \`participants\`, \`progress_description\`, \`user_id\`
      ) VALUES 
      (
        '图片编辑器项目', '工具型', '2025-01-01', '6.开发阶段', '2025-03-01',
        'https://docs.example.com/image-editor', 'https://design.example.com/ui-mockup',
        '[{"name":"张三","role":"产品经理"},{"name":"李四","role":"前端开发"},{"name":"王五","role":"后端开发"}]',
        '项目进展顺利，前端界面基本完成，后端API开发中', 1
      ),
      (
        'OA办公系统', '管理型', '2024-12-01', '7.测试阶段', '2025-02-15',
        'https://docs.example.com/oa-system', 'https://design.example.com/oa-ui',
        '[{"name":"赵六","role":"项目经理"},{"name":"钱七","role":"全栈开发"},{"name":"孙八","role":"测试工程师"}]',
        '系统功能开发完成，正在进行系统测试和bug修复', 1
      ),
      (
        '移动端APP', '移动应用', '2025-01-15', '3.功能预评', NULL,
        'https://docs.example.com/mobile-app', NULL,
        '[{"name":"周九","role":"产品经理"},{"name":"吴十","role":"UI设计师"}]',
        '需求分析完成，正在进行功能设计和技术选型', 1
      ),
      (
        '数据分析平台', '数据型', '2024-11-01', '8.交付完成', '2025-01-10',
        'https://docs.example.com/data-platform', 'https://design.example.com/data-ui',
        '[{"name":"郑十一","role":"数据工程师"},{"name":"王十二","role":"前端开发"},{"name":"李十三","role":"后端开发"}]',
        '项目已完成交付，正在进行用户培训和系统维护', 1
      )
    `;

    await query(insertDataSQL);
    console.log('示例数据插入成功');

    console.log('项目表创建完成！');
    process.exit(0);

  } catch (error) {
    console.error('创建项目表失败:', error);
    process.exit(1);
  }
}

createProjectsTable();
