import express from 'express';
import { query, paginate } from '../config/database.js';

const router = express.Router();

// 获取背景图片列表
router.get('/imageList', async (req, res) => {
  try {
    const { pageNum = 1, pageSize = 10 } = req.query;
    
    const sql = `
      SELECT 
        id,
        name,
        preview_url,
        original_url,
        thumbnail_url,
        category_id,
        tags,
        width,
        height,
        file_size,
        color_palette,
        created_at,
        updated_at
      FROM background_images 
      WHERE status = 1 
      ORDER BY created_at DESC
    `;
    
    const result = await paginate(sql, [], pageNum, pageSize);
    
    // 解析tags和color_palette字段
    result.records = result.records.map(item => ({
      ...item,
      tags: item.tags ? (typeof item.tags === 'string' ? JSON.parse(item.tags) : item.tags) : [],
      color_palette: item.color_palette ? (typeof item.color_palette === 'string' ? JSON.parse(item.color_palette) : item.color_palette) : []
    }));
    
    res.success(result);
  } catch (error) {
    console.error('获取背景图片列表失败:', error);
    res.error('获取背景图片列表失败');
  }
});

// 根据分类获取背景图片
router.get('/imageList/category/:categoryId', async (req, res) => {
  try {
    const { categoryId } = req.params;
    const { pageNum = 1, pageSize = 10 } = req.query;
    
    const sql = `
      SELECT 
        bg.id,
        bg.name,
        bg.preview_url,
        bg.original_url,
        bg.thumbnail_url,
        bg.category_id,
        bg.tags,
        bg.width,
        bg.height,
        bg.file_size,
        bg.color_palette,
        bg.created_at,
        bg.updated_at,
        bc.name as category_name
      FROM background_images bg
      LEFT JOIN background_categories bc ON bg.category_id = bc.id
      WHERE bg.status = 1 AND bg.category_id = ?
      ORDER BY bg.created_at DESC
    `;
    
    const result = await paginate(sql, [categoryId], pageNum, pageSize);
    
    // 解析tags和color_palette字段
    result.records = result.records.map(item => ({
      ...item,
      tags: item.tags ? (typeof item.tags === 'string' ? JSON.parse(item.tags) : item.tags) : [],
      color_palette: item.color_palette ? (typeof item.color_palette === 'string' ? JSON.parse(item.color_palette) : item.color_palette) : []
    }));
    
    res.success(result);
  } catch (error) {
    console.error('获取分类背景图片失败:', error);
    res.error('获取分类背景图片失败');
  }
});

// 根据颜色搜索背景图片
router.get('/imageList/color/:color', async (req, res) => {
  try {
    const { color } = req.params;
    const { pageNum = 1, pageSize = 10 } = req.query;
    
    const sql = `
      SELECT 
        id,
        name,
        preview_url,
        original_url,
        thumbnail_url,
        category_id,
        tags,
        width,
        height,
        file_size,
        color_palette,
        created_at,
        updated_at
      FROM background_images 
      WHERE status = 1 AND color_palette LIKE ?
      ORDER BY created_at DESC
    `;
    
    const result = await paginate(sql, [`%${color}%`], pageNum, pageSize);
    
    // 解析tags和color_palette字段
    result.records = result.records.map(item => ({
      ...item,
      tags: item.tags ? (typeof item.tags === 'string' ? JSON.parse(item.tags) : item.tags) : [],
      color_palette: item.color_palette ? (typeof item.color_palette === 'string' ? JSON.parse(item.color_palette) : item.color_palette) : []
    }));
    
    res.success(result);
  } catch (error) {
    console.error('根据颜色搜索背景图片失败:', error);
    res.error('根据颜色搜索背景图片失败');
  }
});

// 搜索背景图片
router.get('/search', async (req, res) => {
  try {
    const { keyword, pageNum = 1, pageSize = 10 } = req.query;
    
    if (!keyword) {
      return res.error('搜索关键词不能为空', 400);
    }
    
    const sql = `
      SELECT 
        id,
        name,
        preview_url,
        original_url,
        thumbnail_url,
        category_id,
        tags,
        width,
        height,
        file_size,
        color_palette,
        created_at,
        updated_at
      FROM background_images 
      WHERE status = 1 AND (name LIKE ? OR tags LIKE ?)
      ORDER BY created_at DESC
    `;
    
    const searchTerm = `%${keyword}%`;
    const result = await paginate(sql, [searchTerm, searchTerm], pageNum, pageSize);
    
    // 解析tags和color_palette字段
    result.records = result.records.map(item => ({
      ...item,
      tags: item.tags ? (typeof item.tags === 'string' ? JSON.parse(item.tags) : item.tags) : [],
      color_palette: item.color_palette ? (typeof item.color_palette === 'string' ? JSON.parse(item.color_palette) : item.color_palette) : []
    }));
    
    res.success(result);
  } catch (error) {
    console.error('搜索背景图片失败:', error);
    res.error('搜索背景图片失败');
  }
});

// 获取背景分类
router.get('/categories', async (req, res) => {
  try {
    const sql = `
      SELECT 
        id,
        name,
        icon,
        sort_order,
        status,
        created_at,
        updated_at
      FROM background_categories 
      WHERE status = 1 
      ORDER BY sort_order ASC, created_at DESC
    `;
    
    const result = await query(sql);
    res.success({ records: result, total: result.length });
  } catch (error) {
    console.error('获取背景分类失败:', error);
    res.error('获取背景分类失败');
  }
});

// 创建背景分类
router.post('/category', async (req, res) => {
  try {
    const { name, icon, sort_order = 0 } = req.body;
    
    if (!name) {
      return res.error('分类名称不能为空', 400);
    }
    
    const sql = `
      INSERT INTO background_categories (name, icon, sort_order, status, created_at, updated_at)
      VALUES (?, ?, ?, 1, NOW(), NOW())
    `;
    
    const result = await query(sql, [name, icon, sort_order]);
    res.success({ id: result.insertId }, '背景分类创建成功');
  } catch (error) {
    console.error('创建背景分类失败:', error);
    res.error('创建背景分类失败');
  }
});

// 创建背景图片
router.post('/image', async (req, res) => {
  try {
    const { 
      name, 
      preview_url, 
      original_url, 
      thumbnail_url, 
      category_id, 
      tags, 
      width, 
      height, 
      file_size,
      color_palette
    } = req.body;
    
    if (!name || !original_url) {
      return res.error('图片名称和原图URL不能为空', 400);
    }
    
    const sql = `
      INSERT INTO background_images (
        name, preview_url, original_url, thumbnail_url, category_id, 
        tags, width, height, file_size, color_palette, status, created_at, updated_at
      )
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1, NOW(), NOW())
    `;
    
    const tagsStr = tags ? (typeof tags === 'object' ? JSON.stringify(tags) : tags) : null;
    const colorPaletteStr = color_palette ? (typeof color_palette === 'object' ? JSON.stringify(color_palette) : color_palette) : null;
    
    const result = await query(sql, [
      name, preview_url, original_url, thumbnail_url, category_id,
      tagsStr, width, height, file_size, colorPaletteStr
    ]);
    
    res.success({ id: result.insertId }, '背景图片创建成功');
  } catch (error) {
    console.error('创建背景图片失败:', error);
    res.error('创建背景图片失败');
  }
});

// 更新背景图片
router.put('/image/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { 
      name, 
      preview_url, 
      original_url, 
      thumbnail_url, 
      category_id, 
      tags, 
      width, 
      height, 
      file_size,
      color_palette
    } = req.body;
    
    const sql = `
      UPDATE background_images 
      SET name = ?, preview_url = ?, original_url = ?, thumbnail_url = ?, category_id = ?, 
          tags = ?, width = ?, height = ?, file_size = ?, color_palette = ?, updated_at = NOW()
      WHERE id = ? AND status = 1
    `;
    
    const tagsStr = tags ? (typeof tags === 'object' ? JSON.stringify(tags) : tags) : null;
    const colorPaletteStr = color_palette ? (typeof color_palette === 'object' ? JSON.stringify(color_palette) : color_palette) : null;
    
    const result = await query(sql, [
      name, preview_url, original_url, thumbnail_url, category_id,
      tagsStr, width, height, file_size, colorPaletteStr, id
    ]);
    
    if (result.affectedRows === 0) {
      return res.error('背景图片不存在或更新失败', 404);
    }
    
    res.success(null, '背景图片更新成功');
  } catch (error) {
    console.error('更新背景图片失败:', error);
    res.error('更新背景图片失败');
  }
});

export default router;
