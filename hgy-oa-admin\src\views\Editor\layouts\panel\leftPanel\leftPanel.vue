<template>
    <a-layout-sider class="sider-box" :class="active?'active':''">
        <div ref="widgetPanel" id="s-widget-panel">
            <div class="s-widget-classify">
                <ul class="s-classify-wrap">
                    <li v-for="(item, index) in widgetClassifyList" :key="index" :class="['s-classify-item', { 's-active-classify-item': activeWidgetClassify === index }]" @click="clickClassify(index)">
                        <component :is="item.icon" class="icon" :size="24"/>
                        <span class="title">{{ item.name }}</span>
                    </li>
                </ul>
                <help>
                    <ul class="b-classify-wrap">
                        <li :class="['b-classify-item']">
                            <icon-question-circle class="icon" />
                            <span class="title">帮助</span>
                        </li>
                    </ul>
                </help>
            </div>
            <div ref="widgetWrap" v-show="active" class="s-widget-wrap">
                <!-- 真正的懒加载：只渲染当前激活的组件 -->
                <component
                    v-if="currentComponent"
                    :is="currentComponent.component"
                    :category-id="currentComponent.categoryId"
                    :category-name="currentComponent.categoryName"
                    :active="true"
                    :key="activeWidgetClassify"
                />
            </div>
            <div v-show="active" class="s-side-wrap">
                <a-tooltip effect="dark" content="收起侧边栏" placement="right">
                    <div class="pack__up" @click="active = false"></div>
                </a-tooltip>
            </div>
        </div>

        <!-- 新建项目弹窗 -->
        <a-modal
            v-model:visible="newProjectModalVisible"
            title="精选推荐"
            width="600px"
            :footer="false"
            class="new-project-modal"
        >
            <div class="template-grid">
                <div
                    v-for="template in templateList"
                    :key="template.id"
                    class="template-item"
                    @click="createNewProject(template)"
                >
                    <div class="template-preview" :style="{ backgroundColor: template.bgColor }">
                        <component :is="template.icon" :size="32" />
                    </div>
                    <div class="template-info">
                        <div class="template-name">{{ template.name }}</div>
                        <div class="template-size">{{ template.size }}</div>
                    </div>
                </div>
            </div>
        </a-modal>
    </a-layout-sider>
</template>
<script setup lang="ts">
import { ref, computed } from 'vue'
import TempListWrap from "./wrap/TempListWrap.vue";
import MaterialWrap from "./wrap/MaterialWrap.vue";
import MyTemplatesWrap from "./wrap/MyTemplatesWrap.vue";
import { useEditor } from '@/views/Editor/app';

const widgetClassifyList =  [
    {
        name: '新建',
        icon: 'icon-plus',
        show: false,
        isNewProject: true,
    },
    {
        name: '模板',
        icon: 'icon-apps',
        show: false,
        component: TempListWrap,
    },
    {
        name: '文字',
        icon: 'icon-edit',
        show: false,
        component: MaterialWrap,
        categoryId: 1,
        categoryName: '文字'
    },
    {
        name: '特效',
        icon: 'icon-star',
        show: false,
        component: MaterialWrap,
        categoryId: 2,
        categoryName: '特效'
    },
    {
        name: '插画',
        icon: 'icon-common',
        show: false,
        component: MaterialWrap,
        categoryId: 3,
        categoryName: '插画'
    },
    {
        name: '背景',
        icon: 'icon-mosaic',
        show: false,
        component: MaterialWrap,
        categoryId: 4,
        categoryName: '背景'
    },
    {
        name: '老师',
        icon: 'icon-user',
        show: false,
        component: MaterialWrap,
        categoryId: 5,
        categoryName: '老师'
    },
    {
        name: '二维码',
        icon: 'icon-qrcode',
        show: false,
        component: MaterialWrap,
        categoryId: 6,
        categoryName: '二维码'
    },
    {
        name: '书籍',
        icon: 'icon-book',
        show: false,
        component: MaterialWrap,
        categoryId: 7,
        categoryName: '书籍'
    },
    {
        name: '其它',
        icon: 'icon-folder',
        show: false,
        component: MaterialWrap,
        categoryId: 8,
        categoryName: '其它'
    },
    {
        name: '我的',
        icon: 'icon-user',
        show: false,
        component: MyTemplatesWrap,
    },
]
const activeWidgetClassify =  ref(1) // 默认选中模板
const active =  ref(true)
const newProjectModalVisible = ref(false)

// 计算当前激活的组件
const currentComponent = computed(() => {
  return widgetClassifyList[activeWidgetClassify.value]
})

// 模板列表数据
const templateList = ref([
    {
        id: 1,
        name: '海报',
        size: '1242*2208',
        width: 1242,
        height: 2208,
        icon: 'icon-file-image',
        bgColor: '#E3F2FD'
    },
    {
        id: 2,
        name: '公众号首图',
        size: '900*383',
        width: 900,
        height: 383,
        icon: 'icon-wechat',
        bgColor: '#E8F5E8'
    },
    {
        id: 3,
        name: '白板',
        size: '1920*1080',
        width: 1920,
        height: 1080,
        icon: 'icon-desktop',
        bgColor: '#FFF3E0'
    },
    {
        id: 4,
        name: '演示PPT',
        size: '1920*1080',
        width: 1920,
        height: 1080,
        icon: 'icon-file-pdf',
        bgColor: '#F3E5F5'
    },
    {
        id: 5,
        name: '小红书封面',
        size: '1242*1660',
        width: 1242,
        height: 1660,
        icon: 'icon-heart',
        bgColor: '#FFEBEE'
    },
    {
        id: 6,
        name: '电商主图',
        size: '800*800',
        width: 800,
        height: 800,
        icon: 'icon-shopping',
        bgColor: '#FFF8E1'
    },
    {
        id: 7,
        name: '直播背景',
        size: '1920*1080',
        width: 1920,
        height: 1080,
        icon: 'icon-live-broadcast',
        bgColor: '#E1F5FE'
    },
    {
        id: 8,
        name: '营销长图',
        size: '750*1334',
        width: 750,
        height: 1334,
        icon: 'icon-image',
        bgColor: '#F9FBE7'
    },
    {
        id: 9,
        name: '横图Banner',
        size: '1920*600',
        width: 1920,
        height: 600,
        icon: 'icon-layout',
        bgColor: '#FCE4EC'
    }
])

const clickClassify = (index: number) => {
    // 如果点击的是"新建"
    if (widgetClassifyList[index].isNewProject) {
        newProjectModalVisible.value = true
        return
    }

    if (activeWidgetClassify.value  === index){
        active.value = !active.value

    }else {
        activeWidgetClassify.value = index
        active.value = true
    }
}

// 创建新项目
const createNewProject = (template: any) => {
    console.log('创建新项目:', template)

    // 关闭弹窗
    newProjectModalVisible.value = false

    // 这里调用编辑器的API创建指定尺寸的画布
    createCanvas(template.width, template.height)
}

// 创建画布的方法
const createCanvas = (width: number, height: number) => {
    try {
        console.log(`创建画布: ${width}x${height}`)

        // 获取编辑器实例
        const editorInstance = useEditor()

        if (editorInstance && editorInstance.editor) {
            const { editor } = editorInstance

            console.log('编辑器实例:', editor)

            // 清空当前画布内容
            if (editor.contentFrame) {
                editor.contentFrame.clear()
                console.log('画布内容已清空')

                // 设置新的画布尺寸
                editor.contentFrame.width = width
                editor.contentFrame.height = height
                console.log(`画布尺寸已设置为: ${width}x${height}`)

                // 适配视图到新尺寸
                editor.zoomToFit()
                console.log('视图已适配到新尺寸')

                // 取消选中任何对象，选中画布本身
                editor.discardActiveObject()

                console.log('画布创建成功，新尺寸:', {
                    width: editor.contentFrame.width,
                    height: editor.contentFrame.height
                })
            } else {
                console.warn('contentFrame未找到')
            }
        } else {
            console.warn('编辑器实例未找到')
        }

    } catch (error) {
        console.error('创建画布失败:', error)
    }
}

const getStyle = (index: number) => {
    return {
        display: activeWidgetClassify.value === index ? '' : 'none',
    }
}
</script>
<style lang="less" scoped>
// Color variables (appears count calculates by raw css)
@import "../../../styles/layouts";
@color1: #3e4651; // Appears 2 times
@menuWidth: 67px; // 默认菜单宽度
@active-text-color: #2254f4; // #1195db;
.sider-box{
  width:@menuWidth !important;
  :deep(.arco-layout-sider-children){
    overflow: initial;
  }
}
.sider-box.active{
  width: calc(@menuWidth + 329px) !important;
}
#s-widget-panel {
  transition: all 1s;
  color: @color1;
  display: flex;
  flex-direction: row;
  font-weight: 600;
  height: calc(100vh - 20px);
  position: relative;
  .s-widget-classify {
    border-right: 1px solid rgba(0, 0, 0, 0.07);
    background-color: #ffffff;
    height: 100%;
    text-align: center;
    display: grid;
    align-content: space-between;
    width: calc(@menuWidth);
    .icon {
      font-size: 24px;
      color: #070707c9;
    }
    .s-classify-wrap {
      margin: 0;
      padding-top: 3px;
      user-select: none;
      width: 100%;
      justify-items: center;
      padding-left: 0;
      max-height: calc(100vh - 90px);
      overflow: auto;
      .s-classify-item {
        position: relative;
        align-items: center;
        cursor: pointer;
        display: flex;
        flex-direction: column;
        font-size: 12px;
        font-weight: 500;
        height: 68px;
        justify-content: center;
        width: 100%;
        .title {
          color: var(--color-text-2);;
          margin-top: 5px;
        }
        .icon {
        }
      }
      .s-classify-item:hover > .icon {
      }
      .s-active-classify-item {
        position: relative;
        .icon,
        .title {
          color:rgb(var(--primary-6));
        }
      }
      .s-active-classify-item::after,
      .s-classify-item:hover::after {
        position: absolute;
        content: '';
        left: 0;
        top: 13px;
        width: 4px;
        height: 65%;
        background:rgb(var(--primary-6));;
      }
    }
    .b-classify-wrap {
      //margin: 0;
      padding-top: 3px;
      margin-bottom: 20px;
      user-select: none;
      width: 100%;
      justify-items: center;
      padding-left: 0;
      .b-classify-item {
        position: relative;
        align-items: center;
        cursor: pointer;
        display: flex;
        flex-direction: column;
        font-size: 12px;
        font-weight: 500;
        height: 68px;
        justify-content: center;
        width: 100%;
        .title {
          color: var(--color-text-2);;
          margin-top: 5px;
        }
        .icon {
        }
      }
      .b-classify-item:hover{
        .icon,
        .title {
          color:rgb(var(--primary-6));
        }
      }
      .b-active-classify-item {
        position: relative;
        .icon,
        .title {
          color:rgb(var(--primary-6));
        }
      }
    }
  }
  .s-widget-wrap {
    width: @leftPanelWidth;
    background-color: #fff;
    flex: 1;
    height: 100%;
  }
  .s-side-wrap {
    position: fixed;
    left: calc(@leftPanelWidth + 66px);
    pointer-events: none;
    z-index: 100;
    width: 20px;
    height: 100%;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
    .pack__up {
      pointer-events: all;
      border-radius: 0 100% 100% 0;
      cursor: pointer;
      width: 20px;
      height: 64px;
      cursor: pointer;
      background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAACACAMAAABOb9vcAAAAhFBMVEUAAAD///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////8AAADHx8cODg50dHTx8fF2dnZ1dXWWlpZHR0c4ODhQpkZ5AAAAIXRSTlMA9t+/upkRAnPq5NXDfDEsKQjMeGlRThkMsquljTwzIWhBHpjgAAABJElEQVRYw+3YyW7CQBCEYbxig8ELGJyQkJRJyPb+75dj3zy/lD7kMH3+ZEuzSFO1mlZwhjOE2uwhVHJYMygNVwilhz2EUvNaMigledUFoE1anKYAtA9nVRuANpviOQBt0t2ZQSnZ9QxK6Qih9LSGUHkJobYlhGp6CPW4hlAVhckLhMop1InCjEK1FBYU1hSqo/BI4YXCjMIthTWFijDCCB3g7fuO4O1t/rkvQXPz/LUIzX0oAM0tQHOfCkBzC9DcuwLQXACao9Dv1yb9lsek2xaaxMcMH1x6Ff79dY0wwgj/DGv3p2tG4cX9wd55h4rCO/hk3uEs9w6QlXPIbXrfIJ6XrmVBOtJCA1YkXqVLkh1aUgyNk1fV1BxLxzpsuNLKzrME/AWr0ywwvyj83AAAAABJRU5ErkJggg==);
      background-repeat: no-repeat;
      background-size: cover;
      background-position: 50%;
      filter: drop-shadow(5px 0px 4px rgba(0, 0, 0, 0.03));
    }
    .pack__up:hover {
      color: rgba(0, 0, 0, 0.9);
      opacity: 0.9;
    }
  }
}

// 新建项目弹窗样式
:deep(.new-project-modal) {
  .arco-modal-header {
    text-align: center;
    border-bottom: 1px solid #e5e6eb;
  }

  .arco-modal-body {
    padding: 24px;
  }
}

.template-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
  max-height: 400px;
  overflow-y: auto;
}

.template-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px;
  border: 1px solid #e5e6eb;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;

  &:hover {
    border-color: #165dff;
    box-shadow: 0 2px 8px rgba(22, 93, 255, 0.15);
    transform: translateY(-2px);
  }
}

.template-preview {
  width: 60px;
  height: 60px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12px;

  .arco-icon {
    color: #666;
  }
}

.template-info {
  text-align: center;
}

.template-name {
  font-size: 14px;
  font-weight: 500;
  color: #1d2129;
  margin-bottom: 4px;
}

.template-size {
  font-size: 12px;
  color: #86909c;
}
</style>
