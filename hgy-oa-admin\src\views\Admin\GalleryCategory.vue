<template>
  <div class="gallery-category">
    <div class="page-header">
      <h1>图片分类管理</h1>
      <div class="header-actions">
        <a-button @click="testAddSubCategory" style="margin-right: 8px;">
          测试添加子分类
        </a-button>
        <a-button type="primary" @click="showCreateModal">
          <template #icon>
            <icon-plus />
          </template>
          新增分类
        </a-button>
      </div>
    </div>

    <!-- 分类树形表格 -->
    <a-card>
      <a-table
        :columns="columns"
        :data="categoryData"
        :pagination="false"
        :loading="loading"
        row-key="id"
        :default-expand-all-rows="true"
        size="medium"
      >
        <template #icon="{ record }">
          <component :is="getIconComponent(record.icon)" v-if="record.icon" />
          <span v-else>-</span>
        </template>
        
        <template #status="{ record }">
          <a-switch
            :model-value="record.is_active === 1"
            @change="(value) => handleStatusChange(record, value)"
            size="small"
          />
        </template>

        <template #created_at="{ record }">
          <div style="font-size: 12px; color: #86909c;">
            {{ formatDateTime(record.created_at) }}
          </div>
        </template>

        <template #actions="{ record }">
          <a-space size="mini">
            <a-button type="text" size="mini" @click="showEditModal(record)">
              编辑
            </a-button>
            <a-button
              type="text"
              size="mini"
              @click="showCreateModal(record)"
              v-if="!record.parent_id"
            >
              添加子分类
            </a-button>
            <a-button type="text" size="mini" status="danger" @click="handleDelete(record)">
              删除
            </a-button>
          </a-space>
        </template>
      </a-table>
    </a-card>

    <!-- 新增/编辑分类弹窗 -->
    <a-modal
      v-model:visible="categoryModalVisible"
      :title="getModalTitle()"
      width="500px"
      @ok="handleCategorySubmit"
      @cancel="handleCategoryCancel"
    >
      <a-form :model="categoryForm" :rules="categoryRules" ref="categoryFormRef" layout="vertical">
        <a-form-item label="分类名称" field="name">
          <a-input v-model="categoryForm.name" placeholder="请输入分类名称" />
        </a-form-item>
        
        <a-form-item label="父分类" field="parent_id">
          <a-select
            v-model="categoryForm.parent_id"
            placeholder="请选择父分类（留空为一级分类）"
            allow-clear
            :disabled="!!parentCategory"
          >
            <a-option :value="null">无（一级分类）</a-option>
            <a-option
              v-for="category in primaryCategories"
              :key="category.id"
              :value="category.id"
            >
              {{ category.name }}
            </a-option>
          </a-select>
          <div v-if="parentCategory" style="margin-top: 4px; color: #86909c; font-size: 12px;">
            将作为"{{ parentCategory.name }}"的子分类
          </div>
        </a-form-item>
        
        <a-form-item label="图标" field="icon" v-if="!categoryForm.parent_id">
          <a-select v-model="categoryForm.icon" placeholder="请选择图标" allow-clear>
            <a-option value="IconCalendar">日历图标</a-option>
            <a-option value="IconHeart">心形图标</a-option>
            <a-option value="IconHome">房屋图标</a-option>
            <a-option value="IconCamera">相机图标</a-option>
            <a-option value="IconTrophy">奖杯图标</a-option>
            <a-option value="IconBook">书本图标</a-option>
            <a-option value="IconGift">礼物图标</a-option>
            <a-option value="IconFire">火焰图标</a-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="排序" field="sort_order">
          <a-input-number v-model="categoryForm.sort_order" :min="0" placeholder="排序值，数字越小越靠前" />
        </a-form-item>
        
        <a-form-item label="资料准备" field="description">
          <a-textarea v-model="categoryForm.description" placeholder="请输入资料准备说明" :rows="3" />
        </a-form-item>
        
        <a-form-item label="状态" field="is_active">
          <a-switch v-model="categoryForm.is_active" checked-text="启用" unchecked-text="禁用" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue'
import { Message, Modal } from '@arco-design/web-vue'
import {
  IconPlus,
  IconCalendar,
  IconHeart,
  IconHome,
  IconCamera,
  IconTrophy,
  IconBook,
  IconGift,
  IconFire
} from '@arco-design/web-vue/es/icon'
import {
  getCategoryList,
  createCategory,
  updateCategory,
  deleteCategory,
  type Category
} from '@/api/gallery'

// 响应式数据
const loading = ref(false)
const categoryData = ref([])
const primaryCategories = ref([])
const categoryModalVisible = ref(false)
const isEdit = ref(false)
const currentCategoryId = ref(null)
const parentCategory = ref(null)

// 表单数据
const categoryForm = reactive({
  name: '',
  parent_id: null,
  icon: '',
  sort_order: 0,
  description: '',
  is_active: true
})

// 表单引用
const categoryFormRef = ref()

// 表格列配置
const columns = [
  { title: '分类名称', dataIndex: 'name', width: 150 },
  { title: '图标', slotName: 'icon', width: 50, align: 'center' },
  { title: '排序', dataIndex: 'sort_order', width: 60, align: 'center' },
  { title: '资料准备', dataIndex: 'description', width: 230, ellipsis: true, tooltip: true },
  { title: '状态', slotName: 'status', width: 60, align: 'center' },
  { title: '创建时间', slotName: 'created_at', width: 110 },
  { title: '操作', slotName: 'actions', width: 100, align: 'center', fixed: 'right' }
]

// 表单验证规则
const categoryRules = {
  name: [
    { required: true, message: '请输入分类名称' },
    { maxLength: 100, message: '分类名称最多100个字符' }
  ],
  sort_order: [
    { required: true, message: '请输入排序值' }
  ]
}

// 图标组件映射
const getIconComponent = (iconName: string) => {
  const iconMap: Record<string, any> = {
    'IconCalendar': IconCalendar,
    'IconHeart': IconHeart,
    'IconHome': IconHome,
    'IconCamera': IconCamera,
    'IconTrophy': IconTrophy,
    'IconBook': IconBook,
    'IconGift': IconGift,
    'IconFire': IconFire
  }
  return iconMap[iconName] || IconFire
}

// 格式化时间
const formatDateTime = (dateTime: string | Date | null | undefined): string => {
  if (!dateTime) return '-'
  const date = new Date(dateTime)
  if (isNaN(date.getTime())) return '-'

  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')

  return `${year}-${month}-${day}`
}

// 获取弹窗标题
const getModalTitle = () => {
  if (isEdit.value) {
    return '编辑分类'
  } else if (parentCategory.value) {
    return `为"${parentCategory.value.name}"添加子分类`
  } else {
    return '新增分类'
  }
}

// 获取分类列表
const fetchCategoryList = async () => {
  loading.value = true
  try {
    console.log('开始获取分类列表...')
    const response = await getCategoryList()
    console.log('分类列表API响应:', response)

    if (response.success) {
      // API返回的是树形结构，直接使用
      const treeData = response.data

      // Arco Design表格支持树形数据，直接使用API返回的结构
      categoryData.value = treeData
      primaryCategories.value = treeData.filter((item: Category) => !item.parent_id)
      console.log('分类数据更新成功:', categoryData.value)
      console.log('一级分类:', primaryCategories.value)
    } else {
      throw new Error(response.msg || '获取分类列表失败')
    }

  } catch (error) {
    console.error('获取分类列表失败:', error)
    Message.error('获取分类列表失败')

    // 如果API失败，显示一些模拟数据作为后备
    const mockData: Category[] = [
      {
        id: 1,
        name: '节日',
        parent_id: null,
        icon: 'IconCalendar',
        sort_order: 1,
        description: '节日相关图片分类',
        is_active: 1,
        created_at: '2024-01-01 10:00:00',
        updated_at: '2024-01-01 10:00:00',
        children: [
          {
            id: 2,
            name: '端午节',
            parent_id: 1,
            icon: null,
            sort_order: 1,
            description: null,
            is_active: 1,
            created_at: '2024-01-01 10:01:00',
            updated_at: '2024-01-01 10:01:00',
            children: []
          },
          {
            id: 3,
            name: '中秋节',
            parent_id: 1,
            icon: null,
            sort_order: 2,
            description: null,
            is_active: 1,
            created_at: '2024-01-01 10:02:00',
            updated_at: '2024-01-01 10:02:00',
            children: []
          }
        ]
      }
    ]

    categoryData.value = mockData
    primaryCategories.value = mockData.filter(item => !item.parent_id)
  } finally {
    loading.value = false
  }
}

// 显示新增弹窗
const showCreateModal = (parent: Category | null = null) => {
  isEdit.value = false
  parentCategory.value = parent
  currentCategoryId.value = null
  resetCategoryForm()
  if (parent) {
    categoryForm.parent_id = parent.id
  }
  categoryModalVisible.value = true

  // 确保表单在下一个tick中重置验证状态
  nextTick(() => {
    categoryFormRef.value?.clearValidate()
  })
}

// 显示编辑弹窗
const showEditModal = (record: Category) => {
  isEdit.value = true
  parentCategory.value = null
  currentCategoryId.value = record.id
  categoryForm.name = record.name
  categoryForm.parent_id = record.parent_id
  categoryForm.icon = record.icon || ''
  categoryForm.sort_order = record.sort_order
  categoryForm.description = record.description || ''
  categoryForm.is_active = record.is_active === 1
  categoryModalVisible.value = true
}

// 重置表单
const resetCategoryForm = () => {
  categoryForm.name = ''
  categoryForm.parent_id = null
  categoryForm.icon = ''
  categoryForm.sort_order = 0
  categoryForm.description = ''
  categoryForm.is_active = true
}

// 分类表单提交
const handleCategorySubmit = async () => {
  console.log('开始提交分类表单 111 ...')
  console.log('表单引用:', categoryFormRef.value)
  console.log('当前表单数据:', categoryForm)

  try {
    // 检查表单引用是否存在
    if (!categoryFormRef.value) {
      console.error('表单引用不存在')
      Message.error('表单初始化失败')
      return
    }

    // Arco Design的validate()方法：成功时返回undefined，失败时返回错误对象
    const errors = await categoryFormRef.value.validate()
    console.log('表单验证结果:', errors)

    // 如果有错误，停止提交
    if (errors) {
      console.log('表单验证失败:', errors)
      return
    }

    console.log('表单验证通过，开始提交数据')

    console.log('提交表单数据:', categoryForm)
    console.log('是否编辑:', isEdit.value)
    console.log('父分类:', parentCategory.value)

    // 准备提交的数据
    const submitData = {
      name: categoryForm.name,
      parent_id: categoryForm.parent_id,
      icon: categoryForm.icon || undefined,
      sort_order: categoryForm.sort_order,
      description: categoryForm.description || undefined,
      is_active: (categoryForm.is_active ? 1 : 0) as 0 | 1
    }

    console.log('准备提交的数据:', submitData)

    // 调用真实的API
    if (isEdit.value && currentCategoryId.value) {
      console.log('调用更新分类API...')
      const response = await updateCategory(currentCategoryId.value, submitData)
      console.log('更新分类API响应:', response)

      if (response.success) {
        Message.success('编辑分类成功')
      } else {
        throw new Error(response.msg || '编辑分类失败')
      }
    } else {
      console.log('调用创建分类API...')
      const response = await createCategory(submitData)
      console.log('创建分类API响应:', response)

      if (response.success) {
        if (parentCategory.value) {
          Message.success(`成功为"${parentCategory.value.name}"添加子分类"${categoryForm.name}"`)
        } else {
          Message.success('新增分类成功')
        }
      } else {
        throw new Error(response.msg || '创建分类失败')
      }
    }

    categoryModalVisible.value = false
    fetchCategoryList()
  } catch (error: any) {
    console.error('保存分类失败:', error)
    Message.error(error.message || '保存分类失败')
  }
}

// 分类表单取消
const handleCategoryCancel = () => {
  categoryModalVisible.value = false
  categoryFormRef.value?.resetFields()
}

// 状态切换
const handleStatusChange = async (record: Category, value: boolean) => {
  try {
    const updateData = {
      name: record.name,
      parent_id: record.parent_id,
      icon: record.icon,
      sort_order: record.sort_order,
      description: record.description,
      is_active: (value ? 1 : 0) as 0 | 1
    }

    const response = await updateCategory(record.id, updateData)

    if (response.success) {
      record.is_active = value ? 1 : 0
      Message.success(`${value ? '启用' : '禁用'}分类成功`)
    } else {
      throw new Error(response.msg || '更新分类状态失败')
    }
  } catch (error: any) {
    console.error('更新分类状态失败:', error)
    Message.error(error.message || '更新分类状态失败')
    // 恢复开关状态
    record.is_active = record.is_active === 1 ? 0 : 1
  }
}

// 删除分类
const handleDelete = (record: any) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除分类"${record.name}"吗？删除后该分类下的所有图片将失去分类信息。`,
    onOk: async () => {
      try {
        console.log('调用删除分类API...', record.id)
        const response = await deleteCategory(record.id)
        console.log('删除分类API响应:', response)

        if (response.success) {
          Message.success('删除分类成功')
          fetchCategoryList()
        } else {
          throw new Error(response.msg || '删除分类失败')
        }
      } catch (error: any) {
        console.error('删除分类失败:', error)
        Message.error(error.message || '删除分类失败')
      }
    }
  })
}

// 页面加载时获取数据
onMounted(() => {
  fetchCategoryList()
})
</script>

<style scoped>
.gallery-category {
  padding: 0;
  max-width: 100%;
  overflow: hidden;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0;
  color: #262626;
}

/* 表格样式优化 */
:deep(.arco-table) {
  font-size: 14px;
}

:deep(.arco-table-th) {
  background-color: #fafafa;
  font-weight: 600;
  white-space: nowrap;
}

:deep(.arco-table-td) {
  border-bottom: 1px solid #f0f0f0;
  padding: 12px 16px;
}

/* 操作按钮样式 */
:deep(.arco-table-td .arco-space-item .arco-btn-text) {
  padding: 2px 6px;
  font-size: 12px;
  height: auto;
  min-height: 24px;
}

/* 描述列样式 */
:deep(.arco-table-td .arco-typography) {
  margin-bottom: 0;
}

/* 状态开关样式 */
:deep(.arco-switch-small) {
  min-width: 32px;
}
</style>
