<template>
  <div class="user-list">
    <div class="page-header">
      <h1>用户列表</h1>
      <div class="header-actions">
        <a-button @click="handleRefresh" style="margin-right: 8px;">
          <template #icon>
            <icon-refresh />
          </template>
          刷新
        </a-button>
        <a-button type="primary" @click="showCreateModal">
          <template #icon>
            <icon-plus />
          </template>
          新增用户
        </a-button>

      </div>
    </div>

    <!-- 搜索筛选 -->
    <a-card style="margin-bottom: 16px;">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-input
            v-model="searchForm.keyword"
            placeholder="搜索用户名"
            allow-clear
            @press-enter="handleSearch"
          >
            <template #prefix>
              <icon-search />
            </template>
          </a-input>
        </a-col>
        <a-col :span="4">
          <a-select
            v-model="searchForm.status"
            placeholder="状态"
            allow-clear
            allow-search
            :filter-option="statusFilterOption"
          >
            <a-option value="1">正常</a-option>
            <a-option value="0">禁用</a-option>
          </a-select>
        </a-col>
        <a-col :span="6">
          <a-button type="primary" @click="handleSearch">搜索</a-button>
          <a-button style="margin-left: 8px;" @click="handleReset">重置</a-button>
        </a-col>

      </a-row>
    </a-card>
    
    <a-card>
      <a-table
        :columns="columns"
        :data="userData"
        :pagination="pagination"
        :loading="loading"
        @page-change="handlePageChange"
        @page-size-change="handlePageSizeChange"
      >
        <template #avatar="{ record }">
          <a-avatar :size="32" :style="{ backgroundColor: getAvatarColor(record.username) }">
            {{ record.username.charAt(0).toUpperCase() }}
          </a-avatar>
        </template>
        
        <template #status="{ record }">
          <a-switch
            :model-value="record.is_active === 1"
            @change="(value) => handleStatusChange(record, value)"
            :loading="record.statusLoading"
          />
        </template>
        
        <template #lastLogin="{ record }">
          <div v-if="record.last_login">
            <div>{{ formatDateTime(record.last_login) }}</div>
          </div>
          <span v-else style="color: #999;">从未登录</span>
        </template>

        <template #department="{ record }">
          <span v-if="record.department_name">{{ record.department_name }}</span>
          <span v-else style="color: #999;">未分配</span>
        </template>

        <template #actions="{ record }">
          <a-space size="mini">
            <a-button type="text" size="small" @click="showEditModal(record)">
              编辑
            </a-button>
            <a-button type="text" size="small" @click="showResetPasswordModal(record)">
              重置密码
            </a-button>
            <a-button type="text" size="small" status="danger" @click="handleDelete(record)">
              删除
            </a-button>
          </a-space>
        </template>
      </a-table>
    </a-card>

    <!-- 新增/编辑用户弹窗 -->
    <a-modal
      v-model:visible="userModalVisible"
      :title="isEdit ? '编辑用户' : '新增用户'"
      width="600px"
      @ok="handleUserSubmit"
      @cancel="handleUserCancel"
    >
      <a-form :model="userForm" :rules="userRules" ref="userFormRef" layout="vertical">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="用户名" field="username">
              <a-input v-model="userForm.username" placeholder="请输入用户名" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="中文名" field="nickname">
              <a-input v-model="userForm.nickname" placeholder="请输入中文名" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="部门" field="department_id">
              <a-select
                v-model="userForm.department_id"
                placeholder="请选择部门"
                allow-clear
                allow-search
                :filter-option="departmentFilterOption"
              >
                <a-option v-for="dept in departmentOptions" :key="dept.id" :value="dept.id">
                  {{ dept.name }}
                </a-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="角色" field="role">
              <a-select
                v-model="userForm.role"
                placeholder="请选择角色"
                allow-search
                :filter-option="roleFilterOption"
              >
                <a-option v-for="role in roleOptions" :key="role.name" :value="role.name">
                  {{ role.display_name }}
                </a-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="状态" field="is_active">
              <a-select
                v-model="userForm.is_active"
                placeholder="请选择状态"
                allow-search
                :filter-option="statusFilterOption"
              >
                <a-option :value="1">正常</a-option>
                <a-option :value="0">禁用</a-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-row :gutter="16" v-if="!isEdit">
          <a-col :span="12">
            <a-form-item label="密码" field="password">
              <a-input-password v-model="userForm.password" placeholder="请输入密码" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="确认密码" field="confirmPassword">
              <a-input-password v-model="userForm.confirmPassword" placeholder="请确认密码" />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-modal>

    <!-- 重置密码弹窗 -->
    <a-modal
      v-model:visible="passwordModalVisible"
      title="重置密码"
      width="400px"
      @ok="handlePasswordSubmit"
      @cancel="handlePasswordCancel"
    >
      <a-form :model="passwordForm" :rules="passwordRules" ref="passwordFormRef" layout="vertical">
        <a-form-item label="新密码" field="password">
          <a-input-password v-model="passwordForm.password" placeholder="请输入新密码" />
        </a-form-item>
        <a-form-item label="确认密码" field="confirmPassword">
          <a-input-password v-model="passwordForm.confirmPassword" placeholder="请确认新密码" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { Message, Modal } from '@arco-design/web-vue'
import { 
  IconPlus, 
  IconRefresh, 
  IconSearch 
} from '@arco-design/web-vue/es/icon'
import {
  getUserList,
  createUser,
  updateUser,
  deleteUser,
  updateUserStatus,
  resetUserPassword,
  type User
} from '@/api/user'
import {
  getAllDepartments,
  type Department
} from '@/api/gallery'
import {
  getRoleList,
  type Role
} from '@/api/role'
import { createSelectFilter } from '@/utils/pinyin-filter'

// 响应式数据
const loading = ref(false)
const userData = ref<User[]>([])
const departmentOptions = ref<Department[]>([])
const roleOptions = ref<Array<{ name: string; display_name: string }>>([
  { name: 'admin', display_name: '管理员' },
  { name: 'manager', display_name: '经理' },
  { name: 'designer', display_name: '设计' },
  { name: 'developer', display_name: '项目' },
  { name: 'salesman', display_name: '业务' },
  { name: 'guest', display_name: '游客' }
])

// 角色映射
const roleMap = {
  admin: '管理员',
  manager: '经理',
  designer: '设计',
  developer: '项目',
  salesman: '业务',
  guest: '游客'
}

// 搜索表单
const searchForm = reactive({
  keyword: '',
  status: ''
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showTotal: true,
  showPageSize: true
})

// 表格列配置
const columns = [
  { title: '头像', slotName: 'avatar', width: 80, align: 'center' },
  { title: '用户名', dataIndex: 'username', width: 150 },
  { title: '中文名', dataIndex: 'nickname', width: 120 },
  { title: '部门', slotName: 'department', width: 150 },
  {
    title: '角色',
    dataIndex: 'role',
    width: 100,
    render: ({ record }: { record: User }) => {
      return roleMap[record.role as keyof typeof roleMap] || record.role || '未知'
    }
  },
  { title: '状态', slotName: 'status', width: 100, align: 'center' },
  { title: '最后登录', slotName: 'lastLogin', width: 160 },
  { title: '创建时间', dataIndex: 'created_at', width: 160, render: ({ record }: { record: User }) => formatDateTime(record.created_at) },
  { title: '操作', slotName: 'actions', width: 180, align: 'center', fixed: 'right' }
]



// 弹窗相关
const userModalVisible = ref(false)
const passwordModalVisible = ref(false)
const isEdit = ref(false)
const currentUserId = ref<number | null>(null)

// 表单数据
const userForm = reactive({
  username: '',
  nickname: '',
  department_id: undefined as number | undefined,
  role: 'admin' as 'admin' | 'manager' | 'designer' | 'developer' | 'salesman' | 'guest',
  password: '',
  confirmPassword: '',
  is_active: 1
})

const passwordForm = reactive({
  password: '',
  confirmPassword: ''
})

// 表单引用
const userFormRef = ref()
const passwordFormRef = ref()

// 表单验证规则 - 动态计算
const userRules = computed(() => {
  const rules: any = {
    username: [
      { required: true, message: '请输入用户名' },
      { minLength: 3, message: '用户名至少3个字符' },
      { maxLength: 20, message: '用户名最多20个字符' }
    ],
    is_active: [{ required: true, message: '请选择状态' }]
  }

  // 只有在新增模式下才需要密码验证
  if (!isEdit.value) {
    rules.password = [
      { required: true, message: '请输入密码' },
      { minLength: 6, message: '密码至少6个字符' }
    ]
    rules.confirmPassword = [
      { required: true, message: '请确认密码' },
      {
        validator: (value: string, callback: Function) => {
          if (value !== userForm.password) {
            callback('两次输入的密码不一致')
          } else {
            callback()
          }
        }
      }
    ]
  }

  return rules
})

const passwordRules = {
  password: [
    { required: true, message: '请输入新密码' },
    { minLength: 6, message: '密码至少6个字符' }
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码' },
    {
      validator: (value: string, callback: Function) => {
        if (value !== passwordForm.password) {
          callback('两次输入的密码不一致')
        } else {
          callback()
        }
      }
    }
  ]
}

// 工具函数
const formatDateTime = (dateTime: string) => {
  if (!dateTime) return '-'
  const date = new Date(dateTime)
  if (isNaN(date.getTime())) return '-'

  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')

  return `${year}-${month}-${day}`
}

const getAvatarColor = (username: string) => {
  const colors = ['#1890ff', '#52c41a', '#faad14', '#f5222d', '#722ed1', '#13c2c2']
  const index = username.charCodeAt(0) % colors.length
  return colors[index]
}

// 获取部门列表
const fetchDepartmentList = async () => {
  try {
    const response = await getAllDepartments()
    if (response.success) {
      departmentOptions.value = response.data
    }
  } catch (error) {
    console.error('获取部门列表失败:', error)
  }
}

// 获取角色列表
const fetchRoleList = async () => {
  // 角色选项已在初始化时设置，这里可以添加API调用逻辑
  console.log('角色选项已设置:', roleOptions.value)
}

// 拼音筛选函数
const departmentFilterOption = createSelectFilter('name')
const roleFilterOption = createSelectFilter('display_name')
const statusFilterOption = (inputValue: string, option: any) => {
  if (!inputValue) return true
  const label = option.label || option.children || ''
  const searchLower = inputValue.toLowerCase()

  // 支持中英文筛选
  if (label.includes('正常') && (searchLower.includes('z') || searchLower.includes('zheng') || searchLower.includes('normal'))) {
    return true
  }
  if (label.includes('禁用') && (searchLower.includes('j') || searchLower.includes('jin') || searchLower.includes('disable'))) {
    return true
  }

  return label.toLowerCase().includes(searchLower)
}

// 获取用户列表
const fetchUserList = async () => {
  loading.value = true
  try {
    const params = {
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
      keyword: searchForm.keyword,
      status: searchForm.status
    }

    const response = await getUserList(params)
    userData.value = response.data.records
    pagination.total = response.data.total
  } catch (error) {
    console.error('获取用户列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.current = 1
  fetchUserList()
}

// 重置搜索
const handleReset = () => {
  searchForm.keyword = ''
  searchForm.status = ''
  pagination.current = 1
  fetchUserList()
}

// 刷新
const handleRefresh = () => {
  fetchUserList()
}

// 分页处理
const handlePageChange = (page: number) => {
  pagination.current = page
  fetchUserList()
}

const handlePageSizeChange = (pageSize: number) => {
  pagination.pageSize = pageSize
  pagination.current = 1
  fetchUserList()
}

// 显示新增弹窗
const showCreateModal = () => {
  isEdit.value = false
  currentUserId.value = null
  resetUserForm()
  userModalVisible.value = true
}

// 显示编辑弹窗
const showEditModal = (record: User) => {
  isEdit.value = true
  currentUserId.value = record.id
  userForm.username = record.username
  userForm.nickname = record.nickname || ''
  userForm.department_id = record.department_id
  userForm.role = record.role || 'guest'
  userForm.is_active = record.is_active
  userModalVisible.value = true
}

// 重置用户表单
const resetUserForm = () => {
  userForm.username = ''
  userForm.nickname = ''
  userForm.department_id = undefined
  userForm.role = 'guest'
  userForm.password = ''
  userForm.confirmPassword = ''
  userForm.is_active = 1
}

// 用户表单提交
const handleUserSubmit = async () => {
  console.log('表单数据:', userForm)
  try {
    // Arco Design的validate方法，成功时返回undefined，失败时抛出异常
    await userFormRef.value?.validate()
    console.log('表单验证通过')

    if (isEdit.value && currentUserId.value) {
      // 编辑用户
      await updateUser(currentUserId.value, {
        username: userForm.username,
        nickname: userForm.nickname || undefined,
        department_id: userForm.department_id || undefined,
        role: userForm.role,
        is_active: userForm.is_active as 0 | 1
      })
      Message.success('编辑用户成功')
    } else {
      // 新增用户
      await createUser({
        username: userForm.username,
        nickname: userForm.nickname || undefined,
        department_id: userForm.department_id || undefined,
        role: userForm.role,
        password: userForm.password,
        is_active: userForm.is_active as 0 | 1
      })
      Message.success('新增用户成功')
    }

    userModalVisible.value = false
    fetchUserList()
  } catch (error) {
    console.error('操作失败:', error)
    // 如果是表单验证错误，不显示错误消息（Arco会自动显示）
    if (error && typeof error === 'object' && 'field' in error) {
      console.log('表单验证失败')
      return
    }
    // API调用错误
    Message.error(`保存用户失败: ${(error as any)?.message || error}`)
  }
}

// 用户表单取消
const handleUserCancel = () => {
  userModalVisible.value = false
  userFormRef.value?.resetFields()
}

// 状态切换
const handleStatusChange = async (record: User & { statusLoading?: boolean }, value: boolean) => {
  record.statusLoading = true
  try {
    await updateUserStatus(record.id, value ? 1 : 0)
    record.is_active = value ? 1 : 0
    Message.success(`${value ? '启用' : '禁用'}用户成功`)
  } catch (error) {
    console.error('更新用户状态失败:', error)
  } finally {
    record.statusLoading = false
  }
}

// 显示重置密码弹窗
const showResetPasswordModal = (record: User) => {
  currentUserId.value = record.id
  passwordForm.password = ''
  passwordForm.confirmPassword = ''
  passwordModalVisible.value = true
}

// 重置密码提交
const handlePasswordSubmit = async () => {
  try {
    const valid = await passwordFormRef.value?.validate()
    if (!valid) return

    if (currentUserId.value) {
      await resetUserPassword(currentUserId.value, passwordForm.password)
      Message.success('重置密码成功')
      passwordModalVisible.value = false
    }
  } catch (error) {
    console.error('重置密码失败:', error)
  }
}

// 重置密码取消
const handlePasswordCancel = () => {
  passwordModalVisible.value = false
  passwordFormRef.value?.resetFields()
}

// 删除用户
const handleDelete = (record: User) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除用户"${record.username}"吗？此操作不可恢复。`,
    onOk: async () => {
      try {
        await deleteUser(record.id)
        Message.success('删除用户成功')
        fetchUserList()
      } catch (error) {
        console.error('删除用户失败:', error)
      }
    }
  })
}





// 页面加载时获取数据
onMounted(() => {
  fetchDepartmentList()
  fetchRoleList()
  fetchUserList()
})
</script>

<style scoped>
.user-list {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0;
  color: #262626;
}

.header-actions {
  display: flex;
  align-items: center;
}

:deep(.arco-table-th) {
  background-color: #fafafa;
}

:deep(.arco-table-td) {
  border-bottom: 1px solid #f0f0f0;
}

:deep(.arco-switch-small) {
  min-width: 28px;
}
</style>
