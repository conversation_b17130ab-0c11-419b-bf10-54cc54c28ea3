import express from 'express';
import multer from 'multer';
import { query } from '../config/database.js';
import { uploadToQiniu, generateFileKey, deleteFromQiniu, getFileUrl, getPrivateFileUrl } from '../config/qiniu.js';
import { optionalAuth } from '../middleware/auth.js';

const router = express.Router();

// 配置multer用于文件上传
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB
  },
  fileFilter: (req, file, cb) => {
    // 修复文件名编码问题
    try {
      file.originalname = Buffer.from(file.originalname, 'latin1').toString('utf8');
    } catch (error) {
      console.log('文件名编码转换失败，使用原始名称:', file.originalname);
    }

    const allowedTypes = [
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'image/jpeg',
      'image/jpg',
      'image/png',
      'image/gif'
    ];

    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('只支持 Word、Excel、图片格式'), false);
    }
  }
});

// 获取文件类型
const getFileType = (mimetype) => {
  if (mimetype.includes('word')) return 'word';
  if (mimetype.includes('excel') || mimetype.includes('sheet')) return 'excel';
  if (mimetype.includes('image')) return 'image';
  return 'other';
};

// 获取文案列表
router.get('/', optionalAuth, async (req, res) => {
  try {
    const { page = 1, pageSize = 10, keyword = '', fileType = '' } = req.query;
    const offset = (page - 1) * pageSize;

    // 先检查表是否存在
    try {
      await query('SELECT 1 FROM documents LIMIT 1');
    } catch (tableError) {
      console.log('documents表不存在，返回空数据');
      return res.json({
        success: true,
        data: {
          list: [],
          total: 0,
          page: parseInt(page),
          pageSize: parseInt(pageSize)
        }
      });
    }

    let whereClause = 'WHERE deleted_at IS NULL';
    const params = [];

    if (keyword) {
      whereClause += ' AND title LIKE ?';
      params.push(`%${keyword}%`);
    }

    if (fileType) {
      whereClause += ' AND file_type = ?';
      params.push(fileType);
    }

    // 获取文案列表
    const sql = `
      SELECT
        id,
        title,
        filename,
        file_type,
        file_size,
        file_path,
        qiniu_key,
        remark,
        description,
        is_public,
        upload_user,
        upload_user as upload_user_nickname,
        created_at,
        updated_at
      FROM documents
      ${whereClause}
      ORDER BY created_at DESC
      LIMIT ${parseInt(pageSize)} OFFSET ${offset}
    `;

    console.log('执行SQL:', sql);
    console.log('参数:', params);

    const documents = await query(sql, params);

    // 获取总数
    const countSql = `
      SELECT COUNT(*) as total
      FROM documents
      ${whereClause}
    `;

    const countResult = await query(countSql, params);

    const total = countResult[0].total;

    res.json({
      success: true,
      data: {
        list: documents,
        total,
        page: parseInt(page),
        pageSize: parseInt(pageSize)
      }
    });
  } catch (error) {
    console.error('获取文案列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取文案列表失败',
      error: error.message
    });
  }
});

// 上传文案
router.post('/upload', optionalAuth, upload.single('file'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: '请选择文件'
      });
    }

    const { title, description = '', remark = '', is_public = '1' } = req.body;
    const file = req.file;

    if (!title) {
      return res.status(400).json({
        success: false,
        message: '请输入文件名称'
      });
    }

    // 生成文件key
    const fileExtension = file.originalname.split('.').pop();
    const fileKey = generateFileKey(file.originalname, 'documents');

    // 上传到七牛云
    const qiniuResult = await uploadToQiniu(file.buffer, fileKey, file.originalname);

    // 为私有Bucket生成带签名的下载URL
    let fileUrl;
    try {
      fileUrl = getPrivateFileUrl(qiniuResult.key, 86400 * 365); // 1年有效期
    } catch (error) {
      fileUrl = qiniuResult.url; // 降级使用公开URL
    }

    const fileType = getFileType(file.mimetype);

    // 保存到数据库，同时保存七牛云key
    const result = await query(`
      INSERT INTO documents (
        title, filename, file_type, file_size, file_path, qiniu_key,
        remark, description, is_public, upload_user, created_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
    `, [
      title,
      file.originalname, // 文件名已在multer中处理编码
      fileType,
      file.size,
      fileUrl,
      qiniuResult.key, // 保存七牛云key
      remark,
      description,
      parseInt(is_public),
      req.user?.username || 'anonymous'
    ]);

    res.json({
      success: true,
      message: '文案上传成功',
      data: {
        id: result.insertId,
        title,
        filename: file.originalname,
        file_type: fileType,
        file_size: file.size,
        file_path: fileUrl,
        remark,
        description,
        is_public: parseInt(is_public)
      }
    });
  } catch (error) {
    console.error('上传文案失败:', error);
    res.status(500).json({
      success: false,
      message: '上传文案失败'
    });
  }
});

// 更新文案信息
router.put('/:id', optionalAuth, async (req, res) => {
  try {
    const { id } = req.params;
    const { title, description, remark, is_public } = req.body;

    const result = await query(`
      UPDATE documents
      SET title = ?, description = ?, remark = ?, is_public = ?, updated_at = NOW()
      WHERE id = ? AND deleted_at IS NULL
    `, [title, description || '', remark || '', is_public ? 1 : 0, id]);

    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        message: '文案不存在'
      });
    }

    res.json({
      success: true,
      message: '更新成功'
    });
  } catch (error) {
    console.error('更新文案失败:', error);
    res.status(500).json({
      success: false,
      message: '更新文案失败'
    });
  }
});

// 更新文案状态
router.put('/:id/status', optionalAuth, async (req, res) => {
  try {
    const { id } = req.params;
    const { is_public } = req.body;

    const result = await query(`
      UPDATE documents 
      SET is_public = ?, updated_at = NOW()
      WHERE id = ? AND deleted_at IS NULL
    `, [is_public ? 1 : 0, id]);

    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        message: '文案不存在'
      });
    }

    res.json({
      success: true,
      message: '状态更新成功'
    });
  } catch (error) {
    console.error('更新文案状态失败:', error);
    res.status(500).json({
      success: false,
      message: '更新文案状态失败'
    });
  }
});

// 删除文案（软删除）
router.delete('/:id', optionalAuth, async (req, res) => {
  try {
    const { id } = req.params;

    const result = await query(`
      UPDATE documents 
      SET deleted_at = NOW()
      WHERE id = ? AND deleted_at IS NULL
    `, [id]);

    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        message: '文案不存在'
      });
    }

    res.json({
      success: true,
      message: '删除成功'
    });
  } catch (error) {
    console.error('删除文案失败:', error);
    res.status(500).json({
      success: false,
      message: '删除文案失败'
    });
  }
});

// 下载文件
router.get('/:id/download', async (req, res) => {
  try {
    const { id } = req.params;
    console.log(`下载请求 - 文档ID: ${id}`);

    // 查询文档信息
    const documents = await query('SELECT * FROM documents WHERE id = ? AND deleted_at IS NULL', [id]);

    if (documents.length === 0) {
      console.log(`文档不存在 - ID: ${id}`);
      return res.status(404).json({
        success: false,
        message: '文档不存在'
      });
    }

    const document = documents[0];
    console.log(`文档信息:`, {
      id: document.id,
      filename: document.filename,
      qiniu_key: document.qiniu_key,
      file_path: document.file_path ? '存在' : '不存在'
    });

    // 如果有七牛云key，重新生成下载URL（确保URL有效）
    if (document.qiniu_key) {
      try {
        console.log(`使用七牛云key生成下载URL: ${document.qiniu_key}`);
        // 使用保存的七牛云key重新生成私有URL，有效期1小时
        const { getPrivateFileUrl } = await import('../config/qiniu.js');
        const newFileUrl = getPrivateFileUrl(document.qiniu_key, 3600);

        console.log(`生成的下载URL: ${newFileUrl}`);

        // 获取文件扩展名
        const fileExtension = document.filename.split('.').pop();
        // 使用格式：文案文件名.格式
        const downloadFilename = `${document.title}.${fileExtension}`;
        const encodedFilename = encodeURIComponent(downloadFilename);

        // 设置下载响应头
        res.setHeader('Content-Disposition', `attachment; filename*=UTF-8''${encodedFilename}`);

        // 代理下载而不是重定向，确保文件名正确
        try {
          const fetch = (await import('node-fetch')).default;
          const fileResponse = await fetch(newFileUrl);

          if (!fileResponse.ok) {
            throw new Error(`HTTP ${fileResponse.status}`);
          }

          // 设置正确的Content-Type
          const contentType = fileResponse.headers.get('content-type') || 'application/octet-stream';
          res.setHeader('Content-Type', contentType);

          // 流式传输文件内容
          fileResponse.body.pipe(res);
        } catch (proxyError) {
          console.error('代理下载失败，降级为重定向:', proxyError);
          res.redirect(newFileUrl);
        }
      } catch (error) {
        console.error('生成下载URL失败:', error);
        // 如果生成失败，直接使用原URL
        if (document.file_path) {
          console.log('降级使用原file_path');
          // 获取文件扩展名
          const fileExtension = document.filename.split('.').pop();
          // 使用格式：文案文件名.格式
          const downloadFilename = `${document.title}.${fileExtension}`;
          const encodedFilename = encodeURIComponent(downloadFilename);
          res.setHeader('Content-Disposition', `attachment; filename*=UTF-8''${encodedFilename}`);

          // 代理下载而不是重定向，确保文件名正确
          try {
            const fetch = (await import('node-fetch')).default;
            const fileResponse = await fetch(document.file_path);

            if (!fileResponse.ok) {
              throw new Error(`HTTP ${fileResponse.status}`);
            }

            // 设置正确的Content-Type
            const contentType = fileResponse.headers.get('content-type') || 'application/octet-stream';
            res.setHeader('Content-Type', contentType);

            // 流式传输文件内容
            fileResponse.body.pipe(res);
          } catch (proxyError) {
            console.error('代理下载失败，降级为重定向:', proxyError);
            res.redirect(document.file_path);
          }
        } else {
          res.status(500).json({
            success: false,
            message: '生成下载链接失败'
          });
        }
      }
    } else if (document.file_path) {
      // 如果没有七牛云key但有file_path，直接使用
      console.log('使用file_path直接下载');
      // 获取文件扩展名
      const fileExtension = document.filename.split('.').pop();
      // 使用格式：文案文件名.格式
      const downloadFilename = `${document.title}.${fileExtension}`;
      const encodedFilename = encodeURIComponent(downloadFilename);
      res.setHeader('Content-Disposition', `attachment; filename*=UTF-8''${encodedFilename}`);

      // 代理下载而不是重定向，确保文件名正确
      try {
        const fetch = (await import('node-fetch')).default;
        const fileResponse = await fetch(document.file_path);

        if (!fileResponse.ok) {
          throw new Error(`HTTP ${fileResponse.status}`);
        }

        // 设置正确的Content-Type
        const contentType = fileResponse.headers.get('content-type') || 'application/octet-stream';
        res.setHeader('Content-Type', contentType);

        // 流式传输文件内容
        fileResponse.body.pipe(res);
      } catch (proxyError) {
        console.error('代理下载失败，降级为重定向:', proxyError);
        res.redirect(document.file_path);
      }
    } else {
      // 如果都没有，返回错误
      console.log('没有可用的下载地址');
      res.status(404).json({
        success: false,
        message: '文件下载地址不存在'
      });
    }
  } catch (error) {
    console.error('下载文件失败:', error);
    res.status(500).json({
      success: false,
      message: '下载文件失败'
    });
  }
});

export default router;
