import express from 'express';
import { query, paginate } from '../config/database.js';

const router = express.Router();

// 获取部门列表（支持分页和搜索）
router.get('/list', async (req, res) => {
  try {
    const { pageNum = 1, pageSize = 10, keyword = '', status = '' } = req.query;
    
    let sql = `
      SELECT
        d.id, d.name, d.code, d.description, d.manager_id, d.parent_id,
        d.sort_order, d.is_active as status, d.created_at, d.updated_at,
        m.username as manager_name, m.nickname as manager_nickname,
        p.name as parent_name,
        (SELECT COUNT(*) FROM system_users WHERE department_id = d.id) as user_count
      FROM system_depts d
      LEFT JOIN system_users m ON d.manager_id = m.id
      LEFT JOIN system_depts p ON d.parent_id = p.id
      WHERE 1=1
    `;
    const params = [];

    // 关键词搜索
    if (keyword) {
      sql += ` AND (d.name LIKE ? OR d.code LIKE ? OR d.description LIKE ?)`;
      const searchTerm = `%${keyword}%`;
      params.push(searchTerm, searchTerm, searchTerm);
    }

    // 状态筛选
    if (status) {
      sql += ` AND d.is_active = ?`;
      params.push(status);
    }

    sql += ` ORDER BY d.sort_order ASC, d.created_at DESC`;

    const result = await paginate(sql, params, pageNum, pageSize);
    
    res.success(result, '获取部门列表成功');
  } catch (error) {
    console.error('获取部门列表失败:', error);
    res.error('获取部门列表失败', 500);
  }
});

// 获取所有部门（不分页，用于下拉选择）
router.get('/all', async (req, res) => {
  try {
    const departments = await query(`
      SELECT id, name, code, parent_id, sort_order
      FROM system_depts
      WHERE is_active = 1
      ORDER BY sort_order ASC, name ASC
    `);
    
    res.success(departments, '获取部门列表成功');
  } catch (error) {
    console.error('获取部门列表失败:', error);
    res.error('获取部门列表失败', 500);
  }
});

// 获取部门详情
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    const departments = await query(`
      SELECT 
        d.id, d.name, d.code, d.description, d.manager_id, d.parent_id,
        d.sort_order, d.is_active as status, d.created_at, d.updated_at,
        m.username as manager_name, m.nickname as manager_nickname,
        p.name as parent_name
      FROM system_depts d
      LEFT JOIN system_users m ON d.manager_id = m.id
      LEFT JOIN system_depts p ON d.parent_id = p.id
      WHERE d.id = ?
    `, [id]);
    
    if (departments.length === 0) {
      return res.error('部门不存在', 404);
    }
    
    res.success(departments[0], '获取部门详情成功');
  } catch (error) {
    console.error('获取部门详情失败:', error);
    res.error('获取部门详情失败', 500);
  }
});

// 创建部门
router.post('/', async (req, res) => {
  try {
    const { name, code, description, manager_id, parent_id, sort_order = 0, status = 1 } = req.body;

    if (!name || !code) {
      return res.error('部门名称和代码不能为空', 400);
    }

    // 检查部门代码是否已存在
    const existingDepts = await query('SELECT id FROM system_depts WHERE code = ?', [code]);
    if (existingDepts.length > 0) {
      return res.error('部门代码已存在', 400);
    }

    const result = await query(
      `INSERT INTO system_depts (name, code, description, manager_id, parent_id, sort_order, is_active, created_at, updated_at)
       VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW())`,
      [name, code, description || null, manager_id || null, parent_id || null, sort_order, status]
    );

    res.success({ id: result.insertId }, '创建部门成功');
  } catch (error) {
    console.error('创建部门失败:', error);
    res.error('创建部门失败', 500);
  }
});

// 更新部门
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { name, code, description, manager_id, parent_id, sort_order, status } = req.body;

    if (!name || !code) {
      return res.error('部门名称和代码不能为空', 400);
    }

    // 检查部门是否存在
    const existingDepts = await query('SELECT id FROM system_depts WHERE id = ?', [id]);
    if (existingDepts.length === 0) {
      return res.error('部门不存在', 404);
    }

    // 检查部门代码是否被其他部门使用
    const duplicateDepts = await query(
      'SELECT id FROM system_depts WHERE code = ? AND id != ?',
      [code, id]
    );
    if (duplicateDepts.length > 0) {
      return res.error('部门代码已被其他部门使用', 400);
    }

    await query(
      `UPDATE system_depts SET
       name = ?, code = ?, description = ?, manager_id = ?, parent_id = ?, 
       sort_order = ?, is_active = ?, updated_at = NOW()
       WHERE id = ?`,
      [name, code, description || null, manager_id || null, parent_id || null, sort_order, status, id]
    );

    res.success(null, '更新部门成功');
  } catch (error) {
    console.error('更新部门失败:', error);
    res.error('更新部门失败', 500);
  }
});

// 删除部门
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // 检查部门是否存在
    const existingDepts = await query('SELECT id FROM system_depts WHERE id = ?', [id]);
    if (existingDepts.length === 0) {
      return res.error('部门不存在', 404);
    }

    // 检查是否有用户属于该部门
    const usersInDept = await query('SELECT COUNT(*) as count FROM system_users WHERE department_id = ?', [id]);
    if (usersInDept[0].count > 0) {
      return res.error('该部门下还有用户，无法删除', 400);
    }

    // 检查是否有子部门
    const childDepts = await query('SELECT COUNT(*) as count FROM system_depts WHERE parent_id = ?', [id]);
    if (childDepts[0].count > 0) {
      return res.error('该部门下还有子部门，无法删除', 400);
    }

    await query('DELETE FROM system_depts WHERE id = ?', [id]);

    res.success(null, '删除部门成功');
  } catch (error) {
    console.error('删除部门失败:', error);
    res.error('删除部门失败', 500);
  }
});

// 批量删除部门
router.post('/batch-delete', async (req, res) => {
  try {
    const { ids } = req.body;

    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return res.error('请选择要删除的部门', 400);
    }

    // 检查是否有用户属于这些部门
    const usersInDepts = await query(
      `SELECT COUNT(*) as count FROM system_users WHERE department_id IN (${ids.map(() => '?').join(',')})`,
      ids
    );
    if (usersInDepts[0].count > 0) {
      return res.error('选中的部门中有部门还有用户，无法删除', 400);
    }

    // 检查是否有子部门
    const childDepts = await query(
      `SELECT COUNT(*) as count FROM system_depts WHERE parent_id IN (${ids.map(() => '?').join(',')})`,
      ids
    );
    if (childDepts[0].count > 0) {
      return res.error('选中的部门中有部门还有子部门，无法删除', 400);
    }

    await query(
      `DELETE FROM system_depts WHERE id IN (${ids.map(() => '?').join(',')})`,
      ids
    );

    res.success(null, `成功删除 ${ids.length} 个部门`);
  } catch (error) {
    console.error('批量删除部门失败:', error);
    res.error('批量删除部门失败', 500);
  }
});

export default router;
