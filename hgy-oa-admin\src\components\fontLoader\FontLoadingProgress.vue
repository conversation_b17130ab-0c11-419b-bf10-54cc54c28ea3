<template>
  <div class="font-loading-progress">
    <div class="progress-header">
      <div class="title">
        <icon-font-colors class="icon" />
        <span>字体加载进度</span>
      </div>
      <div class="percentage">{{ percentage }}%</div>
    </div>
    
    <div class="progress-bar">
      <div 
        class="progress-fill" 
        :style="{ width: `${percentage}%` }"
      ></div>
    </div>
    
    <div class="progress-info">
      <div class="stats">
        <span class="current">{{ loaded }}</span>
        <span class="separator">/</span>
        <span class="total">{{ total }}</span>
        <span class="label">已完成</span>
      </div>
      
      <div v-if="currentFont" class="current-font">
        <icon-loading class="loading-icon" />
        <span>正在加载: {{ currentFont }}</span>
      </div>
      
      <div v-if="failed > 0" class="failed-info">
        <icon-exclamation-circle-fill class="error-icon" />
        <span>{{ failed }} 个字体加载失败</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { IconFontColors, IconLoading, IconExclamationCircleFill } from '@arco-design/web-vue/es/icon'

interface Props {
  loaded: number
  total: number
  failed?: number
  currentFont?: string
}

const props = withDefaults(defineProps<Props>(), {
  failed: 0,
  currentFont: ''
})

const percentage = computed(() => {
  if (props.total === 0) return 0
  return Math.round(((props.loaded + props.failed) / props.total) * 100)
})
</script>

<style scoped>
.font-loading-progress {
  padding: 16px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  min-width: 300px;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  color: #1d2129;
}

.icon {
  color: #1890ff;
  font-size: 16px;
}

.percentage {
  font-weight: 600;
  color: #1890ff;
  font-size: 16px;
}

.progress-bar {
  height: 8px;
  background: #f5f5f5;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 12px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #1890ff, #40a9ff);
  border-radius: 4px;
  transition: width 0.3s ease;
  position: relative;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.progress-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.stats {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  color: #4e5969;
}

.current {
  font-weight: 600;
  color: #1890ff;
}

.separator {
  color: #86909c;
}

.total {
  font-weight: 500;
}

.label {
  margin-left: 4px;
}

.current-font {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #1890ff;
  padding: 4px 8px;
  background: #e6f7ff;
  border-radius: 4px;
}

.loading-icon {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.failed-info {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #f53f3f;
  padding: 4px 8px;
  background: #ffece8;
  border-radius: 4px;
}

.error-icon {
  color: #f53f3f;
}
</style>
