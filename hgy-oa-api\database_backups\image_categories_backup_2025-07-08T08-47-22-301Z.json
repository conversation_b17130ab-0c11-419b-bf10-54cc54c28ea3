{"tableName": "image_categories", "createSQL": "CREATE TABLE `image_categories` (\n  `id` int NOT NULL AUTO_INCREMENT,\n  `name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,\n  `sort_order` int DEFAULT '0',\n  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,\n  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,\n  PRIMARY KEY (`id`),\n  KEY `idx_sort_order` (`sort_order`)\n) ENGINE=InnoDB AUTO_INCREMENT=19 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci", "data": [{"id": 16, "name": "背景", "sort_order": 1, "created_at": "2025-06-30T06:22:01.000Z", "updated_at": "2025-06-30T06:22:01.000Z"}, {"id": 17, "name": "素材", "sort_order": 2, "created_at": "2025-06-30T06:22:01.000Z", "updated_at": "2025-06-30T06:22:01.000Z"}, {"id": 18, "name": "节日", "sort_order": 1, "created_at": "2025-07-01T09:05:11.000Z", "updated_at": "2025-07-01T09:05:11.000Z"}], "backupTime": "2025-07-08T08:47:23.232Z", "recordCount": 3}