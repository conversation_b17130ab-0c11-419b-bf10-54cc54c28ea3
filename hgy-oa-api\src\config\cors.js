/**
 * CORS配置文件
 * 根据环境变量动态配置允许的源
 */

// 获取允许的源列表
const getAllowedOrigins = () => {
  const origins = [];
  
  // 本地开发环境
  origins.push(
    'http://localhost:5173',
    'http://localhost:3000',
    'http://127.0.0.1:5173',
    'http://127.0.0.1:3000'
  );
  
  // 从环境变量获取前端URL
  if (process.env.FRONTEND_URL) {
    origins.push(process.env.FRONTEND_URL);
  }
  
  // 测试环境
  if (process.env.NODE_ENV === 'test' || process.env.NODE_ENV === 'testing') {
    origins.push('https://image-admin.dlmu.cc');
  }
  
  // 生产环境
  if (process.env.NODE_ENV === 'production') {
    origins.push('https://admin.yourdomain.com');
  }
  
  // 从环境变量获取额外的允许源
  if (process.env.ALLOWED_ORIGINS) {
    const extraOrigins = process.env.ALLOWED_ORIGINS.split(',').map(origin => origin.trim());
    origins.push(...extraOrigins);
  }
  
  return [...new Set(origins)]; // 去重
};

// CORS配置选项
export const corsOptions = {
  origin: function (origin, callback) {
    const allowedOrigins = getAllowedOrigins();
    
    console.log(`CORS check - Origin: ${origin}, Allowed: ${allowedOrigins.join(', ')}`);
    
    // 允许没有origin的请求（如文件系统、Postman、移动应用等）
    if (!origin) {
      return callback(null, true);
    }
    
    // 开发环境更宽松的策略
    if (process.env.NODE_ENV === 'development') {
      return callback(null, true);
    }
    
    // 检查origin是否在允许列表中
    if (allowedOrigins.indexOf(origin) !== -1) {
      callback(null, true);
    } else {
      console.warn(`CORS blocked origin: ${origin}`);
      console.warn(`Allowed origins: ${allowedOrigins.join(', ')}`);
      callback(new Error('Not allowed by CORS'));
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
  allowedHeaders: [
    'Content-Type',
    'Authorization',
    'x-username',
    'x-requested-with',
    'Accept',
    'Origin',
    'X-Requested-With',
    'Cache-Control',
    'Pragma'
  ],
  exposedHeaders: [
    'Content-Length',
    'Content-Type',
    'X-Total-Count'
  ],
  maxAge: 86400, // 24小时
  optionsSuccessStatus: 200 // 兼容旧版浏览器
};

export default corsOptions;
