<template>
    <div class="wrap">
        <search-header :cateList="cateList" v-model="keyword" @changeCate="changeCate" @search="onSearch"/>
        <div class="other-text-wrap">
            <comp-list-wrap @fetchData="fetchData" :data="page.dataList" :noMore="page.noMore" max-height="calc(100vh - 115px)">
                <template #item="{ item, url }">
                    <a-card hoverable @click="handleClick(item)" class="cursor-pointer drop-shadow" :body-style="{ padding: '0px' }">
                        <div class="">
                            <LazyImg :url="url" class="img" />
                        </div>
                        <!--                      <div class="p5px">-->
                        <!--                          <span class="name truncated">{{ item.name }}</span>-->
                        <!--                      </div>-->
                    </a-card>
                </template>
            </comp-list-wrap>
        </div>
    </div>
</template>

<script lang="ts" setup>
import {LazyImg} from '@/components/vue-waterfall-plugin-next'

import {useEditor} from "@/views/Editor/app";
import {Image} from "leafer-ui";
import {getDefaultName} from "@/views/Editor/utils/utils";
import CompListWrap from "@/views/Editor/layouts/panel/leftPanel/wrap/CompListWrap.vue";
import usePageMixin from "@/views/Editor/layouts/panel/leftPanel/wrap/mixins/pageMixin";
import {queryImageMaterialList, getMaterialCategories} from "@/api/editor/materials";
import SearchHeader from "@/components/editorModules/searchHeader.vue";
const {editor} = useEditor()
const keyword = ref();
const cateList = reactive([
    {label:'全部',value:'-1'},
]);

// 初始化分类列表
const initCategories = async () => {
    try {
        const response = await getMaterialCategories();
        if (response.success) {
            // 找到图片分类及其子分类
            const imageCategory = response.data.find(cat => cat.key === '9');
            if (imageCategory && imageCategory.children) {
                const subcategories = imageCategory.children.map(child => ({
                    label: child.title,
                    value: child.key
                }));
                cateList.splice(1, 0, ...subcategories);
            }
        }
    } catch (error) {
        console.error('获取分类失败:', error);
    }
};
const changeCate = (e) => {
    console.log('e=',e)
}
const onSearch = (value,ev) => {
    console.log('value=',value)
    console.log('keyword=',keyword.value)
    console.log('ev=',ev)
}
const { page } = usePageMixin()
page.pageSize = 20
const fetchData = () => {
    queryImageMaterialList(page).then(res =>{
        if (res.success) {
            const newDataList = res.data.records.map((item: any) => ({
                ...item,
                url: item.preview_url || item.content // 映射字段，图片素材的URL在content字段
            }))
            if (newDataList.length > 0) {
                page.dataList.push(...newDataList)
                page.pageNum += 1
            }
            if (page.dataList.length >= res.data.total) {
                page.noMore = true
            } else {
                page.noMore = false
            }
        }
    })
}
const handleClick = (item) => {
    const image = new Image({
        name:getDefaultName(editor.contentFrame),
        // draggable: true,
        editable: true,
        x:0,
        y:0,
        ...item,
    })
    editor.add(image)
}

// 组件挂载时初始化数据
import { onMounted } from 'vue'

// 接收active属性
const props = defineProps<{
  active: boolean
}>()

onMounted(() => {
  console.log('ImageListWrap 组件挂载，开始初始化...');
  initCategories()
  fetchData()
})
</script>

<style lang="less" scoped>
.search__wrap {
    padding: 1.4rem 1rem 0.8rem 0rem;
}
</style>
