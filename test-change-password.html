<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试修改密码功能</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <h1>测试修改密码功能</h1>
    
    <form id="changePasswordForm">
        <div class="form-group">
            <label for="oldPassword">当前密码:</label>
            <input type="password" id="oldPassword" name="oldPassword" required>
        </div>
        
        <div class="form-group">
            <label for="newPassword">新密码:</label>
            <input type="password" id="newPassword" name="newPassword" required>
        </div>
        
        <div class="form-group">
            <label for="confirmPassword">确认新密码:</label>
            <input type="password" id="confirmPassword" name="confirmPassword" required>
        </div>
        
        <button type="submit">修改密码</button>
    </form>
    
    <div id="result"></div>

    <script>
        document.getElementById('changePasswordForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const oldPassword = document.getElementById('oldPassword').value;
            const newPassword = document.getElementById('newPassword').value;
            const confirmPassword = document.getElementById('confirmPassword').value;
            const resultDiv = document.getElementById('result');
            
            // 验证新密码和确认密码是否一致
            if (newPassword !== confirmPassword) {
                resultDiv.innerHTML = '<div class="result error">新密码和确认密码不一致</div>';
                return;
            }
            
            // 验证密码长度
            if (newPassword.length < 6) {
                resultDiv.innerHTML = '<div class="result error">新密码长度不能少于6位</div>';
                return;
            }
            
            try {
                const response = await fetch('http://localhost:3001/api/auth/change-password', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'x-username': 'admin'  // 模拟已登录的admin用户
                    },
                    body: JSON.stringify({
                        oldPassword: oldPassword,
                        newPassword: newPassword
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    resultDiv.innerHTML = '<div class="result success">' + result.msg + '</div>';
                    // 清空表单
                    document.getElementById('changePasswordForm').reset();
                } else {
                    resultDiv.innerHTML = '<div class="result error">' + result.msg + '</div>';
                }
            } catch (error) {
                console.error('请求失败:', error);
                resultDiv.innerHTML = '<div class="result error">网络请求失败，请检查服务器连接</div>';
            }
        });
    </script>
</body>
</html>
