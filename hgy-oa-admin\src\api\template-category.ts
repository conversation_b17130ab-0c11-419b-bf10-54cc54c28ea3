import { request } from '@/utils/request'

// 获取模板分类树形结构
export const getTemplateCategoryTree = () => {
  return request.get('/api/template-category/tree')
}

// 获取模板分类列表
export const getTemplateCategoryList = (params: any) => {
  return request.get('/api/template-category/list', { params })
}

// 创建模板分类
export const createTemplateCategory = (data: any) => {
  return request.post('/api/template-category', data)
}

// 更新模板分类
export const updateTemplateCategory = (id: number, data: any) => {
  return request.put(`/api/template-category/${id}`, data)
}

// 删除模板分类
export const deleteTemplateCategory = (id: number) => {
  return request.delete(`/api/template-category/${id}`)
}

// 模板分类相关类型定义（一级分类）
export interface TemplateCategory {
  id: number
  name: string
  sort_order: number
  status: number
  created_at: string
  updated_at: string
}

export interface CreateTemplateCategoryData {
  name: string
  sort_order?: number
}

export interface UpdateTemplateCategoryData {
  name?: string
  sort_order?: number
}
