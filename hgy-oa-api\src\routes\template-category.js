import express from 'express';
import { query } from '../config/database.js';

const router = express.Router();

// 获取模板分类列表（一级分类）
router.get('/tree', async (req, res) => {
  try {
    // 获取所有一级分类
    const categories = await query(`
      SELECT id, name, sort_order, status, created_at, updated_at
      FROM editor_template_categories
      WHERE status = 1 AND parent_id IS NULL
      ORDER BY sort_order ASC
    `);

    res.success(categories, '获取模板分类列表成功');
  } catch (error) {
    console.error('获取模板分类列表失败:', error);
    res.error('获取模板分类列表失败', 500);
  }
});

// 获取模板分类列表（一级分类）
router.get('/list', async (req, res) => {
  try {
    const { page = 1, pageSize = 20, keyword } = req.query;
    const offset = (parseInt(page) - 1) * parseInt(pageSize);

    let whereClause = 'WHERE status = 1 AND parent_id IS NULL';
    let params = [];

    if (keyword) {
      whereClause += ' AND name LIKE ?';
      params.push(`%${keyword}%`);
    }

    // 获取分类列表
    const listSql = `
      SELECT id, name, sort_order, status, created_at, updated_at
      FROM editor_template_categories
      ${whereClause}
      ORDER BY sort_order ASC
      LIMIT ? OFFSET ?
    `;

    const categories = await query(listSql, [...params, parseInt(pageSize), offset]);

    // 获取总数
    const countSql = `SELECT COUNT(*) as total FROM editor_template_categories ${whereClause}`;
    const [countResult] = await query(countSql, params);

    res.success({
      list: categories,
      total: countResult.total,
      page: parseInt(page),
      pageSize: parseInt(pageSize)
    }, '获取模板分类列表成功');

  } catch (error) {
    console.error('获取模板分类列表失败:', error);
    res.error('获取模板分类列表失败', 500);
  }
});

// 创建模板分类（一级分类）
router.post('/', async (req, res) => {
  try {
    const { name, sort_order = 0 } = req.body;

    if (!name) {
      return res.error('分类名称不能为空', 400);
    }

    // 检查分类名称是否重复
    const existingCategory = await query(
      'SELECT id FROM editor_template_categories WHERE name = ? AND parent_id IS NULL AND status = 1',
      [name]
    );

    if (existingCategory.length > 0) {
      return res.error('分类名称已存在', 400);
    }

    const result = await query(
      'INSERT INTO editor_template_categories (name, parent_id, sort_order, status, created_at, updated_at) VALUES (?, NULL, ?, 1, NOW(), NOW())',
      [name, parseInt(sort_order)]
    );

    res.success({ id: result.insertId }, '创建模板分类成功');
  } catch (error) {
    console.error('创建模板分类失败:', error);
    res.error('创建模板分类失败', 500);
  }
});

// 更新模板分类（一级分类）
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { name, sort_order } = req.body;

    if (!name) {
      return res.error('分类名称不能为空', 400);
    }

    // 检查分类是否存在
    const category = await query(
      'SELECT id FROM editor_template_categories WHERE id = ? AND status = 1',
      [id]
    );
    if (category.length === 0) {
      return res.error('分类不存在', 404);
    }

    // 检查分类名称是否重复（排除自己）
    const existingCategory = await query(
      'SELECT id FROM editor_template_categories WHERE name = ? AND parent_id IS NULL AND id != ? AND status = 1',
      [name, id]
    );

    if (existingCategory.length > 0) {
      return res.error('分类名称已存在', 400);
    }

    await query(
      'UPDATE editor_template_categories SET name = ?, sort_order = ?, updated_at = NOW() WHERE id = ?',
      [name, parseInt(sort_order) || 0, id]
    );

    res.success(null, '更新模板分类成功');
  } catch (error) {
    console.error('更新模板分类失败:', error);
    res.error('更新模板分类失败', 500);
  }
});

// 删除模板分类（一级分类）
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // 检查分类是否存在
    const category = await query(
      'SELECT id FROM editor_template_categories WHERE id = ? AND status = 1',
      [id]
    );
    if (category.length === 0) {
      return res.error('分类不存在', 404);
    }

    // 检查是否有模板使用该分类
    const templates = await query(
      'SELECT id FROM editor_templates WHERE category_id = ? AND state = 1',
      [id]
    );
    if (templates.length > 0) {
      return res.error('该分类下还有模板，无法删除', 400);
    }

    // 软删除
    await query(
      'UPDATE editor_template_categories SET status = 0, updated_at = NOW() WHERE id = ?',
      [id]
    );

    res.success(null, '删除模板分类成功');
  } catch (error) {
    console.error('删除模板分类失败:', error);
    res.error('删除模板分类失败', 500);
  }
});

export default router;
