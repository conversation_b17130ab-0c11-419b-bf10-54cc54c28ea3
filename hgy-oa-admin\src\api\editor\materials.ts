import { request } from '@/utils/request';
import type { DescData } from '@arco-design/web-vue/es/descriptions/interface';
import {PageParams} from "@/types/page";

/**
 * 模板数据
 * @param params
 */
export function queryTemplateList(params:PageParams) {
  return request.get('/api/template/templateList',{params});
}

/**
 * 统一素材接口 - 通过分类ID获取不同类型素材
 * @param params 查询参数
 * @param categoryId 分类ID
 */
function getMaterialsByCategory(params: PageParams, categoryId: number) {
  return request.get('/api/material/list', {
    params: {
      ...params,
      categoryId
    }
  });
}

/**
 * 文字素材 (categoryId = 5)
 * @param params
 */
export function queryTextMaterialList(params: PageParams) {
  return getMaterialsByCategory(params, 5).then(response => {
    if (response.success) {
      // 转换数据格式以兼容现有组件
      return {
        ...response,
        data: {
          records: response.data.list.map((item: any) => ({
            ...item,
            preview_url: item.thumbnail_path || item.file_path,
            content: item.file_path,
            url: item.file_path
          })),
          total: response.data.total
        }
      };
    }
    return response;
  });
}

/**
 * 图片素材 (categoryId = 9 及其子分类)
 * @param params
 */
export function queryImageMaterialList(params: PageParams) {
  // 使用原有的API，但传递primaryCategory参数来获取主分类及其子分类的所有素材
  return request.get('/api/material/list', {
    params: {
      ...params,
      primaryCategory: 9 // 使用primaryCategory来获取主分类及其子分类
    }
  }).then(response => {
    if (response.success) {
      return {
        ...response,
        data: {
          records: response.data.list.map((item: any) => ({
            ...item,
            preview_url: item.thumbnail_path || item.file_path,
            content: item.file_path,
            url: item.file_path
          })),
          total: response.data.total
        }
      };
    }
    return response;
  });
}

/**
 * 获取素材分类列表 - 带缓存机制
 */
let categoriesCache: any = null;
let categoriesPromise: Promise<any> | null = null;

export function getMaterialCategories() {
  // 如果有缓存，直接返回
  if (categoriesCache) {
    console.log('使用分类缓存数据');
    return Promise.resolve(categoriesCache);
  }

  // 如果正在请求中，返回同一个Promise避免重复请求
  if (categoriesPromise) {
    console.log('分类请求进行中，等待结果...');
    return categoriesPromise;
  }

  console.log('发起新的分类请求');
  categoriesPromise = request.get('/api/material/categories').then(response => {
    if (response.success) {
      categoriesCache = response;
      console.log('分类数据已缓存');
    }
    categoriesPromise = null; // 请求完成，清除Promise引用
    return response;
  }).catch(error => {
    categoriesPromise = null; // 请求失败，清除Promise引用
    throw error;
  });

  return categoriesPromise;
}

/**
 * 清除分类缓存（在分类数据更新时调用）
 */
export function clearCategoriesCache() {
  categoriesCache = null;
  categoriesPromise = null;
  console.log('分类缓存已清除');
}

// 立即清除缓存以使用新的分类数据
clearCategoriesCache();

/**
 * 插画分类 (categoryId = 13)
 * @param params
 */
export function queryGraphCategory(params?: PageParams) {
  return getMaterialCategories().then(response => {
    if (response.success) {
      // 找到插画分类及其子分类
      const illustrationCategory = response.data.find((cat: any) => cat.key === '13');
      const categories = illustrationCategory?.children || [];

      return {
        ...response,
        data: {
          records: categories.map((cat: any) => ({
            id: cat.key,
            name: cat.title,
            description: cat.description,
            sort_order: cat.sort_order
          }))
        }
      };
    }
    return response;
  });
}

/**
 * 插画素材列表 (categoryId = 13 或其子分类)
 * @param params
 */
export function queryGraphList(params: PageParams) {
  const categoryId = (params as any).query?.categoryId || 13;
  return getMaterialsByCategory(params, categoryId).then(response => {
    if (response.success) {
      return {
        ...response,
        data: {
          records: response.data.list.map((item: any) => ({
            ...item,
            preview_url: item.thumbnail_path || item.file_path,
            content: item.file_path,
            url: item.file_path
          })),
          total: response.data.total
        }
      };
    }
    return response;
  });
}

/**
 * 背景素材 (categoryId = 1)
 * @param params
 */
export function queryBgImgMaterialList(params: PageParams) {
  return getMaterialsByCategory(params, 1).then(response => {
    if (response.success) {
      return {
        ...response,
        data: {
          records: response.data.list.map((item: any) => ({
            ...item,
            preview_url: item.thumbnail_path || item.file_path,
            original_url: item.file_path,
            url: item.file_path,
            thumb: item.thumbnail_path || item.file_path,
            name: item.title
          })),
          total: response.data.total
        }
      };
    }
    return response;
  });
}


/**
 * 元素分类 (categoryId = 21)
 * @param params
 */
export function queryElementCategory(params?: PageParams) {
  return getMaterialCategories().then(response => {
    if (response.success) {
      // 找到元素分类及其子分类
      const elementCategory = response.data.find((cat: any) => cat.key === '21');
      const categories = elementCategory?.children || [];

      return {
        ...response,
        data: {
          records: categories.map((cat: any) => ({
            id: cat.key,
            name: cat.title,
            description: cat.description,
            sort_order: cat.sort_order
          }))
        }
      };
    }
    return response;
  });
}

/**
 * 元素素材列表 (categoryId = 21 或其子分类)
 * @param params
 */
export function queryElementList(params: PageParams) {
  const categoryId = (params as any).query?.categoryId || 21;
  return getMaterialsByCategory(params, categoryId).then(response => {
    if (response.success) {
      return {
        ...response,
        data: {
          records: response.data.list.map((item: any) => ({
            ...item,
            preview_url: item.thumbnail_path || item.file_path,
            content: item.file_path,
            url: item.file_path
          })),
          total: response.data.total
        }
      };
    }
    return response;
  });
}
