<template>
  <div class="material-category">
    <div class="page-header">
      <h1>素材分类管理</h1>
      <a-button type="primary" @click="showAddModal">
        新增分类
      </a-button>
    </div>

    <!-- 分类树形结构 -->
    <a-card :loading="loading">
      <div v-if="categoryTree.length === 0">
        <a-empty description="暂无分类数据" />
      </div>
      <div v-else>
        <div v-for="category in categoryTree" :key="category.key" class="category-item">
          <div class="category-header">
            <span class="category-name">{{ category.title }}</span>
            <span class="category-count">{{ category.count }}</span>
            <div class="category-actions">
              <a-button size="mini" @click="editCategory(category)">编辑</a-button>
              <a-button size="mini" @click="addSubCategory(category)">添加子分类</a-button>
              <a-button size="mini" status="danger" @click="deleteCategory(category)">删除</a-button>
            </div>
          </div>
          <div v-if="category.children && category.children.length > 0" class="sub-categories">
            <div v-for="subCategory in category.children" :key="subCategory.key" class="sub-category-item">
              <span class="sub-category-name">{{ subCategory.title }}</span>
              <span class="sub-category-count">{{ subCategory.count }}</span>
              <div class="sub-category-actions">
                <a-button size="mini" @click="editCategory(subCategory)">编辑</a-button>
                <a-button size="mini" status="danger" @click="deleteCategory(subCategory)">删除</a-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </a-card>

    <!-- 新增/编辑分类弹窗 -->
    <a-modal
      v-model:visible="modalVisible"
      :title="modalTitle"
      :confirm-loading="loading"
      @ok="handleSubmit"
      @cancel="handleCancel"
    >
      <a-form :model="formData" layout="vertical">
        <a-form-item label="分类名称" required>
          <a-input v-model="formData.name" placeholder="请输入分类名称" />
        </a-form-item>
        
        <a-form-item label="父级分类">
          <a-select
            v-model="formData.parentId"
            placeholder="请选择父级分类（不选则为一级分类）"
            allow-clear
          >
            <a-option v-for="option in parentOptions" :key="option.key" :value="option.value">
              {{ option.title }}
            </a-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="排序">
          <a-input-number v-model="formData.sort" :min="0" placeholder="数字越小越靠前" />
        </a-form-item>
        
        <a-form-item label="描述">
          <a-textarea v-model="formData.description" placeholder="请输入分类描述" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { Message, Modal } from '@arco-design/web-vue'
import { 
  getMaterialCategoryTree, 
  createMaterialCategory, 
  updateMaterialCategory, 
  deleteMaterialCategory,
  type MaterialCategoryTreeNode,
  type CreateMaterialCategoryParams,
  type UpdateMaterialCategoryParams
} from '@/api/material-category'

// 响应式数据
const modalVisible = ref(false)
const isEdit = ref(false)
const currentEditId = ref<number | null>(null)
const loading = ref(false)

// 表单数据
const formData = ref({
  name: '',
  parentId: null as number | null,
  sort: 0,
  description: ''
})

// 分类树数据
const categoryTree = ref<MaterialCategoryTreeNode[]>([])

// 计算属性
const modalTitle = computed(() => isEdit.value ? '编辑分类' : '新增分类')

const parentOptions = computed(() => {
  const options: Array<{key: string, title: string, value: string}> = []
  
  categoryTree.value.forEach(category => {
    if (category.level < 2) {
      options.push({
        key: category.key,
        title: category.title,
        value: category.key
      })
    }
  })
  
  return options
})

// 方法
const showAddModal = () => {
  isEdit.value = false
  currentEditId.value = null
  formData.value = {
    name: '',
    parentId: null,
    sort: 0,
    description: ''
  }
  modalVisible.value = true
}

const editCategory = (nodeData: MaterialCategoryTreeNode) => {
  isEdit.value = true
  currentEditId.value = parseInt(nodeData.key)
  formData.value = {
    name: nodeData.title,
    parentId: nodeData.key.includes('-') ? parseInt(nodeData.key.split('-')[0]) : null,
    sort: nodeData.sort_order || 0,
    description: nodeData.description || ''
  }
  modalVisible.value = true
}

const addSubCategory = (nodeData: MaterialCategoryTreeNode) => {
  isEdit.value = false
  currentEditId.value = null
  formData.value = {
    name: '',
    parentId: parseInt(nodeData.key),
    sort: 0,
    description: ''
  }
  modalVisible.value = true
}

const deleteCategory = async (nodeData: MaterialCategoryTreeNode) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除分类"${nodeData.title}"吗？`,
    onOk: async () => {
      try {
        loading.value = true
        await deleteMaterialCategory(parseInt(nodeData.key))
        Message.success('删除成功')
        await fetchCategoryTree()
      } catch (error) {
        console.error('删除分类失败:', error)
        Message.error('删除失败')
      } finally {
        loading.value = false
      }
    }
  })
}

const handleSubmit = async () => {
  if (!formData.value.name.trim()) {
    Message.error('请输入分类名称')
    return
  }
  
  try {
    loading.value = true
    
    if (isEdit.value && currentEditId.value) {
      const updateData: UpdateMaterialCategoryParams = {
        name: formData.value.name,
        parent_id: formData.value.parentId,
        sort_order: formData.value.sort,
        description: formData.value.description
      }
      await updateMaterialCategory(currentEditId.value, updateData)
      Message.success('编辑成功')
    } else {
      const createData: CreateMaterialCategoryParams = {
        name: formData.value.name,
        parent_id: formData.value.parentId,
        sort_order: formData.value.sort,
        description: formData.value.description
      }
      await createMaterialCategory(createData)
      Message.success('新增成功')
    }
    
    modalVisible.value = false
    await fetchCategoryTree()
  } catch (error) {
    console.error('提交失败:', error)
    Message.error(isEdit.value ? '编辑失败' : '新增失败')
  } finally {
    loading.value = false
  }
}

const handleCancel = () => {
  modalVisible.value = false
}

// 获取分类树数据
const fetchCategoryTree = async () => {
  try {
    loading.value = true
    const response = await getMaterialCategoryTree()
    categoryTree.value = response.data || []
  } catch (error) {
    console.error('获取分类数据失败:', error)
    Message.error('获取分类数据失败')
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  fetchCategoryTree()
})
</script>

<style scoped>
.material-category {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0;
  color: #262626;
}

.category-item {
  margin-bottom: 16px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  padding: 12px;
}

.category-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.category-name {
  font-weight: 500;
  font-size: 16px;
}

.category-count {
  color: #666;
  font-size: 14px;
}

.category-actions {
  display: flex;
  gap: 8px;
}

.sub-categories {
  margin-left: 20px;
}

.sub-category-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.sub-category-item:last-child {
  border-bottom: none;
}

.sub-category-name {
  font-size: 14px;
}

.sub-category-count {
  color: #666;
  font-size: 12px;
}

.sub-category-actions {
  display: flex;
  gap: 8px;
}
</style>
