# CORS故障排除指南

## 概述

CORS (Cross-Origin Resource Sharing) 是浏览器的安全机制，用于控制跨域请求。本文档帮助解决CORS相关问题。

## 常见CORS错误

### 1. Access-Control-Allow-Origin错误

```
Access to fetch at 'https://image-api.dlmu.cc/api/auth/login' from origin 'https://image-admin.dlmu.cc' 
has been blocked by CORS policy: No 'Access-Control-Allow-Origin' header is present on the requested resource.
```

**原因**: 后端没有配置允许前端域名的CORS策略

**解决方案**: 
1. 检查后端CORS配置
2. 确认前端域名在允许列表中
3. 重启后端服务

### 2. Preflight请求失败

```
Response to preflight request doesn't pass access control check: No 'Access-Control-Allow-Origin' header is present
```

**原因**: OPTIONS预检请求被阻止

**解决方案**: 
1. 确保CORS配置包含OPTIONS方法
2. 检查allowedHeaders配置
3. 验证maxAge设置

## 当前CORS配置

### 允许的源 (Origins)

#### 本地开发环境
- `http://localhost:5173`
- `http://localhost:3000`
- `http://127.0.0.1:5173`
- `http://127.0.0.1:3000`

#### 测试环境
- `https://image-admin.dlmu.cc`

#### 生产环境
- `https://admin.yourdomain.com`

### 允许的方法 (Methods)
- GET, POST, PUT, DELETE, OPTIONS, PATCH

### 允许的头部 (Headers)
- Content-Type
- Authorization
- x-username
- x-requested-with
- Accept
- Origin
- X-Requested-With
- Cache-Control
- Pragma

## 环境变量配置

### 本地开发 (.env.local)
```bash
NODE_ENV=development
FRONTEND_URL=http://localhost:5173
ALLOWED_ORIGINS=http://localhost:5173,http://localhost:3000
```

### 测试环境 (.env.test)
```bash
NODE_ENV=test
FRONTEND_URL=https://image-admin.dlmu.cc
ALLOWED_ORIGINS=https://image-admin.dlmu.cc
```

## 调试步骤

### 1. 检查后端日志
```bash
# 查看CORS日志
pnpm start:test 2>&1 | grep -i cors

# 查看origin检查日志
pnpm start:test 2>&1 | grep "CORS check"
```

### 2. 测试健康检查端点
```bash
# 本地环境
curl -H "Origin: http://localhost:5173" http://localhost:3001/health

# 测试环境
curl -H "Origin: https://image-admin.dlmu.cc" https://image-api.dlmu.cc/health
```

### 3. 测试预检请求
```bash
curl -X OPTIONS \
  -H "Origin: https://image-admin.dlmu.cc" \
  -H "Access-Control-Request-Method: POST" \
  -H "Access-Control-Request-Headers: Content-Type,x-username" \
  https://image-api.dlmu.cc/api/auth/login
```

### 4. 检查响应头
```bash
curl -I -H "Origin: https://image-admin.dlmu.cc" https://image-api.dlmu.cc/api/auth/login
```

应该包含以下响应头：
```
Access-Control-Allow-Origin: https://image-admin.dlmu.cc
Access-Control-Allow-Credentials: true
Access-Control-Allow-Methods: GET,POST,PUT,DELETE,OPTIONS,PATCH
Access-Control-Allow-Headers: Content-Type,Authorization,x-username,...
```

## 常见解决方案

### 1. 添加新的允许源
在环境变量中添加：
```bash
ALLOWED_ORIGINS=https://image-admin.dlmu.cc,https://new-domain.com
```

### 2. 临时禁用CORS (仅开发环境)
```javascript
// 在cors.js中添加
if (process.env.NODE_ENV === 'development') {
  return callback(null, true);
}
```

### 3. 检查Nginx配置 (如果使用)
```nginx
# 确保Nginx没有添加额外的CORS头
location /api/ {
    proxy_pass http://localhost:3001;
    # 不要添加CORS头，让后端处理
}
```

## 验证清单

- [ ] 后端服务正常运行
- [ ] 前端域名在ALLOWED_ORIGINS中
- [ ] 环境变量正确加载
- [ ] CORS配置包含所需的方法和头部
- [ ] 没有Nginx等代理服务器干扰
- [ ] 浏览器开发者工具显示正确的CORS头部

## 联系支持

如果问题仍然存在，请提供：
1. 完整的错误信息
2. 浏览器开发者工具的Network标签截图
3. 后端日志输出
4. 当前的环境配置
