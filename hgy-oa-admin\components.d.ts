/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    AAlert: typeof import('@arco-design/web-vue')['Alert']
    AAvatar: typeof import('@arco-design/web-vue')['Avatar']
    ABreadcrumb: typeof import('@arco-design/web-vue')['Breadcrumb']
    ABreadcrumbItem: typeof import('@arco-design/web-vue')['BreadcrumbItem']
    AButton: typeof import('@arco-design/web-vue')['Button']
    ACard: typeof import('@arco-design/web-vue')['Card']
    ACheckbox: typeof import('@arco-design/web-vue')['Checkbox']
    ACheckboxGroup: typeof import('@arco-design/web-vue')['CheckboxGroup']
    ACol: typeof import('@arco-design/web-vue')['Col']
    AColorPicker: typeof import('@arco-design/web-vue')['ColorPicker']
    AConfigProvider: typeof import('@arco-design/web-vue')['ConfigProvider']
    ADatePicker: typeof import('@arco-design/web-vue')['DatePicker']
    ADescriptions: typeof import('@arco-design/web-vue')['Descriptions']
    ADescriptionsItem: typeof import('@arco-design/web-vue')['DescriptionsItem']
    ADivider: typeof import('@arco-design/web-vue')['Divider']
    ADoption: typeof import('@arco-design/web-vue')['Doption']
    ADropdown: typeof import('@arco-design/web-vue')['Dropdown']
    ADropdownButton: typeof import('@arco-design/web-vue')['DropdownButton']
    AEmpty: typeof import('@arco-design/web-vue')['Empty']
    AForm: typeof import('@arco-design/web-vue')['Form']
    AFormItem: typeof import('@arco-design/web-vue')['FormItem']
    AImagePreview: typeof import('@arco-design/web-vue')['ImagePreview']
    AInput: typeof import('@arco-design/web-vue')['Input']
    AInputGroup: typeof import('@arco-design/web-vue')['InputGroup']
    AInputNumber: typeof import('@arco-design/web-vue')['InputNumber']
    AInputPassword: typeof import('@arco-design/web-vue')['InputPassword']
    AInputSearch: typeof import('@arco-design/web-vue')['InputSearch']
    ALayout: typeof import('@arco-design/web-vue')['Layout']
    ALayoutContent: typeof import('@arco-design/web-vue')['LayoutContent']
    ALayoutFooter: typeof import('@arco-design/web-vue')['LayoutFooter']
    ALayoutHeader: typeof import('@arco-design/web-vue')['LayoutHeader']
    ALayoutSider: typeof import('@arco-design/web-vue')['LayoutSider']
    ALink: typeof import('@arco-design/web-vue')['Link']
    AList: typeof import('@arco-design/web-vue')['List']
    AListItem: typeof import('@arco-design/web-vue')['ListItem']
    AListItemMeta: typeof import('@arco-design/web-vue')['ListItemMeta']
    AMenu: typeof import('@arco-design/web-vue')['Menu']
    AMenuItem: typeof import('@arco-design/web-vue')['MenuItem']
    AModal: typeof import('@arco-design/web-vue')['Modal']
    AOption: typeof import('@arco-design/web-vue')['Option']
    APagination: typeof import('@arco-design/web-vue')['Pagination']
    APopconfirm: typeof import('@arco-design/web-vue')['Popconfirm']
    APopover: typeof import('@arco-design/web-vue')['Popover']
    AProgress: typeof import('@arco-design/web-vue')['Progress']
    ARadio: typeof import('@arco-design/web-vue')['Radio']
    ARadioGroup: typeof import('@arco-design/web-vue')['RadioGroup']
    ARangePicker: typeof import('@arco-design/web-vue')['RangePicker']
    AResult: typeof import('@arco-design/web-vue')['Result']
    ARow: typeof import('@arco-design/web-vue')['Row']
    ASelect: typeof import('@arco-design/web-vue')['Select']
    ASlider: typeof import('@arco-design/web-vue')['Slider']
    ASpace: typeof import('@arco-design/web-vue')['Space']
    ASpin: typeof import('@arco-design/web-vue')['Spin']
    AStatistic: typeof import('@arco-design/web-vue')['Statistic']
    ASubMenu: typeof import('@arco-design/web-vue')['SubMenu']
    ASwitch: typeof import('@arco-design/web-vue')['Switch']
    ATable: typeof import('@arco-design/web-vue')['Table']
    ATableColumn: typeof import('@arco-design/web-vue')['TableColumn']
    ATabPane: typeof import('@arco-design/web-vue')['TabPane']
    ATabs: typeof import('@arco-design/web-vue')['Tabs']
    ATag: typeof import('@arco-design/web-vue')['Tag']
    ATextarea: typeof import('@arco-design/web-vue')['Textarea']
    ATooltip: typeof import('@arco-design/web-vue')['Tooltip']
    ATree: typeof import('@arco-design/web-vue')['Tree']
    ATreeSelect: typeof import('@arco-design/web-vue')['TreeSelect']
    ATrigger: typeof import('@arco-design/web-vue')['Trigger']
    AUpload: typeof import('@arco-design/web-vue')['Upload']
    ColorPicker2ColorPicker: typeof import('./src/components/colorPicker2/color-picker.vue')['default']
    ColorPicker2ControlBar: typeof import('./src/components/colorPicker2/control-bar.vue')['default']
    ColorPicker2InputAlpha: typeof import('./src/components/colorPicker2/input-alpha.vue')['default']
    ColorPicker2InputHex: typeof import('./src/components/colorPicker2/input-hex.vue')['default']
    ColorPicker2InputRgb: typeof import('./src/components/colorPicker2/input-rgb.vue')['default']
    ColorPicker2Palette: typeof import('./src/components/colorPicker2/palette.vue')['default']
    ColorPicker2Panel: typeof import('./src/components/colorPicker2/panel.vue')['default']
    ColorPickerArea: typeof import('./src/components/colorPicker/Area/index.vue')['default']
    ColorPickerAreaAlpha: typeof import('./src/components/colorPicker/Area/Alpha/index.vue')['default']
    ColorPickerAreaDegree: typeof import('./src/components/colorPicker/Area/Degree/index.vue')['default']
    ColorPickerAreaGradientPoints: typeof import('./src/components/colorPicker/Area/GradientPoints/index.vue')['default']
    ColorPickerAreaGradientPointsGradientPoint: typeof import('./src/components/colorPicker/Area/GradientPoints/GradientPoint/index.vue')['default']
    ColorPickerAreaHue: typeof import('./src/components/colorPicker/Area/Hue/index.vue')['default']
    ColorPickerAreaPicker: typeof import('./src/components/colorPicker/Area/Picker/index.vue')['default']
    ColorPickerAreaPreview: typeof import('./src/components/colorPicker/Area/Preview/index.vue')['default']
    ColorPickerColorPicker: typeof import('./src/components/colorPicker/colorPicker.vue')['default']
    ColorPickerGradient: typeof import('./src/components/colorPicker/Gradient/index.vue')['default']
    ColorPickerGradientGradientControls: typeof import('./src/components/colorPicker/Gradient/GradientControls/index.vue')['default']
    ColorPickerPreview: typeof import('./src/components/colorPicker/Preview/index.vue')['default']
    ColorPickerPreviewHex: typeof import('./src/components/colorPicker/Preview/Hex/index.vue')['default']
    ColorPickerPreviewRGB: typeof import('./src/components/colorPicker/Preview/RGB/index.vue')['default']
    ColorPickerPreviewRGBRGBItem: typeof import('./src/components/colorPicker/Preview/RGB/RGBItem/index.vue')['default']
    ContextMenuContextMenu: typeof import('./src/components/contextMenu/ContextMenu.vue')['default']
    ContextMenuContextMenuGroup: typeof import('./src/components/contextMenu/ContextMenuGroup.vue')['default']
    ContextMenuContextMenuIconCheck: typeof import('./src/components/contextMenu/ContextMenuIconCheck.vue')['default']
    ContextMenuContextMenuIconRight: typeof import('./src/components/contextMenu/ContextMenuIconRight.vue')['default']
    ContextMenuContextMenuItem: typeof import('./src/components/contextMenu/ContextMenuItem.vue')['default']
    ContextMenuContextMenuSeparator: typeof import('./src/components/contextMenu/ContextMenuSeparator.vue')['default']
    ContextMenuContextSubMenu: typeof import('./src/components/contextMenu/ContextSubMenu.vue')['default']
    ContextMenuContextSubMenuWrapper: typeof import('./src/components/contextMenu/ContextSubMenuWrapper.vue')['default']
    DialogDialog: typeof import('./src/components/dialog/dialog.vue')['default']
    DropdownDropdownButton: typeof import('./src/components/dropdown/dropdownButton.vue')['default']
    EditorModulesSearchHeader: typeof import('./src/components/editorModules/searchHeader.vue')['default']
    FontLoaderFontLoadingProgress: typeof import('./src/components/fontLoader/FontLoadingProgress.vue')['default']
    GColorPickerGColorPicker: typeof import('./src/components/g-color-picker/g-color-picker.vue')['default']
    GColorPickerGColorPickerPanel: typeof import('./src/components/g-color-picker/g-color-picker-panel.vue')['default']
    GColorPickerPanel: typeof import('./src/components/g-color-picker/panel/index.vue')['default']
    GColorPickerPanelAlpha: typeof import('./src/components/g-color-picker/panel/alpha.vue')['default']
    GColorPickerPanelFormat: typeof import('./src/components/g-color-picker/panel/format/index.vue')['default']
    GColorPickerPanelFormatInputs: typeof import('./src/components/g-color-picker/panel/format/inputs.vue')['default']
    GColorPickerPanelHeader: typeof import('./src/components/g-color-picker/panel/header.vue')['default']
    GColorPickerPanelHue: typeof import('./src/components/g-color-picker/panel/hue.vue')['default']
    GColorPickerPanelLinearGradient: typeof import('./src/components/g-color-picker/panel/linear-gradient.vue')['default']
    GColorPickerPanelRadialGradient: typeof import('./src/components/g-color-picker/panel/radial-gradient.vue')['default']
    GColorPickerPanelSaturation: typeof import('./src/components/g-color-picker/panel/saturation.vue')['default']
    GColorPickerPanelSlider: typeof import('./src/components/g-color-picker/panel/slider.vue')['default']
    GColorPickerPanelSwatches: typeof import('./src/components/g-color-picker/panel/swatches.vue')['default']
    GColorPickerTrigger: typeof import('./src/components/g-color-picker/trigger.vue')['default']
    IconAlignCenter: typeof import('@arco-design/web-vue/es/icon')['IconAlignCenter']
    IconAlignLeft: typeof import('@arco-design/web-vue/es/icon')['IconAlignLeft']
    IconAlignRight: typeof import('@arco-design/web-vue/es/icon')['IconAlignRight']
    IconCheck: typeof import('@arco-design/web-vue/es/icon')['IconCheck']
    IconCheckCircle: typeof import('@arco-design/web-vue/es/icon')['IconCheckCircle']
    IconClockCircle: typeof import('@arco-design/web-vue/es/icon')['IconClockCircle']
    IconClose: typeof import('@arco-design/web-vue/es/icon')['IconClose']
    IconCopy: typeof import('@arco-design/web-vue/es/icon')['IconCopy']
    IconDelete: typeof import('@arco-design/web-vue/es/icon')['IconDelete']
    IconDown: typeof import('@arco-design/web-vue/es/icon')['IconDown']
    IconDownload: typeof import('@arco-design/web-vue/es/icon')['IconDownload']
    IconDragDotVertical: typeof import('@arco-design/web-vue/es/icon')['IconDragDotVertical']
    IconEdit: typeof import('@arco-design/web-vue/es/icon')['IconEdit']
    IconEmpty: typeof import('@arco-design/web-vue/es/icon')['IconEmpty']
    IconEye: typeof import('@arco-design/web-vue/es/icon')['IconEye']
    IconEyeInvisible: typeof import('@arco-design/web-vue/es/icon')['IconEyeInvisible']
    IconFile: typeof import('@arco-design/web-vue/es/icon')['IconFile']
    IconFolder: typeof import('@arco-design/web-vue/es/icon')['IconFolder']
    IconFullscreen: typeof import('@arco-design/web-vue/es/icon')['IconFullscreen']
    IconImage: typeof import('@arco-design/web-vue/es/icon')['IconImage']
    IconImport: typeof import('@arco-design/web-vue/es/icon')['IconImport']
    IconItalic: typeof import('@arco-design/web-vue/es/icon')['IconItalic']
    IconLayers: typeof import('@arco-design/web-vue/es/icon')['IconLayers']
    IconLock: typeof import('@arco-design/web-vue/es/icon')['IconLock']
    IconMenu: typeof import('@arco-design/web-vue/es/icon')['IconMenu']
    IconMinus: typeof import('@arco-design/web-vue/es/icon')['IconMinus']
    IconMosaic: typeof import('@arco-design/web-vue/es/icon')['IconMosaic']
    IconPaste: typeof import('@arco-design/web-vue/es/icon')['IconPaste']
    IconPlus: typeof import('@arco-design/web-vue/es/icon')['IconPlus']
    IconQuestionCircle: typeof import('@arco-design/web-vue/es/icon')['IconQuestionCircle']
    IconRedo: typeof import('@arco-design/web-vue/es/icon')['IconRedo']
    IconRefresh: typeof import('@arco-design/web-vue/es/icon')['IconRefresh']
    IconSave: typeof import('@arco-design/web-vue/es/icon')['IconSave']
    IconSearch: typeof import('@arco-design/web-vue/es/icon')['IconSearch']
    IconSettings: typeof import('@arco-design/web-vue/es/icon')['IconSettings']
    IconStop: typeof import('@arco-design/web-vue/es/icon')['IconStop']
    IconStrikethrough: typeof import('@arco-design/web-vue/es/icon')['IconStrikethrough']
    IconToBottom: typeof import('@arco-design/web-vue/es/icon')['IconToBottom']
    IconToLeft: typeof import('@arco-design/web-vue/es/icon')['IconToLeft']
    IconToRight: typeof import('@arco-design/web-vue/es/icon')['IconToRight']
    IconToTop: typeof import('@arco-design/web-vue/es/icon')['IconToTop']
    IconUnderline: typeof import('@arco-design/web-vue/es/icon')['IconUnderline']
    IconUndo: typeof import('@arco-design/web-vue/es/icon')['IconUndo']
    IconUnlock: typeof import('@arco-design/web-vue/es/icon')['IconUnlock']
    IconUpload: typeof import('@arco-design/web-vue/es/icon')['IconUpload']
    KeyKey: typeof import('./src/components/key/key.vue')['default']
    KeyKeySingle: typeof import('./src/components/key/keySingle.vue')['default']
    LabelBox: typeof import('./src/components/labelBox.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SvgIconSvgIcon: typeof import('./src/components/svgIcon/svgIcon.vue')['default']
    SwipeNumberSwipeNumber: typeof import('./src/components/swipeNumber/swipeNumber.vue')['default']
    TinymceTinyEditor: typeof import('./src/components/tinymce/tinyEditor.vue')['default']
    TooltipTipContentKey: typeof import('./src/components/tooltip/tipContentKey.vue')['default']
    TreeBaseNode: typeof import('./src/components/tree/base-node.vue')['default']
    TreeExpandTransition: typeof import('./src/components/tree/expand-transition.vue')['default']
    TreeNode: typeof import('./src/components/tree/node.vue')['default']
    TreeNodeSwitcher: typeof import('./src/components/tree/node-switcher.vue')['default']
    TreeTransitionNodeList: typeof import('./src/components/tree/transition-node-list.vue')['default']
    TreeTree: typeof import('./src/components/tree/tree.vue')['default']
    UploadMAUpload: typeof import('./src/components/upload/m-a-upload.vue')['default']
    VueWaterfallPluginNextComponentsLazyImg: typeof import('./src/components/vue-waterfall-plugin-next/components/LazyImg.vue')['default']
    VueWaterfallPluginNextComponentsWaterfall: typeof import('./src/components/vue-waterfall-plugin-next/components/Waterfall.vue')['default']
  }
}
