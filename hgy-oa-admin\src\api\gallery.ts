import { request } from '@/utils/request'

// 分类相关接口类型定义
export interface Category {
  id: number
  name: string
  parent_id?: number | null
  icon?: string
  sort_order: number
  description?: string
  is_active: 0 | 1
  created_at: string
  updated_at: string
  children?: Category[]
}

export interface CreateCategoryParams {
  name: string
  parent_id?: number | null
  icon?: string
  sort_order?: number
  description?: string
  is_active?: 0 | 1
}

export interface UpdateCategoryParams {
  name: string
  parent_id?: number | null
  icon?: string
  sort_order?: number
  description?: string
  is_active?: 0 | 1
}

// 图片相关接口类型定义
export interface GalleryImage {
  id: number
  title: string
  filename: string
  original_name: string
  file_path: string
  thumbnail_path?: string
  file_size: number
  width: number
  height: number
  format: string
  primary_category_id?: number
  secondary_category_id?: number
  primary_category?: string
  secondary_category?: string
  description?: string
  upload_user_id: number
  upload_user?: string
  download_count: number
  view_count: number
  is_public: 0 | 1
  is_featured: 0 | 1
  created_at: string
  updated_at: string
}

export interface ImageListParams {
  pageNum?: number
  pageSize?: number
  keyword?: string
  primaryCategory?: string | number
  secondaryCategory?: string | number
  isPublic?: string | number
}

export interface ImageListResponse {
  records: GalleryImage[]
  total: number
  pageNum: number
  pageSize: number
}

// 分类管理API
export const getCategoryList = () => {
  return request.get('/api/gallery/categories')
}

export const getPrimaryCategories = () => {
  return request.get('/api/gallery/categories/primary')
}

export const getSecondaryCategories = (parentId: number) => {
  return request.get(`/api/gallery/categories/secondary/${parentId}`)
}

export const createCategory = (data: CreateCategoryParams) => {
  return request.post('/api/gallery/categories', data)
}

export const updateCategory = (id: number, data: UpdateCategoryParams) => {
  return request.put(`/api/gallery/categories/${id}`, data)
}

export const deleteCategory = (id: number) => {
  return request.delete(`/api/gallery/categories/${id}`)
}

// 图片管理API
export const getImageList = (params: ImageListParams) => {
  return request.get('/api/gallery/images', { params })
}

export const getPublicImageList = (params: ImageListParams) => {
  return request.get('/api/gallery/images/public', { params })
}

export const uploadImage = (formData: FormData) => {
  return request.post('/api/gallery/images', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

export const updateImage = (id: number, data: any) => {
  return request.put(`/api/gallery/images/${id}`, data)
}

export const deleteImage = (id: number) => {
  return request.delete(`/api/gallery/images/${id}`)
}

export const batchDeleteImages = (ids: number[]) => {
  return request.post('/api/gallery/images/batch-delete', { ids })
}

export const updateImageStatus = (id: number, isPublic: 0 | 1) => {
  return request.patch(`/api/gallery/images/${id}/status`, { is_public: isPublic })
}

// 部门相关接口类型定义
export interface Department {
  id: number
  name: string
  code: string
  description?: string
  manager_id?: number
  parent_id?: number
  sort_order: number
  status: 0 | 1
  created_at: string
  updated_at: string
  manager_name?: string
  manager_nickname?: string
  parent_name?: string
  user_count?: number
}

export interface CreateDepartmentParams {
  name: string
  code: string
  description?: string
  manager_id?: number
  parent_id?: number
  sort_order?: number
  status?: 0 | 1
}

export interface UpdateDepartmentParams {
  name: string
  code: string
  description?: string
  manager_id?: number
  parent_id?: number
  sort_order?: number
  status?: 0 | 1
}

export interface DepartmentListParams {
  pageNum?: number
  pageSize?: number
  keyword?: string
  status?: string | number
}

export interface DepartmentListResponse {
  records: Department[]
  total: number
  pageNum: number
  pageSize: number
}

// 部门管理API
export const getDepartmentList = (params: DepartmentListParams) => {
  return request.get('/api/department/list', { params })
}

export const getAllDepartments = () => {
  return request.get('/api/department/all')
}

export const getDepartmentDetail = (id: number) => {
  return request.get(`/api/department/${id}`)
}

export const createDepartment = (data: CreateDepartmentParams) => {
  return request.post('/api/department', data)
}

export const updateDepartment = (id: number, data: UpdateDepartmentParams) => {
  return request.put(`/api/department/${id}`, data)
}

export const deleteDepartment = (id: number) => {
  return request.delete(`/api/department/${id}`)
}

export const batchDeleteDepartments = (ids: number[]) => {
  return request.post('/api/department/batch-delete', { ids })
}
