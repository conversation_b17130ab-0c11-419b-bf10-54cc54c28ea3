# 前端项目部署文档

## 项目概述

图片制作站前端管理系统，基于Vue 3 + Vite + TypeScript开发，提供图片编辑器、模板管理、用户管理等功能。

## 环境要求

- Node.js v22.12.0 (推荐使用nvm管理Node版本)
- pnpm >= 8.0.0
- 现代浏览器支持 (Chrome 88+, Firefox 78+, Safari 14+)

## 环境配置

### 本地开发环境

```bash
# 前端访问域名
http://localhost:5173/

# API配置
VITE_API_BASE_URL=http://localhost:3001

# 应用配置
VITE_APP_TITLE=图片制作站 - 本地开发
VITE_APP_VERSION=1.0.0
VITE_DEVTOOLS=true
```

### 测试环境

```bash
# 前端访问域名
https://image-admin.dlmu.cc

# API配置
VITE_API_BASE_URL=https://image-api.dlmu.cc

# 应用配置
VITE_APP_TITLE=图片制作站 - 测试环境
VITE_APP_VERSION=1.0.0
VITE_DEVTOOLS=false
```

## 部署步骤

### 1. 克隆代码

```bash
git clone [repository-url]
cd hgy-oa-admin
```

### 2. 安装Node.js和依赖

```bash
# 使用nvm安装指定版本的Node.js (项目根目录有.nvmrc文件)
nvm use

# 安装pnpm
npm install -g pnpm

# 安装项目依赖
pnpm install
```

### 3. 配置环境变量

#### 本地开发环境
```bash
# 使用 .env.local 文件（已创建）
pnpm dev:local
```

#### 测试环境
```bash
# 使用 .env.test 文件（已创建）
pnpm dev:test
```

### 4. 开发调试

```bash
# 本地开发环境启动
pnpm dev:local

# 测试环境模式启动
pnpm dev:test
```

### 5. 构建部署

#### 测试环境构建
```bash
pnpm build:test
```

#### 生产环境构建
```bash
# 设置环境变量
export VITE_API_BASE_URL=https://your-api-domain.com
export VITE_APP_TITLE=图片制作站
export VITE_APP_VERSION=1.0.0

# 构建
pnpm build
```

### 6. 部署到服务器

#### 静态文件部署
```bash
# 构建完成后，dist目录包含所有静态文件
# 将dist目录内容上传到Web服务器

# 示例：使用nginx
cp -r dist/* /var/www/html/
```

#### Docker部署
```dockerfile
# Dockerfile示例
FROM nginx:alpine
COPY dist/ /usr/share/nginx/html/
COPY nginx.conf /etc/nginx/nginx.conf
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

## 可用脚本

```bash
pnpm dev           # 开发环境启动（默认本地模式）
pnpm dev:local     # 本地开发环境启动
pnpm dev:test      # 测试环境模式启动
pnpm build         # 生产环境构建
pnpm build:test    # 测试环境构建
pnpm preview       # 预览构建结果
```

## 目录结构

```
hgy-oa-admin/
├── src/
│   ├── api/                # API接口
│   ├── assets/             # 静态资源
│   │   └── data/fonts/     # 本地字体文件
│   ├── components/         # 公共组件
│   ├── layouts/            # 布局组件
│   ├── router/             # 路由配置
│   ├── store/              # 状态管理
│   │   └── modules/font/   # 字体管理模块
│   ├── utils/              # 工具函数
│   ├── views/              # 页面组件
│   │   ├── Admin/          # 管理页面
│   │   ├── Editor/         # 编辑器页面
│   │   └── Login/          # 登录页面
│   └── main.ts             # 应用入口
├── public/
│   └── fonts/              # 公共字体文件
├── .env.local              # 本地环境配置
├── .env.test               # 测试环境配置
├── vite.config.ts          # Vite配置
└── package.json
```

## 功能模块

### 核心功能
- **图片编辑器**: 基于Leafer.js的在线图片编辑功能
- **模板管理**: 模板保存、加载、分类管理
- **字体管理**: 本地字体加载、字体预览
- **用户管理**: 用户认证、权限控制
- **素材管理**: 图片、图形、文字素材管理

### 页面路由
- `/login` - 登录页面
- `/index` - 首页
- `/editor` - 图片编辑器
- `/admin` - 管理后台
  - `/admin/users` - 用户管理
  - `/admin/templates` - 模板管理
  - `/admin/materials` - 素材管理

## Nginx配置示例

```nginx
server {
    listen 80;
    server_name image-admin.dlmu.cc;
    root /var/www/html;
    index index.html;

    # 处理Vue Router的history模式
    location / {
        try_files $uri $uri/ /index.html;
    }

    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # API代理（如果需要）
    location /api/ {
        proxy_pass http://localhost:3001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }

    # 字体文件CORS设置
    location /fonts/ {
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';
        add_header Access-Control-Allow-Headers 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range';
    }
}
```

## 故障排除

### 常见问题

1. **API请求失败**
   - 检查VITE_API_BASE_URL配置是否正确
   - 确认后端服务是否正常运行
   - 检查网络连接和CORS设置

2. **字体加载失败**
   - 确认字体文件是否存在于public/fonts目录
   - 检查字体文件路径配置
   - 验证字体文件格式是否正确

3. **路由404错误**
   - 确认nginx配置了history模式支持
   - 检查路由配置是否正确

4. **构建失败**
   - 清除node_modules重新安装依赖
   - 检查TypeScript类型错误
   - 确认环境变量配置

### 调试方法

```bash
# 查看详细构建信息
pnpm build --debug

# 分析构建包大小
pnpm build --analyze

# 检查依赖问题
pnpm audit
```

## 性能优化

### 构建优化
- 代码分割和懒加载
- 静态资源压缩
- Tree-shaking优化
- 图片资源优化

### 运行时优化
- 组件缓存
- 虚拟滚动
- 防抖和节流
- 内存泄漏防护

## 安全注意事项

1. **环境变量安全**
   - 不要在前端代码中暴露敏感信息
   - 使用VITE_前缀的环境变量

2. **XSS防护**
   - 输入内容过滤和转义
   - CSP内容安全策略

3. **HTTPS部署**
   - 生产环境强制使用HTTPS
   - 配置安全头部

## 监控和维护

### 错误监控
- 集成错误监控服务
- 用户行为分析
- 性能监控

### 更新维护
- 定期更新依赖包
- 安全漏洞检查
- 功能迭代部署
