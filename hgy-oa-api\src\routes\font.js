import express from 'express';
import { query, paginate } from '../config/database.js';

const router = express.Router();

// 获取字体列表
router.get('/list', async (req, res) => {
  try {
    const { pageNum = 1, pageSize = 10 } = req.query;
    
    const sql = `
      SELECT 
        id,
        name,
        code,
        preview_url,
        download_url,
        file_size,
        font_family,
        font_weight,
        font_style,
        category_id,
        tags,
        is_system,
        status,
        created_at,
        updated_at
      FROM fonts 
      WHERE status = 1 
      ORDER BY is_system DESC, created_at DESC
    `;
    
    const result = await paginate(sql, [], pageNum, pageSize);
    
    // 解析tags字段
    result.records = result.records.map(item => ({
      ...item,
      tags: item.tags ? (typeof item.tags === 'string' ? JSON.parse(item.tags) : item.tags) : [],
      is_system: Boolean(item.is_system)
    }));
    
    res.success(result);
  } catch (error) {
    console.error('获取字体列表失败:', error);
    res.error('获取字体列表失败');
  }
});

// 根据分类获取字体
router.get('/list/category/:categoryId', async (req, res) => {
  try {
    const { categoryId } = req.params;
    const { pageNum = 1, pageSize = 10 } = req.query;
    
    const sql = `
      SELECT 
        id,
        name,
        code,
        preview_url,
        download_url,
        file_size,
        font_family,
        font_weight,
        font_style,
        category_id,
        tags,
        is_system,
        status,
        created_at,
        updated_at
      FROM fonts 
      WHERE status = 1 AND category_id = ?
      ORDER BY is_system DESC, created_at DESC
    `;
    
    const result = await paginate(sql, [categoryId], pageNum, pageSize);
    
    // 解析tags字段
    result.records = result.records.map(item => ({
      ...item,
      tags: item.tags ? (typeof item.tags === 'string' ? JSON.parse(item.tags) : item.tags) : [],
      is_system: Boolean(item.is_system)
    }));
    
    res.success(result);
  } catch (error) {
    console.error('获取分类字体失败:', error);
    res.error('获取分类字体失败');
  }
});

// 搜索字体
router.get('/search', async (req, res) => {
  try {
    const { keyword, pageNum = 1, pageSize = 10 } = req.query;
    
    if (!keyword) {
      return res.error('搜索关键词不能为空', 400);
    }
    
    const sql = `
      SELECT 
        id,
        name,
        code,
        preview_url,
        download_url,
        file_size,
        font_family,
        font_weight,
        font_style,
        category_id,
        tags,
        is_system,
        status,
        created_at,
        updated_at
      FROM fonts 
      WHERE status = 1 AND (name LIKE ? OR code LIKE ? OR font_family LIKE ? OR tags LIKE ?)
      ORDER BY is_system DESC, created_at DESC
    `;
    
    const searchTerm = `%${keyword}%`;
    const result = await paginate(sql, [searchTerm, searchTerm, searchTerm, searchTerm], pageNum, pageSize);
    
    // 解析tags字段
    result.records = result.records.map(item => ({
      ...item,
      tags: item.tags ? (typeof item.tags === 'string' ? JSON.parse(item.tags) : item.tags) : [],
      is_system: Boolean(item.is_system)
    }));
    
    res.success(result);
  } catch (error) {
    console.error('搜索字体失败:', error);
    res.error('搜索字体失败');
  }
});

// 获取系统字体
router.get('/system', async (req, res) => {
  try {
    const sql = `
      SELECT 
        id,
        name,
        code,
        font_family,
        font_weight,
        font_style
      FROM fonts 
      WHERE status = 1 AND is_system = 1
      ORDER BY name ASC
    `;
    
    const result = await query(sql);
    res.success({ records: result, total: result.length });
  } catch (error) {
    console.error('获取系统字体失败:', error);
    res.error('获取系统字体失败');
  }
});

// 创建字体
router.post('/font', async (req, res) => {
  try {
    const { 
      name, 
      code, 
      preview_url, 
      download_url, 
      file_size, 
      font_family, 
      font_weight = 'normal', 
      font_style = 'normal',
      category_id,
      tags,
      is_system = false
    } = req.body;
    
    if (!name || !code || !font_family) {
      return res.error('字体名称、代码和字体族不能为空', 400);
    }
    
    const sql = `
      INSERT INTO fonts (
        name, code, preview_url, download_url, file_size, 
        font_family, font_weight, font_style, category_id, 
        tags, is_system, status, created_at, updated_at
      )
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1, NOW(), NOW())
    `;
    
    const tagsStr = tags ? (typeof tags === 'object' ? JSON.stringify(tags) : tags) : null;
    const result = await query(sql, [
      name, code, preview_url, download_url, file_size,
      font_family, font_weight, font_style, category_id,
      tagsStr, is_system ? 1 : 0
    ]);
    
    res.success({ id: result.insertId }, '字体创建成功');
  } catch (error) {
    console.error('创建字体失败:', error);
    res.error('创建字体失败');
  }
});

export default router;
