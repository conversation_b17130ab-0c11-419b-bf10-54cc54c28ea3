<template>
  <div class="document-list">
    <div class="page-header">
      <h1>文案管理</h1>
      <div class="header-actions">
        <a-button @click="handleRefresh" style="margin-right: 8px;">
          <template #icon>
            <icon-refresh />
          </template>
          刷新
        </a-button>
        <a-button type="primary" @click="showUploadModal">
          <template #icon>
            <icon-upload />
          </template>
          上传文案
        </a-button>
      </div>
    </div>

    <!-- 搜索筛选 -->
    <a-card style="margin-bottom: 16px;">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-input 
            v-model="searchForm.keyword" 
            placeholder="搜索文件名称"
            allow-clear
            @press-enter="handleSearch"
          >
            <template #prefix>
              <icon-search />
            </template>
          </a-input>
        </a-col>
        <a-col :span="4">
          <a-select v-model="searchForm.fileType" placeholder="文件类型" allow-clear>
            <a-option value="word">Word文档</a-option>
            <a-option value="excel">Excel表格</a-option>
            <a-option value="image">图片</a-option>
          </a-select>
        </a-col>
        <a-col :span="6">
          <a-button type="primary" @click="handleSearch">搜索</a-button>
          <a-button style="margin-left: 8px;" @click="handleReset">重置</a-button>
        </a-col>
      </a-row>
    </a-card>
    
    <a-card>
      <a-table
        :columns="columns"
        :data="documentData"
        :pagination="pagination"
        :loading="loading"
        @page-change="handlePageChange"
        @page-size-change="handlePageSizeChange"
      >


        <template #file_type="{ record }">
          <a-tag :color="getFileTypeColor(record.file_type)">
            {{ getFileTypeName(record.file_type) }}
          </a-tag>
        </template>

        <template #file_size="{ record }">
          {{ formatFileSize(record.file_size) }}
        </template>

        <template #upload_user="{ record }">
          {{ record.upload_user_nickname || record.upload_user || '-' }}
        </template>

        <template #remark="{ record }">
          <div class="remark-content">
            <span v-if="record.remark" :title="record.remark">{{ record.remark }}</span>
            <span v-else class="no-data">-</span>
          </div>
        </template>

        <template #status="{ record }">
          <a-switch
            :model-value="record.is_public === 1"
            @change="(value) => handleStatusChange(record, value)"
            size="small"
          />
        </template>

        <template #created_at="{ record }">
          {{ formatDate(record.created_at) }}
        </template>

        <template #actions="{ record }">
          <a-space size="mini">
            <a-button type="text" size="small" @click="handleDownload(record)">
              下载
            </a-button>
            <a-button type="text" size="small" @click="showEditModal(record)">
              编辑
            </a-button>
            <a-button type="text" size="small" status="danger" @click="handleDelete(record)">
              删除
            </a-button>
          </a-space>
        </template>
      </a-table>
    </a-card>

    <!-- 上传文案弹窗 -->
    <a-modal
      v-model:visible="uploadModalVisible"
      title="上传文案"
      width="600px"
      @ok="handleUploadSubmit"
      @cancel="handleUploadCancel"
      :ok-loading="loading"
    >
      <a-form :model="uploadForm" :rules="uploadRules" ref="uploadFormRef" layout="vertical">
        <a-form-item label="文案文件" field="file">
          <a-upload
            ref="uploadRef"
            v-model:file-list="fileList"
            :auto-upload="false"
            :show-file-list="true"
            :limit="1"
            accept=".doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png,.gif"
            @change="handleFileChange"
            @before-upload="handleBeforeUpload"
          >
            <template #upload-button>
              <div class="upload-area">
                <icon-upload style="font-size: 48px; color: #c9cdd4;" />
                <div style="margin-top: 8px; color: #86909c;">点击或拖拽上传文案</div>
                <div style="margin-top: 4px; color: #c9cdd4; font-size: 12px;">
                  支持 Word、Excel、图片格式，文件大小不超过 50MB
                </div>
              </div>
            </template>
          </a-upload>
        </a-form-item>
        
        <a-form-item label="文件名称" field="title">
          <a-input v-model="uploadForm.title" placeholder="请输入文件名称" />
        </a-form-item>
        
        <a-form-item label="文件描述" field="description">
          <a-textarea v-model="uploadForm.description" placeholder="请输入文件描述" :rows="3" />
        </a-form-item>

        <a-form-item label="备注" field="remark">
          <a-input v-model="uploadForm.remark" placeholder="请输入备注信息（可选）" />
        </a-form-item>
        
        <a-form-item label="是否公开" field="is_public">
          <a-switch v-model="uploadForm.is_public" checked-text="公开" unchecked-text="私有" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 编辑文案弹窗 -->
    <a-modal
      v-model:visible="editModalVisible"
      title="编辑文案信息"
      width="600px"
      @ok="handleEditSave"
      @cancel="editModalVisible = false"
    >
      <a-form :model="editForm" layout="vertical">
        <a-form-item label="文件名称">
          <a-input v-model="editForm.title" placeholder="请输入文件名称" />
        </a-form-item>
        
        <a-form-item label="文件描述">
          <a-textarea v-model="editForm.description" placeholder="请输入文件描述" :rows="3" />
        </a-form-item>

        <a-form-item label="备注">
          <a-input v-model="editForm.remark" placeholder="请输入备注信息（可选）" />
        </a-form-item>
        
        <a-form-item label="是否公开">
          <a-switch v-model="editForm.is_public" checked-text="公开" unchecked-text="私有" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { Message, Modal } from '@arco-design/web-vue'
import {
  IconRefresh,
  IconUpload,
  IconSearch
} from '@arco-design/web-vue/es/icon'
import { request } from '@/utils/request'

// 响应式数据
const loading = ref(false)
const documentData = ref([])
const uploadModalVisible = ref(false)
const editModalVisible = ref(false)
const currentEditDocument = ref(null)
const fileList = ref([])

// 搜索表单
const searchForm = reactive({
  keyword: '',
  fileType: ''
})

// 上传表单
const uploadForm = reactive({
  title: '',
  description: '',
  remark: '',
  is_public: true,
  file: null
})

// 编辑表单
const editForm = reactive({
  title: '',
  description: '',
  remark: '',
  is_public: true
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showTotal: true,
  showPageSize: true
})

// 表格列配置
const columns = [
  { title: '文件名称', dataIndex: 'title', width: 220 },
  { title: '文件类型', slotName: 'file_type', width: 100, align: 'center' },
  { title: '文件大小', slotName: 'file_size', width: 100, align: 'center' },
  { title: '文件描述', dataIndex: 'description', width: 180, ellipsis: true, tooltip: true },
  { title: '上传用户', slotName: 'upload_user', width: 120 },
  { title: '备注', slotName: 'remark', width: 140 },
  { title: '上传时间', slotName: 'created_at', width: 120 },
  { title: '状态', slotName: 'status', width: 80, align: 'center' },
  { title: '操作', slotName: 'actions', width: 150, align: 'center', fixed: 'right' }
]

// 表单引用
const uploadFormRef = ref()
const uploadRef = ref()

// 表单验证规则
const uploadRules = {
  title: [
    { required: true, message: '请输入文件名称' }
  ],
  file: [
    { required: true, message: '请选择文件' }
  ]
}

// 工具函数
const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 格式化日期（只显示年月日）
const formatDate = (dateTime: string) => {
  if (!dateTime) return '-'
  const date = new Date(dateTime)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

const getFileTypeColor = (type: string) => {
  const colorMap: Record<string, string> = {
    'word': 'blue',
    'excel': 'green',
    'image': 'orange'
  }
  return colorMap[type] || 'gray'
}

const getFileTypeName = (type: string) => {
  const nameMap: Record<string, string> = {
    'word': 'Word文档',
    'excel': 'Excel表格',
    'image': '图片'
  }
  return nameMap[type] || type
}

// 获取文案列表
const fetchDocumentList = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.current,
      pageSize: pagination.pageSize,
      keyword: searchForm.keyword,
      fileType: searchForm.fileType
    }

    console.log('请求参数:', params)

    const response = await request.get('/api/documents', { params })

    console.log('完整响应:', response)
    console.log('响应success:', response.success)
    console.log('响应data:', response.data)

    // request工具已经解包了一层，response就是API返回的完整结构
    if (response && response.success === true) {
      console.log('进入成功分支')
      console.log('response.data:', response.data)
      console.log('response.data.list:', response.data.list)

      documentData.value = response.data.list || []
      pagination.total = response.data.total || 0

      console.log('设置文案数据:', documentData.value)
      console.log('设置总数:', pagination.total)
      console.log('documentData.value长度:', documentData.value.length)
    } else {
      console.error('进入失败分支')
      console.error('response:', response)
      console.error('response.success:', response?.success)
      Message.error((response as any)?.message || '获取文案列表失败')
      documentData.value = []
      pagination.total = 0
    }
  } catch (error: any) {
    console.error('获取文案列表失败:', error)
    Message.error('获取文案列表失败: ' + (error.message || '未知错误'))
    documentData.value = []
    pagination.total = 0
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.current = 1
  fetchDocumentList()
}

// 重置搜索
const handleReset = () => {
  searchForm.keyword = ''
  searchForm.fileType = ''
  pagination.current = 1
  fetchDocumentList()
}

// 刷新
const handleRefresh = () => {
  fetchDocumentList()
}

// 分页处理
const handlePageChange = (page: number) => {
  pagination.current = page
  fetchDocumentList()
}

const handlePageSizeChange = (pageSize: number) => {
  pagination.pageSize = pageSize
  pagination.current = 1
  fetchDocumentList()
}

// 显示上传弹窗
const showUploadModal = () => {
  uploadModalVisible.value = true
  // 重置表单
  uploadForm.title = ''
  uploadForm.description = ''
  uploadForm.remark = ''
  uploadForm.is_public = true
  uploadForm.file = null
  fileList.value = []
}

// 文件选择处理
const handleFileChange = (fileListParam: any[], file: any) => {
  // 更新文件列表
  fileList.value = fileListParam

  // 如果文件列表为空，清空表单中的文件字段
  if (fileListParam.length === 0) {
    uploadForm.file = null
    return
  }

  // 处理新添加的文件，自动设置标题
  if (file) {
    const rawFile = file.originFile || file.file || file

    // 确保文件列表中的每个项目都有正确的文件引用
    if (fileListParam.length > 0) {
      const fileItem = fileListParam[fileListParam.length - 1]
      if (!fileItem.originFile && !fileItem.file && rawFile) {
        fileItem.originFile = rawFile
        fileItem.file = rawFile
      }
    }

    if (rawFile && rawFile.name) {
      const filename = rawFile.name
      const dotIndex = filename.lastIndexOf('.')
      const title = dotIndex > 0 ? filename.substring(0, dotIndex) : filename

      // 自动填充文件名
      if (!uploadForm.title) {
        uploadForm.title = title
      }
    }
  }
}

const handleBeforeUpload = (file: any) => {
  // 检查文件类型
  const allowedTypes = [
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'image/jpeg',
    'image/jpg',
    'image/png',
    'image/gif'
  ]

  if (!allowedTypes.includes(file.type)) {
    Message.error('只支持 Word、Excel、图片格式!')
    return false
  }

  // 检查文件大小 (50MB)
  const isLt50M = file.size / 1024 / 1024 < 50
  if (!isLt50M) {
    Message.error('文件大小不能超过 50MB!')
    return false
  }

  // 手动创建文件列表项
  const fileItem = {
    uid: Date.now().toString(),
    name: file.name,
    status: 'init',
    originFile: file,
    file: file
  }

  // 更新文件列表
  fileList.value = [fileItem]

  // 更新表单中的文件字段（用于表单验证）
  uploadForm.file = file

  // 自动设置标题为文件名（去掉扩展名）
  if (file.name) {
    const filename = file.name
    const dotIndex = filename.lastIndexOf('.')
    const title = dotIndex > 0 ? filename.substring(0, dotIndex) : filename
    uploadForm.title = title
  }

  return false // 阻止自动上传，我们手动处理
}

// 调试用的事件处理函数
const handleExceed = () => {
  console.log('=== handleExceed 触发 ===')
}

const handleRemove = () => {
  console.log('=== handleRemove 触发 ===')
}





// 上传提交
const handleUploadSubmit = async () => {
  try {
    // 检查表单引用
    if (!uploadFormRef.value) {
      Message.error('表单初始化失败')
      return
    }

    const errors = await uploadFormRef.value.validate()
    if (errors) {
      return
    }

    if (fileList.value.length === 0) {
      Message.error('请选择要上传的文件')
      return
    }

    const fileItem = fileList.value[0]

    // 获取原始文件对象
    const file = fileItem.originFile || fileItem.file || fileItem

    if (!file || !(file instanceof File)) {
      Message.error('文件不存在或格式不正确')
      return
    }

    loading.value = true

    // 创建FormData
    const formData = new FormData()
    formData.append('file', file)
    formData.append('title', uploadForm.title)
    formData.append('description', uploadForm.description || '')
    formData.append('remark', uploadForm.remark || '')
    formData.append('is_public', uploadForm.is_public ? '1' : '0')

    // 上传到七牛云
    const response = await request.post('/api/documents/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })

    if (response && response.success) {
      Message.success('文案上传成功')
      uploadModalVisible.value = false
      fetchDocumentList()
    } else {
      Message.error((response as any)?.message || '上传失败')
    }
  } catch (error: any) {
    Message.error('上传文案失败: ' + (error.message || '未知错误'))
  } finally {
    loading.value = false
  }
}

// 取消上传
const handleUploadCancel = () => {
  uploadModalVisible.value = false
  fileList.value = []
}

// 状态切换
const handleStatusChange = async (record: any, value: boolean) => {
  try {
    const response = await request.put(`/api/documents/${record.id}/status`, {
      is_public: value ? 1 : 0
    })

    if (response.data.success) {
      record.is_public = value ? 1 : 0
      Message.success('状态更新成功')
    } else {
      Message.error(response.data.message || '状态更新失败')
    }
  } catch (error) {
    console.error('更新文案状态失败:', error)
    Message.error('更新文案状态失败')
  }
}

// 下载文件
const handleDownload = async (record: any) => {
  try {
    if (record.file_path) {
      // 使用统一的API配置
      const { getApiBaseUrl } = await import('../../config/api')
      const downloadUrl = `${getApiBaseUrl()}/api/documents/${record.id}/download`
      console.log('下载URL:', downloadUrl)

      // 创建一个隐藏的a标签来触发下载
      const link = document.createElement('a')
      link.href = downloadUrl
      // 生成时间戳格式的文件名：原名(年月日时分秒).扩展名
      const fileExtension = record.filename ? record.filename.split('.').pop() : ''
      const now = new Date()
      const timestamp = now.getFullYear().toString() +
                       (now.getMonth() + 1).toString().padStart(2, '0') +
                       now.getDate().toString().padStart(2, '0') +
                       now.getHours().toString().padStart(2, '0') +
                       now.getMinutes().toString().padStart(2, '0') +
                       now.getSeconds().toString().padStart(2, '0')
      const downloadFilename = fileExtension ? `${record.title}(${timestamp}).${fileExtension}` : `${record.title}(${timestamp})`
      link.download = downloadFilename || '文件'
      link.style.display = 'none'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      Message.success('开始下载文件')
    } else if (record.remark) {
      // 如果有网盘地址，直接打开
      window.open(record.remark, '_blank')
    } else {
      Message.warning('暂无下载地址')
    }
  } catch (error) {
    console.error('下载失败:', error)
    Message.error('下载失败，请稍后重试')
  }
}

// 显示编辑弹窗
const showEditModal = (record: any) => {
  currentEditDocument.value = record
  editForm.title = record.title
  editForm.description = record.description || ''
  editForm.remark = record.remark || ''
  editForm.is_public = record.is_public === 1
  editModalVisible.value = true
}

// 保存编辑
const handleEditSave = async () => {
  try {
    const response = await request.put(`/api/documents/${currentEditDocument.value.id}`, {
      title: editForm.title,
      description: editForm.description,
      remark: editForm.remark,
      is_public: editForm.is_public ? 1 : 0
    })

    if (response.data.success) {
      Message.success('更新成功')
      editModalVisible.value = false
      fetchDocumentList()
    } else {
      Message.error(response.data.message || '更新失败')
    }
  } catch (error) {
    console.error('更新文案失败:', error)
    Message.error('更新文案失败')
  }
}

// 删除文案
const handleDelete = (record: any) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除文案"${record.title}"吗？删除后可在回收站中恢复。`,
    onOk: async () => {
      try {
        const response = await request.delete(`/api/documents/${record.id}`)
        if (response.data.success) {
          Message.success('删除成功')
          fetchDocumentList()
        } else {
          Message.error(response.data.message || '删除失败')
        }
      } catch (error) {
        console.error('删除文案失败:', error)
        Message.error('删除文案失败')
      }
    }
  })
}

onMounted(() => {
  fetchDocumentList()
})
</script>

<style scoped>
.document-list {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0;
  color: #262626;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.upload-area {
  padding: 40px;
  text-align: center;
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  background: #fafafa;
  cursor: pointer;
  transition: border-color 0.3s;
}

.upload-area:hover {
  border-color: #165dff;
}

.document-id {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-weight: 600;
  color: #165dff;
}

.netdisk-url a {
  color: #165dff;
  text-decoration: none;
  word-break: break-all;
}

.netdisk-url a:hover {
  text-decoration: underline;
}

.no-data {
  color: #86909c;
  font-style: italic;
}

:deep(.arco-table-th) {
  background-color: #fafafa;
}

:deep(.arco-table-td) {
  border-bottom: 1px solid #f0f0f0;
}
</style>
