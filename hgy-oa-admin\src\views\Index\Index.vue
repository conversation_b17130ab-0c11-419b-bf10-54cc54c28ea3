<template>
  <div class="index-container">
    <!-- Tabs 导航 -->
    <a-tabs v-model:active-key="activeTab" @change="handleTabChange">
      <!-- 图片展示栏 -->
      <a-tab-pane key="gallery" title="图片展示">
        <!-- 分类筛选 -->
        <div class="category-filter">
          <div class="filter-container">
            <!-- 一级分类 -->
            <div class="primary-categories">
              <span class="category-label">一级分类：</span>
              <span
                v-for="category in primaryCategories"
                :key="category.id"
                :class="['category-item', { active: currentPrimaryCategory === category.id }]"
                @click="handlePrimaryCategoryChange(category.id)"
              >
                {{ category.name }}
              </span>
            </div>

            <!-- 二级分类 -->
            <div class="secondary-categories" v-if="currentSecondaryCategories.length > 0">
              <span class="category-label">二级分类：</span>
              <span
                v-for="subCategory in currentSecondaryCategories"
                :key="subCategory.id"
                :class="['category-item', { active: currentSecondaryCategory === subCategory.id }]"
                @click="handleSecondaryCategoryChange(subCategory.id)"
              >
                {{ subCategory.name }}
              </span>
            </div>

            <div class="filter-actions">
              <a-input-search
                v-model="searchKeyword"
                placeholder="搜索图片..."
                style="width: 300px;"
                @search="handleSearch"
                allow-clear
              />
            </div>
          </div>
        </div>

    <!-- 图片展示区域 -->
    <div class="gallery-container">
      <div class="gallery-grid" v-if="imageList.length > 0">
        <div
          v-for="image in imageList"
          :key="image.id"
          class="gallery-item"
          @click="handleImageClick(image)"
        >
          <div class="image-wrapper">
            <img :src="image.thumbnail" :alt="image.title" />
          </div>
          <div class="image-info">
            <div class="image-title-row">
              <h4 class="image-title">{{ image.title }}</h4>
              <span class="image-id">#{{ formatImageId(image.id) }}</span>
            </div>
            <div class="image-meta">
              <span class="image-size">{{ image.width }} × {{ image.height }}</span>
              <span class="netdisk-text" v-if="image.description">{{ image.description }}</span>
              <span class="no-netdisk" v-else>暂无网盘</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <a-spin size="large" />
        <p>正在加载图片...</p>
      </div>

      <!-- 空状态 -->
      <div v-if="!loading && imageList.length === 0" class="empty-container">
        <a-empty description="暂无图片数据" />
      </div>
    </div>

        <!-- 分页 -->
        <div class="pagination-container" v-if="total > 0">
          <a-pagination
            v-model:current="currentPage"
            v-model:page-size="pageSize"
            :total="total"
            :show-total="true"
            :show-page-size="true"
            @change="handlePageChange"
            @page-size-change="handlePageSizeChange"
          />
        </div>
      </a-tab-pane>

      <!-- 套件展示栏 -->
      <a-tab-pane key="suites" title="套件展示">
        <!-- 套件分类筛选 -->
        <div class="category-filter">
          <div class="filter-container">
            <!-- 一级套件分类 -->
            <div class="primary-categories">
              <span class="category-label">一级分类：</span>
              <span
                v-for="category in suitePrimaryCategories"
                :key="category.id"
                :class="['category-item', { active: currentSuitePrimaryCategory === category.id }]"
                @click="handleSuitePrimaryCategoryChange(category.id)"
              >
                {{ category.name }}
              </span>
            </div>

            <!-- 二级套件分类 -->
            <div class="secondary-categories" v-if="currentSuiteSecondaryCategories.length > 0">
              <span class="category-label">二级分类：</span>
              <span
                v-for="subCategory in currentSuiteSecondaryCategories"
                :key="subCategory.id"
                :class="['category-item', { active: currentSuiteSecondaryCategory === subCategory.id }]"
                @click="handleSuiteSecondaryCategoryChange(subCategory.id)"
              >
                {{ subCategory.name }}
              </span>
            </div>

            <div class="filter-actions">
              <a-input-search
                v-model="suiteSearchKeyword"
                placeholder="搜索套件..."
                style="width: 300px;"
                @search="handleSuiteSearch"
                allow-clear
              />
            </div>
          </div>
        </div>

        <!-- 套件图片展示区域 -->
        <div class="gallery-container">
          <div class="gallery-grid" v-if="suiteImageList.length > 0">
            <div
              v-for="image in suiteImageList"
              :key="image.id"
              class="gallery-item"
              @click="handleImageClick(image)"
            >
              <div class="image-wrapper">
                <img :src="image.thumbnail" :alt="image.title" />
              </div>
              <div class="image-info">
                <div class="image-title-row">
                  <h4 class="image-title">{{ image.title }}</h4>
                  <span class="image-id">#{{ formatImageId(image.id) }}</span>
                </div>
                <div class="image-meta">
                  <span class="image-size">{{ image.width }} × {{ image.height }}</span>
                  <span class="netdisk-text" v-if="image.description">{{ image.description }}</span>
                  <span class="no-netdisk" v-else>暂无网盘</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 加载状态 -->
          <div v-if="suiteImageLoading" class="loading-container">
            <a-spin size="large" />
            <p>正在加载图片...</p>
          </div>

          <!-- 空状态 -->
          <div v-if="!suiteImageLoading && suiteImageList.length === 0" class="empty-container">
            <a-empty description="暂无图片数据" />
          </div>

          <!-- 套件图片分页 -->
          <div class="pagination-container" v-if="suiteImageTotal > 0">
            <a-pagination
              v-model:current="suiteImageCurrentPage"
              v-model:page-size="suiteImagePageSize"
              :total="suiteImageTotal"
              :show-total="true"
              :show-page-size="true"
              @change="handleSuiteImagePageChange"
              @page-size-change="handleSuiteImagePageSizeChange"
            />
          </div>
        </div>
      </a-tab-pane>
    </a-tabs>

    <!-- 图片预览弹窗 -->
    <a-modal
      v-model:visible="previewVisible"
      :footer="false"
      width="90%"
      :mask-closable="true"
    >
      <template #title>
        <div style="display: flex; justify-content: space-between; align-items: center; width: 100%;">
          <span>{{ currentPreviewImage?.title }}</span>
          <!-- <div style="display: flex; align-items: center; gap: 8px;">
            <span style="font-size: 14px; font-weight: normal;">放大镜</span>
            <a-switch v-model="magnifierEnabled" size="small" />
          </div> -->
        </div>
      </template>
      <div class="preview-container" v-if="currentPreviewImage" ref="previewContainerRef">
        <!-- 放大镜显示区域 -->
        <div v-show="magnifierEnabled && showMagnifier" class="magnifier-display" :style="magnifierStyle"></div>

        <a-row :gutter="24">
          <!-- 左侧图片 -->
          <a-col :span="12">
            <div class="preview-image-container">
              <div class="image-scroll-wrapper">
                <div
                  class="magnifier-container"
                  @mousemove="handleMouseMove"
                  @mouseenter="handleMouseEnter"
                  @mouseleave="handleMouseLeave"
                >
                  <img
                    :src="currentPreviewImage.url"
                    :alt="currentPreviewImage.title"
                    class="preview-image"
                    @load="handleImageLoad"
                    ref="previewImageRef"
                  />
                  <!-- 放大镜指示器 -->
                  <div
                    v-show="magnifierEnabled && showMagnifier"
                    class="magnifier-lens"
                    :style="lensStyle"
                  ></div>
                </div>
              </div>
            </div>
          </a-col>

          <!-- 右侧信息 -->
          <a-col :span="12">
            <div class="info-section">
              <h4>图片信息</h4>
              <a-descriptions :column="1" size="small" bordered>
                <a-descriptions-item label="尺寸">{{ currentPreviewImage.width }} × {{ currentPreviewImage.height }}</a-descriptions-item>
                <a-descriptions-item label="格式">{{ currentPreviewImage.format }}</a-descriptions-item>
                <a-descriptions-item label="大小">{{ formatFileSize(currentPreviewImage.size) }}</a-descriptions-item>

                <a-descriptions-item label="基础分类">
                  <div v-if="currentPreviewImage.categories && currentPreviewImage.categories.length > 0">
                    <a-tag v-for="category in currentPreviewImage.categories" :key="category.id" style="margin-right: 4px; margin-bottom: 4px;">
                      {{ category.name }}
                    </a-tag>
                  </div>
                  <span v-else class="no-data">暂无分类</span>
                </a-descriptions-item>

                <a-descriptions-item label="网盘地址">
                  <div v-if="currentPreviewImage.description" class="netdisk-url">
                    <span style="word-break: break-all; font-size: 12px;">{{ currentPreviewImage.description }}</span>
                  </div>
                  <span v-else class="no-data">暂无网盘地址</span>
                </a-descriptions-item>

                <a-descriptions-item label="资料准备">
                  <div v-if="currentPreviewImage.documents && currentPreviewImage.documents.length > 0" class="material-preparation">
                    <div v-for="document in currentPreviewImage.documents" :key="document.id" class="document-item" style="margin-bottom: 8px;">
                      <a-button
                        type="text"
                        size="small"
                        @click="downloadDocument(document)"
                        style="padding: 4px 8px; height: auto; font-size: 12px; color: #165dff;"
                      >
                        <template #icon>
                          <icon-download />
                        </template>
                        {{ document.title }}.{{ getFileExtension(document.file_type) }}
                      </a-button>
                    </div>
                  </div>
                  <span v-else class="no-data">暂无资料准备文案</span>
                </a-descriptions-item>

                <a-descriptions-item label="是否套件">
                  <div v-if="currentPreviewImage.suites && currentPreviewImage.suites.length > 0" class="suite-info">
                    <div v-for="suite in currentPreviewImage.suites" :key="suite.id" style="margin-bottom: 4px;">
                      <a-button
                        type="text"
                        size="small"
                        @click="goToSuiteCategory(suite)"
                        style="padding: 2px 6px; height: auto; font-size: 12px; color: #165dff;"
                      >
                        {{ suite.name }}
                      </a-button>
                    </div>
                  </div>
                  <span v-else class="no-data">不属于套件</span>
                </a-descriptions-item>


              </a-descriptions>
            </div>
          </a-col>
        </a-row>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { Message } from '@arco-design/web-vue'
import { IconDownload } from '@arco-design/web-vue/es/icon'

import { request } from '@/utils/request'
import { getCategoryTree, type Category } from '@/api/category'

// 本地分类接口定义（支持混合ID类型）
interface LocalCategory {
  id: string | number
  name: string
  icon?: string
  children?: LocalCategory[]
}

// 路由实例
const route = useRoute()
const router = useRouter()

// 响应式数据
const loading = ref(false)
const imageList = ref([])
const currentPrimaryCategory = ref<string | number>('all')
const currentSecondaryCategory = ref<string | number>('all')
const searchKeyword = ref('')
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)
const previewVisible = ref(false)
const currentPreviewImage = ref(null)
const previewImageRef = ref(null)
const previewContainerRef = ref(null)

// Tabs相关数据
const activeTab = ref('gallery')

// 套件分类相关数据
const suitePrimaryCategories = ref([])
const currentSuitePrimaryCategory = ref<string | number>('all')
const currentSuiteSecondaryCategories = ref([])
const currentSuiteSecondaryCategory = ref<string | number>('all')
const suiteSearchKeyword = ref('')

// 套件图片相关数据
const suiteImageList = ref([])
const suiteImageCurrentPage = ref(1)
const suiteImagePageSize = ref(20)
const suiteImageTotal = ref(0)
const suiteImageLoading = ref(false)

// 放大镜相关数据
const magnifierEnabled = ref(false) // 放大镜开关，默认关闭
const showMagnifier = ref(false)
const lensStyle = ref({})
const magnifierStyle = ref({})

// 分类数据
const primaryCategories = ref<LocalCategory[]>([])
const allCategories = ref<Category[]>([])
const currentSecondaryCategories = ref<LocalCategory[]>([])



// 获取当前一级分类名称
const getCurrentPrimaryCategoryName = () => {
  const category = primaryCategories.value.find(cat => cat.id === currentPrimaryCategory.value)
  return category ? category.name : ''
}



// 获取分类数据
const fetchCategories = async () => {
  try {
    const response = await getCategoryTree()
    if (response.success) {
      allCategories.value = response.data || []

      // 构建一级分类列表，添加"全部"选项
      const categories = [
        {
          id: 'all' as string | number,
          name: '全部',
          icon: 'IconFire',
          children: []
        },
        ...response.data.map((cat: Category) => ({
          ...cat
        }))
      ]

      primaryCategories.value = categories
    }
  } catch (error) {
    console.error('获取分类失败:', error)
    // 使用默认分类作为后备
    primaryCategories.value = [
      {
        id: 'all' as string | number,
        name: '全部',
        icon: 'IconFire',
        children: []
      }
    ]
  }
}

// 获取图片列表
const fetchImageList = async () => {
  loading.value = true
  try {
    const params = {
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      keyword: searchKeyword.value,
      categoryId: currentSecondaryCategory.value === 'all' ?
        (currentPrimaryCategory.value === 'all' ? '' : currentPrimaryCategory.value) :
        currentSecondaryCategory.value
    }

    const response = await request.get('/api/gallery/images/public', { params })

    if (response.success) {
      // 转换数据格式以适配前端显示
      const images = response.data.records.map((item: any) => ({
        id: item.id,
        title: item.title,
        thumbnail: item.thumbnail_path || item.file_path,
        url: item.file_path,
        width: item.width,
        height: item.height,
        format: item.format.toUpperCase(),
        description: item.description, // 网盘地址
        material_preparation: item.material_preparation, // 资料准备
        size: item.file_size,
        categories: item.categories || [],
        documents: item.documents || [],
        suites: item.suites || [],
        // 兼容旧的字段名
        primaryCategory: item.categories && item.categories.length > 0 ?
          item.categories.find((cat: any) => cat.parent_id === null)?.name : '',
        secondaryCategory: item.categories && item.categories.length > 0 ?
          item.categories.find((cat: any) => cat.parent_id !== null)?.name : ''
      }))

      imageList.value = images
      total.value = response.data.total
    } else {
      throw new Error(response.msg || '获取图片列表失败')
    }

  } catch (error) {
    console.error('获取图片列表失败:', error)
    Message.error('获取图片列表失败')

    // 清空图片列表，不显示模拟数据
    imageList.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 一级分类切换
const handlePrimaryCategoryChange = (categoryId: string) => {
  currentPrimaryCategory.value = categoryId
  currentSecondaryCategory.value = 'all'

  // 更新二级分类
  if (categoryId === 'all') {
    // 全部分类也显示"全部"二级分类
    currentSecondaryCategories.value = [{ id: 'all', name: '全部' }]
  } else {
    const category = primaryCategories.value.find(cat => cat.id === categoryId)
    if (category && category.children) {
      currentSecondaryCategories.value = [
        { id: 'all', name: '全部' },
        ...category.children
      ]
    } else {
      currentSecondaryCategories.value = [{ id: 'all', name: '全部' }]
    }
  }

  currentPage.value = 1
  fetchImageList()
}

// 二级分类切换
const handleSecondaryCategoryChange = (categoryId: string) => {
  currentSecondaryCategory.value = categoryId
  currentPage.value = 1
  fetchImageList()
}

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  fetchImageList()
}

// 分页处理
const handlePageChange = (page: number) => {
  currentPage.value = page
  fetchImageList()
}

const handlePageSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  fetchImageList()
}

// Tab切换处理
const handleTabChange = (key: string) => {
  activeTab.value = key
  if (key === 'suites') {
    fetchSuiteCategories()
    fetchSuiteImageList()
  }
}

// 获取套件分类列表
const fetchSuiteCategories = async () => {
  try {
    const response = await request.get('/api/suite-categories/tree')
    if (response.success) {
      suitePrimaryCategories.value = [
        { id: 'all', name: '全部', children: [] },
        ...response.data
      ]

      // 初始化时设置默认的二级分类（显示所有二级分类）
      if (currentSuitePrimaryCategory.value === 'all') {
        const allSecondaryCategories: any[] = []
        response.data.forEach((primary: any) => {
          if (primary.children && primary.children.length > 0) {
            allSecondaryCategories.push(...primary.children)
          }
        })
        currentSuiteSecondaryCategories.value = [
          { id: 'all', name: '全部' },
          ...allSecondaryCategories
        ]
      }
    }
  } catch (error) {
    console.error('获取套件分类失败:', error)
    Message.error('获取套件分类失败')
  }
}

// 获取套件图片列表
const fetchSuiteImageList = async () => {
  try {
    suiteImageLoading.value = true

    const params: any = {
      pageNum: suiteImageCurrentPage.value,
      pageSize: suiteImagePageSize.value
    }

    // 根据分类筛选
    if (currentSuiteSecondaryCategory.value && currentSuiteSecondaryCategory.value !== 'all') {
      params.suite_category_id = currentSuiteSecondaryCategory.value
    } else if (currentSuitePrimaryCategory.value && currentSuitePrimaryCategory.value !== 'all') {
      params.suite_primary_category_id = currentSuitePrimaryCategory.value
    } else {
      // 如果没有选择具体分类，显示所有有套件关联的图片
      params.has_suite = 1
    }

    // 搜索关键词
    if (suiteSearchKeyword.value) {
      params.keyword = suiteSearchKeyword.value
    }

    const response = await request.get('/api/gallery/images/public', { params })

    if (response.success) {
      suiteImageList.value = response.data.records.map((item: any) => ({
        id: item.id,
        title: item.title,
        thumbnail: item.thumbnail_path || item.file_path,
        url: item.file_path,
        width: item.width,
        height: item.height,
        format: item.format.toUpperCase(),
        description: item.description, // 网盘地址
        material_preparation: item.material_preparation, // 资料准备
        size: item.file_size,
        created_at: item.created_at,
        categories: item.categories || [],
        documents: item.documents || [],
        suites: item.suites || [],
        // 兼容旧的字段名
        primaryCategory: item.categories && item.categories.length > 0 ?
          item.categories.find((cat: any) => cat.parent_id === null)?.name : '',
        secondaryCategory: item.categories && item.categories.length > 0 ?
          item.categories.find((cat: any) => cat.parent_id !== null)?.name : ''
      }))
      suiteImageTotal.value = response.data.total
    } else {
      throw new Error(response.msg || '获取图片列表失败')
    }
  } catch (error) {
    console.error('获取套件图片列表失败:', error)
    Message.error('获取套件图片列表失败')
  } finally {
    suiteImageLoading.value = false
  }
}

// 套件图片分页处理
const handleSuiteImagePageChange = (page: number) => {
  suiteImageCurrentPage.value = page
  fetchSuiteImageList()
}

const handleSuiteImagePageSizeChange = (size: number) => {
  suiteImagePageSize.value = size
  suiteImageCurrentPage.value = 1
  fetchSuiteImageList()
}

// 套件分类处理
const handleSuitePrimaryCategoryChange = (categoryId: string | number) => {
  currentSuitePrimaryCategory.value = categoryId
  currentSuiteSecondaryCategory.value = 'all'

  if (categoryId === 'all') {
    // 显示所有二级分类
    const allSecondaryCategories: any[] = []
    suitePrimaryCategories.value.forEach((primary: any) => {
      if (primary.children && primary.children.length > 0) {
        allSecondaryCategories.push(...primary.children)
      }
    })
    currentSuiteSecondaryCategories.value = [
      { id: 'all', name: '全部' },
      ...allSecondaryCategories
    ]
  } else {
    const primaryCategory = suitePrimaryCategories.value.find((cat: any) => cat.id === categoryId)
    currentSuiteSecondaryCategories.value = primaryCategory ? [
      { id: 'all', name: '全部' },
      ...primaryCategory.children
    ] : []
  }

  suiteImageCurrentPage.value = 1
  fetchSuiteImageList()
}

const handleSuiteSecondaryCategoryChange = (categoryId: string | number) => {
  currentSuiteSecondaryCategory.value = categoryId
  suiteImageCurrentPage.value = 1
  fetchSuiteImageList()
}

// 套件搜索处理
const handleSuiteSearch = () => {
  suiteImageCurrentPage.value = 1
  fetchSuiteImageList()
}

// 套件点击处理
const handleSuiteClick = (suite: any) => {
  // 切换到图片展示栏并筛选该套件的图片
  activeTab.value = 'gallery'

  // 清除分类筛选
  currentPrimaryCategory.value = 'all'
  currentSecondaryCategory.value = 'all'
  currentSecondaryCategories.value = []

  // 设置搜索关键词为套件名称
  searchKeyword.value = suite.name
  currentPage.value = 1

  // 获取图片列表
  fetchImageList()
}

// 图片点击
const handleImageClick = (image: any) => {
  currentPreviewImage.value = image
  previewVisible.value = true
}

// 格式化文件大小
const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 格式化图片ID，补零到5位
const formatImageId = (id: number) => {
  return id.toString().padStart(5, '0')
}

// 格式化日期（只显示年月日）
const formatDate = (dateTime: string) => {
  if (!dateTime) return '-'
  const date = new Date(dateTime)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

// 获取文档类型颜色
const getDocumentTypeColor = (fileType: string) => {
  const colorMap: Record<string, string> = {
    'word': 'blue',
    'excel': 'green',
    'image': 'orange',
    'pdf': 'red'
  }
  return colorMap[fileType] || 'gray'
}

// 获取文档类型名称
const getDocumentTypeName = (fileType: string) => {
  const nameMap: Record<string, string> = {
    'word': 'Word',
    'excel': 'Excel',
    'image': '图片',
    'pdf': 'PDF'
  }
  return nameMap[fileType] || fileType
}

// 获取文件扩展名
const getFileExtension = (fileType: string) => {
  const extensionMap: Record<string, string> = {
    'word': 'docx',
    'excel': 'xlsx',
    'image': 'jpg',
    'pdf': 'pdf'
  }
  return extensionMap[fileType] || 'txt'
}

// 跳转到套件分类
const goToSuiteCategory = (suite: any) => {
  // 跳转到图片演示站，并筛选该套件的图片
  router.push({
    path: '/index',
    query: {
      suite: suite.name
    }
  })
  // 关闭弹窗
  previewVisible.value = false
}

// 下载文案
const downloadDocument = async (document: any) => {
  try {
    if (document.file_path || document.qiniu_key) {
      // 使用后端下载API，确保URL有效性和正确的文件名
      const downloadUrl = `http://localhost:3001/api/documents/${document.id}/download`
      console.log('下载URL:', downloadUrl)
      console.log('文档信息:', document)

      // 使用fetch下载，避免路由干扰
      try {
        const response = await fetch(downloadUrl, {
          method: 'GET',
          credentials: 'include'
        })

        if (response.ok) {
          // 获取文件名
          const contentDisposition = response.headers.get('Content-Disposition')
          let filename = `${document.title}.${getFileExtension(document.file_type)}`

          if (contentDisposition) {
            const filenameMatch = contentDisposition.match(/filename\*?=['"]?([^'";]+)['"]?/)
            if (filenameMatch) {
              filename = decodeURIComponent(filenameMatch[1])
            }
          }

          // 创建blob并下载
          const blob = await response.blob()
          const url = window.URL.createObjectURL(blob)
          const link = document.createElement('a')
          link.href = url
          link.download = filename
          link.style.display = 'none'
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)
          window.URL.revokeObjectURL(url)

          Message.success('文件下载成功')
        } else {
          throw new Error(`下载失败: ${response.status}`)
        }
      } catch (fetchError) {
        console.error('Fetch下载失败，尝试直接链接:', fetchError)
        // 如果fetch失败，回退到直接链接方式
        window.location.href = downloadUrl
        Message.success('开始下载文件')
      }
    } else if (document.remark) {
      // 如果有网盘地址，直接打开
      window.open(document.remark, '_blank')
      Message.info('已打开网盘地址')
    } else {
      Message.warning('暂无下载地址')
    }
  } catch (error) {
    console.error('下载失败:', error)
    Message.error('下载失败，请稍后重试')
  }
}



// 处理图片加载，根据宽度设置显示样式
const handleImageLoad = (event: Event) => {
  const img = event.target as HTMLImageElement
  if (img) {
    const naturalWidth = img.naturalWidth

    // 根据图片宽度设置样式
    if (naturalWidth >= 600) {
      // 宽度大于等于600px的，按550px等比例缩放
      img.style.width = '550px'
      img.style.height = 'auto'
    } else {
      // 宽度小于600px的，按原始宽度显示
      img.style.width = `${naturalWidth}px`
      img.style.height = 'auto'
    }
  }
}

// 处理鼠标移动，实现放大镜效果
const handleMouseMove = (event: MouseEvent) => {
  if (!previewImageRef.value || !magnifierEnabled.value || !showMagnifier.value || !previewContainerRef.value) return

  const img = previewImageRef.value as HTMLImageElement
  const container = event.currentTarget as HTMLElement
  const previewContainer = previewContainerRef.value as HTMLElement
  const rect = container.getBoundingClientRect()
  const previewRect = previewContainer.getBoundingClientRect()

  // 计算鼠标在容器中的相对位置
  const x = event.clientX - rect.left
  const y = event.clientY - rect.top

  // 放大镜镜头大小
  const lensSize = 200
  const displaySize = 400 // 放大显示区域大小

  // 计算镜头位置（确保不超出边界）
  const lensX = Math.max(0, Math.min(x - lensSize / 2, rect.width - lensSize))
  const lensY = Math.max(0, Math.min(y - lensSize / 2, rect.height - lensSize))

  // 设置镜头样式
  lensStyle.value = {
    left: `${lensX}px`,
    top: `${lensY}px`,
    width: `${lensSize}px`,
    height: `${lensSize}px`
  }

  // 计算在原图中的位置比例
  const imgRect = img.getBoundingClientRect()
  const imgX = event.clientX - imgRect.left
  const imgY = event.clientY - imgRect.top

  // 计算原图中对应的位置
  const scaleX = img.naturalWidth / img.offsetWidth
  const scaleY = img.naturalHeight / img.offsetHeight

  const originalX = imgX * scaleX
  const originalY = imgY * scaleY

  // 如果原图比显示区域小，则按原图尺寸显示
  const finalWidth = Math.min(img.naturalWidth, displaySize)
  const finalHeight = Math.min(img.naturalHeight, displaySize)

  // 设置放大镜显示区域的背景位置
  const bgX = -(originalX - finalWidth / 2)
  const bgY = -(originalY - finalHeight / 2)

  // 以弹窗为定位锚点，计算放大镜显示区域的位置
  const displayLeft = previewRect.width * 0.5 + 20 // 弹窗中间位置 + 20px 间距
  const displayTop = 20 // 距离弹窗顶部 20px

  magnifierStyle.value = {
    backgroundImage: `url(${currentPreviewImage.value?.url})`,
    backgroundSize: `${img.naturalWidth}px ${img.naturalHeight}px`,
    backgroundPosition: `${bgX}px ${bgY}px`,
    backgroundRepeat: 'no-repeat',
    width: `${displaySize}px`,
    height: `${displaySize}px`,
    left: `${displayLeft}px`,
    top: `${displayTop}px`
  }
}

// 处理鼠标进入
const handleMouseEnter = () => {
  if (magnifierEnabled.value) {
    showMagnifier.value = true
  }
}

// 处理鼠标离开
const handleMouseLeave = () => {
  showMagnifier.value = false
}

// 页面加载时获取数据
onMounted(async () => {
  // 先获取分类数据
  await fetchCategories()

  // 获取套件分类数据
  await fetchSuiteCategories()

  // 处理URL参数
  const categoryId = route.query.categoryId as string
  const suiteName = route.query.suite as string

  if (suiteName) {
    // 如果有套件参数，设置搜索关键词为套件名称
    searchKeyword.value = suiteName
  } else if (categoryId) {
    // 查找目标分类
    let targetCategory = null
    let parentCategory = null

    for (const category of allCategories.value) {
      if (category.id.toString() === categoryId) {
        targetCategory = category
        break
      }
      if (category.children) {
        const child = category.children.find((child: any) => child.id.toString() === categoryId)
        if (child) {
          targetCategory = child
          parentCategory = category
          break
        }
      }
    }

    if (targetCategory) {
      if (parentCategory) {
        // 是二级分类
        currentPrimaryCategory.value = parentCategory.id
        currentSecondaryCategory.value = targetCategory.id
        // 设置二级分类列表
        currentSecondaryCategories.value = [
          { id: 'all', name: '全部' },
          ...parentCategory.children
        ]
      } else {
        // 是一级分类
        currentPrimaryCategory.value = targetCategory.id
        currentSecondaryCategory.value = 'all'
        // 设置二级分类列表
        if (targetCategory.children) {
          currentSecondaryCategories.value = [
            { id: 'all', name: '全部' },
            ...targetCategory.children
          ]
        } else {
          currentSecondaryCategories.value = [{ id: 'all', name: '全部' }]
        }
      }
    }
  } else {
    // 初始化二级分类（全部分类也显示"全部"二级分类）
    currentSecondaryCategories.value = [{ id: 'all', name: '全部' }]
  }

  // 获取图片列表
  fetchImageList()

  // 如果当前tab是套件展示，也获取套件图片列表
  if (activeTab.value === 'suites') {
    fetchSuiteImageList()
  }
})
</script>

<style scoped>
.index-container {
  min-height: calc(100vh - 64px);
  background: #f7f8fa;
}

/* Tabs样式调整 */
:deep(.arco-tabs) {
  margin-bottom: 0;
}

:deep(.arco-tabs-content) {
  padding-top: 0;
}

:deep(.arco-tabs-tab-pane) {
  padding-top: 0;
}

.category-filter {
  background: white;
  border-bottom: 1px solid #e5e6eb;
  padding: 12px 0;
  margin-top: -8px; /* 减少与tabs的间距 */
}

.filter-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

.filter-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 12px;
}

.primary-categories {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
  margin-bottom: 8px;
  line-height: 1.4;
}

.secondary-categories {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
  margin-bottom: 16px;
  line-height: 1.5;
}

.category-label {
  font-size: 12px;
  color: #86909c;
  font-weight: 500;
  white-space: nowrap;
}

.category-item {
  font-size: 12px;
  color: #4e5969;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.category-item:hover {
  color: #165dff;
  background-color: #f2f3ff;
}

.category-item.active {
  color: #165dff;
  background-color: #e8f3ff;
  font-weight: 500;
}

.gallery-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 16px 24px 24px 24px; /* 减少上边距 */
}

.gallery-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
}

.gallery-item {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
}

.gallery-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.image-wrapper {
  position: relative;
  width: 100%;
  height: 160px;
  overflow: hidden;
}

.image-wrapper img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.gallery-item:hover .image-wrapper img {
  transform: scale(1.1);
}

.image-info {
  padding: 12px;
}

.image-title-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.image-title {
  margin: 0;
  font-size: 12px;
  font-weight: 500;
  color: #1d2129;
  line-height: 1.4;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.image-id {
  font-size: 10px;
  font-weight: 600;
  color: #86909c;
  background: #f2f3ff;
  padding: 1px 6px;
  border-radius: 10px;
  margin-left: 6px;
  flex-shrink: 0;
}

.image-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 10px;
  color: #86909c;
}

.image-size {
  color: #86909c;
}

.netdisk-text {
  color: #86909c;
  font-size: 10px;
  word-break: break-all;
  max-width: 150px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.no-netdisk {
  color: #c9cdd4;
  font-style: italic;
  font-size: 10px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .gallery-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 768px) {
  .gallery-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }

  .gallery-container {
    padding: 16px;
  }

  .image-wrapper {
    height: 140px;
  }
}

@media (max-width: 480px) {
  .gallery-grid {
    grid-template-columns: 1fr;
  }

  .image-wrapper {
    height: 200px;
  }
}

.loading-container,
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.loading-container p {
  margin-top: 16px;
  color: #86909c;
}

.pagination-container {
  display: flex;
  justify-content: center;
  padding: 32px 24px;
}

/* 套件栏样式 */
.suites-container {
  padding: 16px 24px 24px 24px; /* 减少上边距 */
}

.suites-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.suite-item {
  cursor: pointer;
  transition: transform 0.2s ease;
}

.suite-item:hover {
  transform: translateY(-4px);
}

.suite-card {
  background: #fff;
  border: 1px solid #e5e6eb;
  border-radius: 8px;
  padding: 24px;
  height: 100%;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

.suite-card:hover {
  border-color: #165dff;
  box-shadow: 0 4px 16px rgba(22, 93, 255, 0.15);
}

.suite-info {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.suite-name {
  font-size: 18px;
  font-weight: 600;
  color: #1d2129;
  margin: 0 0 12px 0;
  line-height: 1.4;
}

.suite-description {
  color: #4e5969;
  font-size: 14px;
  line-height: 1.6;
  margin: 0 0 16px 0;
  flex: 1;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.suite-stats {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: auto;
}

.image-count {
  color: #86909c;
  font-size: 12px;
  background: #f2f3f5;
  padding: 4px 8px;
  border-radius: 4px;
  align-self: flex-start;
}

.category-info {
  color: #165dff;
  font-size: 12px;
  background: #e8f3ff;
  padding: 4px 8px;
  border-radius: 4px;
  align-self: flex-start;
}

/* 套件栏响应式 */
@media (max-width: 768px) {
  .suites-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 16px;
  }

  .suite-card {
    padding: 16px;
  }

  .suite-name {
    font-size: 16px;
  }
}

@media (max-width: 480px) {
  .suites-grid {
    grid-template-columns: 1fr;
  }

  .suites-container {
    padding: 16px;
  }
}

/* 预览弹窗样式 */
.preview-container {
  padding: 16px 0;
  position: relative;
}

.preview-image-container {
  height: 450px;
  padding: 0 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.image-scroll-wrapper {
  width: 100%;
  height: 100%;
  overflow: auto;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  background: #f8f9fa;
  padding: 20px;
  display: flex;
  align-items: flex-start;
  justify-content: center;
}

.magnifier-container {
  position: relative;
  display: inline-block;
  cursor: crosshair;
}

.preview-image {
  display: block;
  border-radius: 4px;
  max-width: none;
  max-height: none;
  /* 宽度和高度将通过JavaScript动态设置 */
}

.magnifier-lens {
  position: absolute;
  border: 2px solid #fff;
  border-radius: 50%;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
  pointer-events: none;
  z-index: 10;
}

.magnifier-display {
  position: absolute;
  width: 400px;
  height: 400px;
  border: 3px solid #fff;
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
  z-index: 1000;
  background-color: #fff;
  pointer-events: none;
  /* left 和 top 将通过 JavaScript 动态设置 */
}

.info-section {
  height: 100%;
}



.info-section {
  height: 100%;
}

.info-section h4 {
  margin: 0 0 8px 0;
  color: #1d2129;
  font-size: 14px;
  font-weight: 600;
  border-bottom: 1px solid #e5e6eb;
  padding-bottom: 4px;
}

.no-data {
  color: #86909c;
  font-style: italic;
  font-size: 12px;
}

.material-preparation {
  font-size: 12px;
  line-height: 1.4;
}



.document-link:hover {
  text-decoration: underline;
  color: #0e42d2;
}
</style>
