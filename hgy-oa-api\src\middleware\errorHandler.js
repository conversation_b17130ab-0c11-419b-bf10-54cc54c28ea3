// 全局错误处理中间件
export const errorHandler = (err, req, res, next) => {
  console.error('错误详情:', err);

  // 数据库错误
  if (err.code === 'ER_ACCESS_DENIED_ERROR') {
    return res.error('数据库访问被拒绝', 500);
  }
  
  if (err.code === 'ER_BAD_DB_ERROR') {
    return res.error('数据库不存在', 500);
  }
  
  if (err.code === 'ECONNREFUSED') {
    return res.error('数据库连接被拒绝', 500);
  }

  // 文件上传错误
  if (err.code === 'LIMIT_FILE_SIZE') {
    return res.error('文件大小超出限制', 400);
  }
  
  if (err.code === 'LIMIT_FILE_COUNT') {
    return res.error('文件数量超出限制', 400);
  }
  
  if (err.code === 'LIMIT_UNEXPECTED_FILE') {
    return res.error('不支持的文件类型', 400);
  }

  // JSON解析错误
  if (err instanceof SyntaxError && err.status === 400 && 'body' in err) {
    return res.error('请求数据格式错误', 400);
  }

  // 默认错误
  const statusCode = err.statusCode || err.status || 500;
  const message = err.message || '服务器内部错误';
  
  res.error(message, statusCode);
};
